<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="内部编码" prop="innerCode">
        <el-input v-model="queryParams.innerCode" placeholder="请输入内部编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机器人名称" prop="robotName">
        <el-input v-model="queryParams.robotName" placeholder="请输入机器人名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="机器人key" prop="robotKey">
        <el-input v-model="queryParams.robotKey" placeholder="请输入机器人key" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_new_table">
    <el-table size="mini" v-loading="loading" :data="tableData" border @selection-change="handleSelectionChange" align="center">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column prop="innerCode" label="内部编码" fixed="left" align="center"></el-table-column>
      <el-table-column prop="robotName" label="机器人名称" align="center" min-width="180"></el-table-column>
      <el-table-column prop="robotKey" label="机器人key" min-width="180" :show-overflow-tooltip="true" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.robotKey" raw-content placement="top-start"
                      v-if="scope.row.robotKey">
            <span v-if="scope.row.robotKey && scope.row.robotKey.length <= 15">
              {{ scope.row.robotKey }}
            </span>
            <span v-if="scope.row.robotKey && scope.row.robotKey.length > 15">
<!--              {{ scope.row.robotKey.substr(0, 15) + '...' }}-->
              {{ scope.row.robotKey }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.robotKey == null"> </span>
        </template>
      </el-table-column>
      <el-table-column prop="describes" label="描述" align="center" :show-overflow-tooltip="true"   min-width="180">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.describes" raw-content placement="top-start"
                      v-if="scope.row.describes">
            <span v-if="scope.row.describes && scope.row.describes.length <= 15">
              {{ scope.row.describes }}
            </span>
            <span v-if="scope.row.describes && scope.row.describes.length > 15">
<!--              {{ scope.row.describes.substr(0, 15) + '...' }}-->
              {{ scope.row.describes }}
            </span>
          </el-tooltip>
          <span v-else-if="scope.row.describes == null"> </span>
        </template>
      </el-table-column>
      <el-table-column prop="createBy" label="创建者" align="center"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" align="center" min-width="180"></el-table-column>
      <el-table-column prop="updateBy" label="更新者" align="center"></el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center" min-width="180"></el-table-column>
    </el-table>
    </div>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改企微预警推送对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="内部编码" prop="innerCode">
          <el-input v-model="form.innerCode" placeholder="请输入内部编码" :disabled="disabledFlag" />
        </el-form-item>
        <el-form-item label="机器人key" prop="robotKey">
          <el-input v-model="form.robotKey" placeholder="请输入机器人key" />
        </el-form-item>
        <el-form-item label="机器人名称" prop="robotName">
          <el-input v-model="form.robotName" placeholder="请输入机器人名称" />
        </el-form-item>
        <el-form-item label="描述" prop="describes">
          <el-input v-model="form.describes" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPush,
  getPush,
  delPush,
  saveOrUpdate
} from "@/api/warn/push/index";

export default {
  name: "Push",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企微预警推送表格数据
      pushList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        innerCode: null,
        robotName: null,
        robotKey: null,
        describes: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        wechatUrl: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        innerCode: [
          {
            required: true, message: "内部编码不能为空", trigger: "blur"
          }
        ],
        robotKey: [
          {
            required: true, message: "机器人key不能为空", trigger: "blur"
          }
        ],
      },


      tableData: [],
      selectVO: '',
      // 显示 添加或修改
      submitFlag: '',
      disabledFlag: false,
    };
  },
  created() {
    this.getList(null);
  },
  methods: {
    /** pageNum事件 */
    numChange(pageNum, selectVO) {
      this.queryParams.pageNum = pageNum;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.queryParams.pageSize = pageSize;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** 查询企微预警推送列表 */
    getList(selectVO) {
      this.loading = true;
      if (selectVO) {
        this.selectVO = selectVO;
      }
      this.queryList();
    },
    queryList() {
      listPush(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.rows
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        warnId: null,
        innerCode: null,
        robotName: null,
        robotKey: null,
        describes: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        wechatUrl: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.warnId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "企业微信机器人编辑";
      this.submitFlag = 'add';
      this.disabledFlag = false;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const warnId = row.warnId || this.ids
      getPush(warnId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "企业微信机器人编辑";
        this.submitFlag = 'update';
        this.disabledFlag = true;
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          saveOrUpdate(this.form).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const warnId = row.warnId || this.ids
      this.$modal.confirm('确定删除选中的机器人吗').then(function () {
        return delPush(warnId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('api/lingxiao/push/export', {
        ...this.queryParams
      }, `push_${new Date().getTime()}.xlsx`)
    }
  }
}
  ;
</script>
