<template>
  <div class="app-container" style="padding: 10px;">

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_new_table">
      <dlTable refName="dlTable" :stripe="true" :border="true" :height="dltableHeight" :columns="columns"
        :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig" @handleOrder="getList"
        @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
        @page-current-change="numChange">
      </dlTable>
    </div>

    <!-- 添加或修改存储配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="90px">
        <el-form-item label="存储编号" prop="storeCode">
          <el-input v-model="form.storeCode" placeholder="请输入存储编号" />
        </el-form-item>
        <el-form-item label="存储名称" prop="storeName">
          <el-input v-model="form.storeName" placeholder="请输入存储名称" />
        </el-form-item>
        <el-form-item label="时序URL" prop="influxUrl">
          <el-input v-model="form.influxUrl" placeholder="请输入时序URL" />
        </el-form-item>
        <el-form-item label="时序组织" prop="influxOrgid">
          <el-input v-model="form.influxOrgid" placeholder="请输入时序组织" />
        </el-form-item>
        <el-form-item label="时序token" prop="influxToken">
          <el-input v-model="form.influxToken" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listStore,
  getStore,
  delStore,
  saveOrUpdate,
} from "@/api/collect/store";

export default {
  name: "CollectStore",
  data() {
    return {
      dltableHeight: 660,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 存储配置表格数据
      storeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        storeCode: [
          {
            required: true, message: "存储编码不能为空", trigger: "blur"
          }
        ],
        storeName: [
          {
            required: true, message: "存储名称不能为空", trigger: "blur"
          }
        ],
        influxUrl: [
          {
            required: true, message: "时序URL不能为空", trigger: "blur"
          }
        ],
        influxToken: [
          {
            required: true, message: "时序token不能为空", trigger: "blur"
          }
        ],
        influxOrgid: [
          {
            required: true, message: "时序组织不能为空", trigger: "blur"
          }
        ],
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: false // 表头排序是否保存在localStorage中
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      columns: [
        {
          label: 'ID', // 表头描述
          fieldIndex: 'storeId', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STORE_ID',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '存储编号', // 表头描述
          fieldIndex: 'storeCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STORE_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '存储名称', // 表头描述
          fieldIndex: 'storeName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STORE_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '时序URL', // 表头描述
          fieldIndex: 'influxUrl', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'INFLUX_URL',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '时序token', // 表头描述
          fieldIndex: 'influxToken', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'INFLUX_TOKEN',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '时序组织', // 表头描述
          fieldIndex: 'influxOrgid', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'INFLUX_ORGID',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'REMARK',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建人', // 表头描述
          fieldIndex: 'createdBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建时间', // 表头描述
          fieldIndex: 'createdTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新人', // 表头描述
          fieldIndex: 'updatedBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新时间', // 表头描述
          fieldIndex: 'updatedTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        }

      ],
      tableData: [],
      selectVO: '',
    };
  },
  created() {
    this.getList(null);
  },
  methods: {
    /** pageNum事件 */
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum;
      this.queryParams.pageNum = pageNum;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize;
      this.queryParams.pageSize = pageSize;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** 查询存储配置列表 */
    getList(selectVO) {
      this.loading = true;
      if (selectVO) {
        this.selectVO = selectVO;
      }
      this.queryList();
    },
    queryList() {
      listStore(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.rows
        this.pageConfig.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        storeId: null,
        storeCode: null,
        storeName: null,
        influxUrl: null,
        influxToken: null,
        influxOrgid: null,
        remark: null,
        createdBy: null,
        createdTime: null,
        updatedBy: null,
        updatedTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.storeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加存储配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const storeId = row.storeId || this.ids
      getStore(storeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改存储配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form))

          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess('保存成功')
            this.open = false
            this.getList()
          })
        }
      })

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const storeIds = row.storeId || this.ids
      this.$modal
        .confirm(
          '确定删除吗?'
        )
        .then(function () {
          return delStore(storeIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/api/collect/store/export', {
        ...this.queryParams
      }, `存储配置_${new Date().getTime()}.xlsx`)
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$nextTick(() => {
        /*mes_new_table 到顶部的高炉 */
        let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
        /*mes_new_table 屏幕高度-mes_new_table到顶部的高炉-50（分页控件） */
        this.dltableHeight = document.body.clientHeight - topValue - 50;
      })
    })
  },

}
  ;
</script>
