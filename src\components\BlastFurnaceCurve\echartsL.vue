<template>
  <div class="echartsL_box">
    <div class="title-bar">
      <div class="title-text">{{ title || '-' }}</div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉' || activeGL === '一烧结'}" @click="chagngeGl('1')">一{{ type }}</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉' || activeGL === '二烧结'}" @click="chagngeGl('2')">二{{ type }}</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsL" class="echartsL"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenMaterial } from "@/api/analyse/blastfurnace";

export default {
  name: '<PERSON>hartsL',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  components: {
  },
  directives: {},
  data() {
    return {
      activeGL: this.type == '高炉' ? '一高炉' : '一烧结',
      chartL: null,
      param: {
        prodCenterCode: '1'
      },
      timer: null
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chartL) {
          this.chartL.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chartL) {
      this.chartL.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
    clearInterval(this.timer);
  },
  methods: {
    initValue() {
      if (this.type == '高炉') {
        ironScreenMaterial(this.param).then(res => {
          if (res.code === 200) {
            var data = res.data;
            const result = Object.keys(data).map(date => ({
              date: date,
              ...data[date]
            }));
            var xDataCokeWeight = [];
            var xDataOreWeight = [];
            var xDataCokeBreezeWeight = [];
            var xDataCoalWeight = [];
            var xDataOtherWeight = [];
            var yData = [];
            result.forEach((item, index) => {
              xDataCokeWeight[index] = item.cokeWeight || 0;
              xDataOreWeight[index] = item.oreWeight || 0;
              xDataCokeBreezeWeight[index] = item.cokeBreezeWeight || 0;
              xDataCoalWeight[index] = item.coalWeight || 0;
              xDataOtherWeight[index] = item.otherWeight || 0;
              yData[index] = item.workDate || '0';
            });
            this.$nextTick(() => {
              this.initChart(xDataCokeWeight, xDataOreWeight, xDataCokeBreezeWeight, xDataCoalWeight, xDataOtherWeight, yData);
              window.addEventListener('resize', this.resizeChart);
            })
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        }).catch((error) => {
          console.error("获取数据出错:", error);
        });
      } else {
        this.$nextTick(() => {
          this.initChart([], [], [], [], []);
          window.addEventListener('resize', this.resizeChart);
        })
      }
    },
    initChart(xDataCokeWeight, xDataOreWeight, xDataCokeBreezeWeight, xDataCoalWeight, xDataOtherWeight, yData) {
      let that = this;
      let len = 0;
      const chartDom = this.$refs.echartsL;
      this.chartL = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: xDataCokeWeight.length > 0 && xDataOreWeight.length > 0 && xDataCokeBreezeWeight.length > 0 && xDataCoalWeight.length > 0 && xDataOtherWeight.length > 0 && yData.length > 0
          }]
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getFullYear()} - ${date.getMonth() + 1}-${date.getDate()}</div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        legend: {
          type: 'plain',
          icon: 'circle',
          left: '15%',
          top: '0',
          itemHeight: 12,
          itemWidth: 12,
          itemGap: 50,
          textStyle: {
            fontSize: 12
          },
          data: ['焦', '矿', '焦丁', '煤', '其它']
        },
        grid: {
          top: '15%',
          left: '1.5%',
          right: '1.5%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgb(244,245,248)',
              width: 5
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#4F5B6A',
              fontSize: 12,
              fontWeight: 500
            },
            formatter: function (value) {
              return value.split('-').slice(1).join('-');
            },
          },
          axisTick: {
            show: false
          },
          data: yData
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#7E8996',
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F0F2F6',
              type: 'dashed',
              width: 2
            }
          },
        },
        series: [
          {
            name: '焦',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#249EFF'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                // (x1,y1) 点到点 (x2,y2) 之间进行渐变
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  }, // 0 起始颜色
                  {
                    offset: 1,
                    color: '#8ec9f8'
                  } // 1 结束颜色
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataCokeWeight
          },
          {
            name: '矿',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#846BCE'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: '#bbade6'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataOreWeight
          },
          {
            name: '焦丁',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#21CCFF'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: '#75dbfa'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataCokeBreezeWeight
          },
          {
            name: '煤',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: 'rgb(14, 16, 210)'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: '#5657dd'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataCoalWeight
          },
          {
            name: '其它',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#86DF6C'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: '#cbf6be'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataOtherWeight
          }
        ]
      };
      this.chartL.setOption(option);
   /*    this.timer = setInterval(() => {
        if (len === xDataCokeWeight.length) {
          len = 0;
        }
        this.chartL.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          dataIndex: len,
        });
        len++;
      }, 3000); */
    },
    resizeChart() {
      if (this.chartL) {
        this.chartL.resize();
      }
    },
    chagngeGl(name) {
      if (name == '1') {
        if (this.type == '高炉') {
          this.activeGL = '一高炉';
          this.param.prodCenterCode = name;
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '一烧结';
          this.param.prodCenterCode = name;
          this.initValue();
        }
        clearInterval(this.timer);
      }
      if (name == '2') {
        if (this.type == '高炉') {
          this.activeGL = '二高炉';
          this.param.prodCenterCode = name;
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '二烧结';
          this.param.prodCenterCode = name;
          this.initValue();
        }
        clearInterval(this.timer);
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsL_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 10%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-text {
  width: 230px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 18px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsL {
  width: 100%;
  height: 100%;
}
</style>