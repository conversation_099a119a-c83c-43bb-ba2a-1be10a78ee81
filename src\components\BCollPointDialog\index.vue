<template>
  <el-dialog title="选择点位" :visible.sync="isOpenDialog" width="960px" append-to-body @close="cancle">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">

      <el-form-item label="点位工序" prop="pointProcess">
        <el-select v-model="queryParams.pointProcess" placeholder="请输入点位业务" allow-create clearable filterable
                   @keyup.enter.native="handleQuery"
        >
          <el-option v-for="item in processList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位业务" prop="pointOperation">
        <el-select v-model="queryParams.pointOperation" placeholder="请输入点位工序" allow-create clearable filterable
                   @keyup.enter.native="handleQuery"
        >
          <el-option v-for="item in operList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位编号" prop="pointCode">
        <el-input v-model="queryParams.pointCode" placeholder="请输入点位编号" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点位名称" prop="pointName">
        <el-input v-model="queryParams.pointName" placeholder="请输入点位名称" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="点位描述" prop="pointDesc">
        <el-input v-model="queryParams.pointDesc" placeholder="请输入点位名称" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>
    <dlTable refName="dlTable" :stripe="true" :border="true" :height="500" :columns="columns"
             :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig" @handleOrder="getList"
             @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
             @page-current-change="numChange"
    >
    </dlTable>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancle">取 消</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  listPoint,
  getProcess,
  getOperList,
  selectByIds
} from '@/api/collect/point'

export default {
  name: 'BCollPointDialog',
  props: {
    isOpen: {
      default: false
    }
  },
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        pointCode: null,
        pointName: null,
        pointProcess: null,
        pointOperation: null
      },
      processList: [],
      operList: [],
      isOpenDialog: false,
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: false // 表头排序是否保存在localStorage中
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      columns: [
        {
          label: '存储编号', // 表头描述
          fieldIndex: 'storeCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STORE_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位工序', // 表头描述
          fieldIndex: 'pointProcess', // 表格显示内容绑定值
          width: 80,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_PROCESS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true// 是否显示筛选icon 和排序 icon
        },
        {
          label: '业务点位', // 表头描述
          fieldIndex: 'pointOperation', // 表格显示内容绑定值
          width: 150,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_OPERATION',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true// 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位编号', // 表头描述
          fieldIndex: 'pointCode', // 表格显示内容绑定值
          width: 150,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true// 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位名称', // 表头描述
          fieldIndex: 'pointName', // 表格显示内容绑定值
          width: 150,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true// 是否显示筛选icon 和排序 icon
        },
        {
          label: '状态', // 表头描述
          fieldIndex: 'status', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STATUS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'REMARK',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位描述', // 表头描述
          fieldIndex: 'pointDesc', // 表格显示内容绑定值
          width: 300,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_DESC',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon'
        },
        {
          label: '创建人', // 表头描述
          fieldIndex: 'createdBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建时间', // 表头描述
          fieldIndex: 'createdTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新人', // 表头描述
          fieldIndex: 'updatedBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新时间', // 表头描述
          fieldIndex: 'updatedTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        }

      ],
      tableData: [],
      ids: []
    }
  },
  watch: {
    isOpen: {
      handler(val) {
        this.isOpenDialog = val
        this.ids=null
        this.reset()
        this.getList(null)
      },
      immediate: true,
      deep: true
    }
  },
  beforeCreate() {
    // console.log('beforeCreate')
  },
  created() {
    // console.log('created')
    this.reset()
    getProcess().then(response => {
      this.processList = response.data
    })
    getOperList().then(response => {
      this.operList = response.data
    })
    this.getList(null)
  },
  beforeUpdate() {
    // console.log('beforeUpdate')
  },
  updated() {
    // console.log('updated')
  },
  beforemount() {
    // console.log('beforemount')
  },
  mounted() {
    // console.log('mounted')
  },
  beforeDestroy() {
    // console.log('beforeDestroy')
  },
  destroyed() {
    // console.log('destroyed')
  },
  methods: {
    reset() {
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        pointCode: null,
        pointName: null,
        pointProcess: null,
        pointOperation: null
      }
    },
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum
      this.queryParams.pageNum = pageNum
      this.selectVO = selectVO
      this.getList(selectVO)
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize
      this.queryParams.pageSize = pageSize
      this.selectVO = selectVO
      this.getList(selectVO)
    },
    handleQuery() {
      this.getList(null)
    },
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pointId)
    },
    /** 查询点位配置列表 */
    getList(selectVO) {
      listPoint(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.rows
        this.pageConfig.total = response.total
      })
    },
    submitForm() {
      if (this.ids.length === 0) {
        this.$modal.msgError('保存成功')
        return
      }
      selectByIds(this.ids).then(response => {
        this.$emit('submit', response.data)
        this.$emit('close')
      })
    },
    cancle() {
      this.$emit('close')
    }
  },
}
</script>
