import request from '@/utils/request'

// 查询操作日志记录列表
export function listWindSelect(query) {
  return request({
    url: '/api/windVolume/listWindSelect',
    method: 'get',
    params: query
  })
}


// 高炉风量数据 新增
export function windAdd(data) {
  return request({
    url: '/api/windVolume/windAdd',
    method: 'post',
    data: data
  })
}

//   高炉风量 保存数据
export function windUpdateData(data) {
  return request({
    url: '/api/windVolume/windUpdateData',
    method: 'put',
    data: data
  })
}
//   高炉风量 保存数据
export function windDeleteData(data) {
  return request({
    url: '/api/windVolume/windDeleteData',
    method: 'delete',
    data: data
  })
}




