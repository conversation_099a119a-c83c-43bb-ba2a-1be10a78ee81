<template>
  <div ref="barChart" style="width: 600px; height: 400px;"></div>
</template>

<script>
  import * as echarts from 'echarts';

  export default {
    name: '<PERSON><PERSON><PERSON>',
    props: {
      BarChartData: {
        type: Object,
        default: () => {}
      }
    },
    mounted() {
      this.initChart();
    },
    methods: {
      initChart() {
        const chart = echarts.init(this.$refs.barChart);
        const option = {
          xAxis: {
            type: 'category',
            data: this.BarChartData.xData || []
          },
          yAxis: {
            type: 'value'
          },
          series: [{
            data: this.BarChartData.yData || [],
            type: 'bar'
          }]
        };
        chart.setOption(option);
      }
    },
    watch: {
      BarChartData: {
        deep: true,
        handler(newVal) {
          this.$nextTick(() => {
            this.initChart(); // 重新初始化图表以应用新的数据
          });
        }
      }
    }
  };
</script>
