import request from '@/utils/request'

// 查询仓储-加工单列表
export function listOrder(query, selectVO) {
    return request({
        url: '/api/wms/processingOrder/list',
        method: 'get',
        params: query,
        selectVO: selectVO
    })
}

export function listManger(query, selectVO) {
    return request({
        url: '/api/wms/processingOrder/listManger',
        method: 'get',
        params: query,
        selectVO: selectVO
    })
}
export function queryRaw(processingOrderId) {
    return request({
        url: '/api/wms/processingOrder/queryRaw/' + processingOrderId,
        method: 'get'
    })
}
export function queryFinish(processingOrderId) {
    return request({
        url: '/api/wms/processingOrder/queryFinish/' + processingOrderId,
        method: 'get'
    })
}
export function cancleOrder(processingOrderId) {
    return request({
        url: '/api/wms/processingOrder/cancleOrder/' + processingOrderId,
        method: 'get'
    })
}
export function getCrossRegion(storehouseCode) {
    return request({
        url: '/api/wms/processingOrder/getCrossRegion/' + storehouseCode,
        method: 'get'
    })
}
export function getPostion(storehouseCode, crossRegion) {
    return request({
        url: '/api/wms/processingOrder/getPostion/' + storehouseCode + '/' + crossRegion,
        method: 'get'
    })
}
export function confrimOrder(data) {
    return request({
        url: '/api/wms/processingOrder/confrimOrder',
        method: 'post',
        data: data
    })
}
// 查询仓储-加工单详细
export function getOrder(processingOrderId) {
    return request({
        url: '/api/wms/processingOrder/' + processingOrderId,
        method: 'get'
    })
}

// 新增仓储-加工单
export function addOrder(data) {
    return request({
        url: '/api/wms/processingOrder',
        method: 'post',
        data: data
    })
}

// 修改仓储-加工单
export function updateOrder(data) {
    return request({
        url: '/api/wms/processingOrder',
        method: 'put',
        data: data
    })
}

// 删除仓储-加工单
export function delOrder(processingOrderId) {
    return request({
        url: '/api/wms/processingOrder/' + processingOrderId,
        method: 'delete'
    })
}
