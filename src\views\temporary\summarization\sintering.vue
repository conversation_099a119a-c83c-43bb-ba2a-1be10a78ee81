<template>
  <div class="sum-container">
    <div class="sum-content-up">
      <div class="sum-content-up01">
        <div class="sum-content-up01-left">
          <ECharts4 :title="title4" :type="type4"></ECharts4>
        </div>
        <div class="sum-content-up01-right">
          <div class="right-son01">
            <EChartsBH></EChartsBH>
          </div>
          <div class="right-son02">
            <EChartsH3></EChartsH3>
          </div>
        </div>
      </div>
      <div class="sum-content-up02">
        <div class="sum-content-up02-left">
          <EChartsLs></EChartsLs>
        </div>
      </div>
    </div>
    <div class="sum-content-down">
      <EChartsBars :title="titleL" :type="type4"></EChartsBars>
    </div>
  </div>
</template>
    
<script>
import EChartsBars from '@/components/BlastFurnaceCurve/echartsBars'
import ECharts4 from '@/components/BlastFurnaceCurve/echarts4'
import EChartsH3 from '@/components/BlastFurnaceCurve/echartsH3'
import EChartsBH from '@/components/BlastFurnaceCurve/echartsBH'
import EChartsLs from '@/components/BlastFurnaceCurve/echartsLs'

export default {
  components: { ECharts4, EChartsBars,EChartsH3, EChartsBH, EChartsLs},
  data() {
    return {
      title4: '烧结产量数据总览',
      titleL: '烧结物料消耗',
      type4: '烧结'
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
  },
  watch: {
  }
}
</script>
<style scoped>
.sum-container {
  padding: 18px 15px;
  background-color: #f7f8fa;
}
.sum-content-up {
  height: 67.8vh;
  width: 100%;
  display: flex;
  position: sticky;
  flex-direction: column;
  flex-wrap: nowrap;
}
.sum-content-up01 {
  width: 100%;
  height: 57%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.sum-content-up01-left {
  flex: 1;
  height: 100%;
  background-color: #fff;
  border-radius: 5px;
  margin: 0 10px 0 0;
}
.sum-content-up02-left {
  flex: 1;
  height: 100%;
  background-color: #fff;
  border-radius: 5px;
}
.sum-content-up01-right {
  width: 500px;
  height: 100%;
  display: flex;
  position: sticky;
  flex-direction: column;
  flex-wrap: nowrap;
}
.right-son01,
.right-son02 {
  width: 100%;
  height: 48.9%;
  background-color: #fff;
  border-radius: 5px;
}
.right-son02 {
  margin: 1.8% 0 0 0;
}
.sum-content-up02 {
  width: 100%;
  height: 41.8%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin: 0.5% 0 0 0;
}
.sum-content-down {
  height: 28vh;
  width: 100%;
  display: flex;
  position: sticky;
  margin: 8px 0 0 0;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #e9eaed;
}
</style>
<style>
.buttonGLXL.el-button {
  border: none;
  background: #f2f3f5;
  color: #0852ff;
  height: 28px;
  width: 78px;
  padding: 0px;
  font-size: 13px;
  font-family: Arial, Helvetica, sans-serif;
}
.buttonGLXL.el-button:hover,
.buttonGLXL.el-button.active {
  background: #0852ff;
  color: white;
}
</style>