<template>
	<div>
		<v-form-designer ref="vfd" :designer-config="designerConfig" :banned-widgets="bannedWidgets">
			<template #customToolButtons>
				<el-button type="text" @click="saveFormJson">保存页面</el-button>
			</template>
		</v-form-designer>
    <!-- <div>{{designerParentData}}</div> -->
	</div>
</template>

<script>
import { getformjsonDesigner, saveformjson } from '@/api/formtemplate/details';

export default {
  props:{
    // designerParentData:{
    //   type:String,
    //   default:'',
    // },
	propPageKey: String,
  },
	data() {
		return {
			designerConfig: {
				languageMenu: false,
				externalLink: false,
				formTemplates: false,
				importJsonButton: false,
				exportJsonButton: false,
				exportCodeButton: false,
				generateSFCButton: false,
				//eventCollapse: false,
				clearDesignerButton: false,
				previewFormButton: true,
				widgetNameReadonly: true,
				//presetCssCode: '.abc { font-size: 16px; }',
			},
			bannedWidgets: ['input', 'textarea', 'number', 'radio', 'checkbox', 'select', 'time', 'time-range', 'date', 'date-range', 'switch', 'rate', 'color', 'slider', 'static-text', 'html-text', 'button', 'divider', 'picture-upload', 'file-upload', 'rich-editor', 'cascader'],
			//bannedWidgets:[],
			formjson: {},
			fieldListApi: {},
			pageKey : ''
		}
	},
	created() {
		
		this.pageKey = this.$route.query.pageKey;
		this.pageKey = this.propPageKey;

		if (this.pageKey.pageKey == null) {
			// this.pageKey = 'teamnotes';
		}

		this.$nextTick(() => {
			//清除缓存
			this.$refs.vfd.clearDesigner();
			this.getformjson();
		})
	},
	methods: {
		saveFormJson() {
			alert(JSON.stringify(this.$refs.vfd.getFormJson()));
			saveformjson(this.pageKey, this.$refs.vfd.getFormJson()).then((response) => {
				console.log("返回数据:" + response);
				if (response == 1) {
					this.$message.success("保存成功");
				}
				else {
					this.$message.success("保存失败");
				}
			});
		},
		getFieldWid() {
			console.log(JSON.stringify(this.$refs.vfd.getFieldWidgets()));
		},
		getformjson() {
			//console.log(this.pageKey);
			getformjsonDesigner(this.pageKey,'1').then((response) => {
				console.log(response);
				this.formjson = response;
				this.$refs.vfd.setFormJson(this.formjson);
			});
		}
	}
}
</script>

<style lang="scss">
body {
	margin: 0;
}
</style>
