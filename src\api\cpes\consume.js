import request from '@/utils/request'

// 查询烧结物料消耗列表
export function listConsume(query, selectVO) {
  return request({
    url: '/api/cpes/consume/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

export function getEditList(query) {
  return request({
    url: '/api/cpes/consume/getEditList',
    method: 'get',
    params: query
  })
}

export function queryforManger(query) {
  return request({
    url: '/api/cpes/consume/queryforManger',
    method: 'get',
    params: query
  })
}

// 查询烧结物料消耗详细
export function getConsume(consumeId) {
  return request({
    url: '/api/cpes/consume/' + consumeId,
    method: 'get'
  })
}

// 新增烧结物料消耗
export function addConsume(data) {
  return request({
    url: '/api/cpes/consume',
    method: 'post',
    data: data
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/consume/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 修改烧结物料消耗
export function updateConsume(data) {
  return request({
    url: '/api/cpes/consume',
    method: 'put',
    data: data
  })
}

// 删除烧结物料消耗
export function delConsume(deleteObject) {
  return request({
    url: '/api/cpes/consume/delete',
    params: deleteObject,
    method: 'get'
  })
}

export function getBinNameList(prodcenterCode) {
  return request({
    url: '/api/cpes/consume/getBinNameList/' + prodcenterCode,
    method: 'get'
  })
}

export function getFeedinPlan(prodcenterCode) {
  return request({
    url: '/api/cpes/consume/getFeedinPlan/' + prodcenterCode,
    method: 'get'
  })
}
