<template>
  <div class="app-container">
    <!-- 生产计划制定弹窗 -->
    <el-dialog
      :title="isEdit ? '编辑生产计划' : '生产计划制定'"
      :visible.sync="createDialogVisible"
      width="1200px"
      top="5vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="plan-create-dialog"
      :modal-append-to-body="false"
    >
      <!-- 生产计划制定表格 -->
      <div class="plan-table-container">
        <!-- 基本信息行 -->
        <div class="basic-info-section">
          <el-form :model="basicForm" label-width="80px" size="small" class="basic-form">
            <el-row :gutter="24">
              <el-col :span="8">
                <el-form-item label="计划时间" required>
                  <el-date-picker
                    v-model="basicForm.planYear"
                    type="month"
                    placeholder="选择年月"
                    format="yyyy年MM月"
                    value-format="yyyy-MM"
                    style="width: 100%"
                    @change="handlePlanTimeChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="作业天数">
                  <el-input-number
                    v-model="basicForm.workingDays"
                    :min="1"
                    :max="31"
                    :controls="false"
                    style="width: 100%"
                    placeholder="天数"
                    @input="calculateDailyTargets"
                    @change="calculateDailyTargets"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="备注">
                  <el-input
                    v-model="planForm.remark"
                    placeholder="请输入备注"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>

        <!-- 生产计划表格区域 -->
        <div class="plan-tables-section">
          <div class="section-title">生产计划数据</div>
          <div class="tables-container">
            <!-- 烧结生产计划表格 -->
            <div class="plan-data-table half-width">
              <div class="table-title">
                <i class="el-icon-s-data"></i>
                烧结生产计划
              </div>
            <table class="production-plan-table compact">
            <thead>
              <tr>
                <th rowspan="2" class="center-header">生产中心</th>
                <th colspan="2" class="group-header ensure-header">必保目标</th>
                <th colspan="2" class="group-header strive-header">力争目标</th>
              </tr>
              <tr>
                <th class="sub-header">月产</th>
                <th class="sub-header">日产</th>
                <th class="sub-header">月产</th>
                <th class="sub-header">日产</th>
              </tr>
            </thead>
            <tbody>
              <!-- 1#烧结行 -->
              <tr>
                <td class="center-cell">1#烧结</td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes01.monthEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('sinter', 'cpes01', 'ensure')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes01.dayEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes01.monthStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('sinter', 'cpes01', 'strive')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes01.dayStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
              </tr>

              <!-- 2#烧结行 -->
              <tr>
                <td class="center-cell">2#烧结</td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes02.monthEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('sinter', 'cpes02', 'ensure')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes02.dayEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes02.monthStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('sinter', 'cpes02', 'strive')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.sinter.cpes02.dayStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
              </tr>

              <!-- 合计行 -->
              <tr class="total-row">
                <td class="center-cell total-cell">合计</td>
                <td class="display-cell total-cell">{{ formatNumber(getSinterTotalValue('monthEnsureTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getSinterTotalValue('dayEnsureTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getSinterTotalValue('monthStriveTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getSinterTotalValue('dayStriveTarget')) }}</td>
              </tr>
            </tbody>
          </table>
          </div>

            <!-- 高炉生产计划表格 -->
            <div class="plan-data-table half-width">
              <div class="table-title">
                <i class="el-icon-s-data"></i>
                高炉生产计划
              </div>
            <table class="production-plan-table compact">
            <thead>
              <tr>
                <th rowspan="2" class="center-header">生产中心</th>
                <th colspan="2" class="group-header ensure-header">必保目标</th>
                <th colspan="2" class="group-header strive-header">力争目标</th>
              </tr>
              <tr>
                <th class="sub-header">月产</th>
                <th class="sub-header">日产</th>
                <th class="sub-header">月产</th>
                <th class="sub-header">日产</th>
              </tr>
            </thead>
            <tbody>
              <!-- 1#高炉行 -->
              <tr>
                <td class="center-cell">1#高炉</td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes01.monthEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('blastFurnace', 'ipes01', 'ensure')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes01.dayEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes01.monthStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('blastFurnace', 'ipes01', 'strive')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes01.dayStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
              </tr>

              <!-- 2#高炉行 -->
              <tr>
                <td class="center-cell">2#高炉</td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes02.monthEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('blastFurnace', 'ipes02', 'ensure')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes02.dayEnsureTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes02.monthStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                    @change="calculateDailyTarget('blastFurnace', 'ipes02', 'strive')"
                  />
                </td>
                <td class="input-cell">
                  <el-input-number
                    v-model="planData.blastFurnace.ipes02.dayStriveTarget"
                    :min="0"
                    :precision="0"
                    :controls="false"
                    size="mini"
                    placeholder="0"
                  />
                </td>
              </tr>

              <!-- 合计行 -->
              <tr class="total-row">
                <td class="center-cell total-cell">合计</td>
                <td class="display-cell total-cell">{{ formatNumber(getBlastFurnaceTotalValue('monthEnsureTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getBlastFurnaceTotalValue('dayEnsureTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getBlastFurnaceTotalValue('monthStriveTarget')) }}</td>
                <td class="display-cell total-cell">{{ formatNumber(getBlastFurnaceTotalValue('dayStriveTarget')) }}</td>
              </tr>
            </tbody>
            </table>
            </div>
          </div>
        </div>
      </div>

      <!-- 弹窗底部按钮 -->
      <div slot="footer" class="dialog-footer">
        <el-button @click="createDialogVisible = false" size="medium">取 消</el-button>
        <el-button @click="handleReset" size="medium">重 置</el-button>
        <el-button type="primary" @click="handleSave" size="medium">
          <i class="el-icon-check"></i>
          {{ isEdit ? '更 新' : '保 存' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 月度计划数据表格 -->
    <el-card class="simple-card" size="mini">
      <!-- 查询区域 -->
      <div class="query-section">
        <el-form :model="queryForm" inline size="small" class="query-form">
          <el-form-item label="计划时间">
            <el-date-picker
              v-model="queryForm.timeRange"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              format="yyyy年MM月"
              value-format="yyyy-MM"
              style="width: 260px;"
            />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="queryForm.status" placeholder="请选择状态" style="width: 140px;" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="已保存" value="0"></el-option>
              <el-option label="已下发" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button size="small" type="primary" icon="el-icon-search" @click="handleQuery">查询</el-button>
            <el-button size="small" type="primary" icon="el-icon-plus" @click="showCreateDialog">新增</el-button>
            <el-button size="small" icon="el-icon-refresh-left" @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 数据表格 -->
      <div class="table-container">
        <vxe-table
          ref="planTable"
          :data="planTableData"
          border
          stripe
          size="mini"
          :height="tableHeight"
          :empty-text="planTableData.length === 0 ? '暂无数据，请先编制计划' : ''"
          :expand-config="{trigger: 'row', lazy: false}"
        >
          <!-- 展开列 -->
          <vxe-column type="expand" width="50" align="center">
            <template #content="{ row }">
              <div class="expand-content">
                <div class="expand-detail">
                  <!-- 烧结行 -->
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="label">1#烧结</span>
                      <span class="value">月必保目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes01', 'monthEnsureTarget')) }}</span>
                      <span class="value">月力争目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes01', 'monthStriveTarget')) }}</span>
                      <span class="value">日必保目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes01', 'dayEnsureTarget')) }}</span>
                      <span class="value">日力争目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes01', 'dayStriveTarget')) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">1#高炉</span>
                      <span class="value">月必保目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes01', 'monthEnsureTarget')) }}</span>
                      <span class="value">月力争目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes01', 'monthStriveTarget')) }}</span>
                      <span class="value">日必保目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes01', 'dayEnsureTarget')) }}</span>
                      <span class="value">日力争目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes01', 'dayStriveTarget')) }}</span>
                    </div>
                  </div>
                  <!-- 高炉行 -->
                  <div class="detail-row">
                    <div class="detail-item">
                      <span class="label">2#烧结</span>
                      <span class="value">月必保目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes02', 'monthEnsureTarget')) }}</span>
                      <span class="value">月力争目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes02', 'monthStriveTarget')) }}</span>
                      <span class="value">日必保目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes02', 'dayEnsureTarget')) }}</span>
                      <span class="value">日力争目标: {{ formatNumber(getExpandValue(row, 'sinter', 'cpes02', 'dayStriveTarget')) }}</span>
                    </div>
                    <div class="detail-item">
                      <span class="label">2#高炉</span>
                      <span class="value">月必保目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes02', 'monthEnsureTarget')) }}</span>
                      <span class="value">月力争目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes02', 'monthStriveTarget')) }}</span>
                      <span class="value">日必保目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes02', 'dayEnsureTarget')) }}</span>
                      <span class="value">日力争目标: {{ formatNumber(getExpandValue(row, 'blastFurnace', 'ipes02', 'dayStriveTarget')) }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </vxe-column>

          <!-- 日期列 -->
          <vxe-column field="planMonth" title="日期(年/月)" width="120" align="center">
            <template #default="{ row }">
              <span>{{ extractMonthFromDate(row.workDate) }}</span>
            </template>
          </vxe-column>

          <!-- 烧结矿分组 -->
          <vxe-colgroup title="烧结矿"  align="center">
            <vxe-column field="sinterMonthEnsure" title="必保月" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateSinterTotal(row, 'monthEnsure')) }}</span>
              </template>
            </vxe-column>
            <vxe-column field="sinterDayEnsure" title="必保日" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateSinterTotal(row, 'dayEnsure')) }}</span>
              </template>
            </vxe-column>
            <vxe-column field="sinterMonthStrive" title="力争月" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateSinterTotal(row, 'monthStrive')) }}</span>
              </template>
            </vxe-column>
            <vxe-column field="sinterDayStrive" title="力争日" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateSinterTotal(row, 'dayStrive')) }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 铁水分组 -->
          <vxe-colgroup title="铁水"  align="center">
            <vxe-column field="ballMonthEnsure" title="必保月" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateBlastFurnaceTotal(row, 'monthEnsure')) || '------' }}</span>
              </template>
            </vxe-column>
            <vxe-column field="ballDayEnsure" title="必保日" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateBlastFurnaceTotal(row, 'dayEnsure')) || '------' }}</span>
              </template>
            </vxe-column>
            <vxe-column field="ballMonthStrive" title="力争月" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateBlastFurnaceTotal(row, 'monthStrive')) || '------' }}</span>
              </template>
            </vxe-column>
            <vxe-column field="ballDayStrive" title="力争日" width="100" align="center">
              <template #default="{ row }">
                <span>{{ formatNumber(calculateBlastFurnaceTotal(row, 'dayStrive')) || '------' }}</span>
              </template>
            </vxe-column>
          </vxe-colgroup>

          <!-- 状态列 -->
          <vxe-column field="status" title="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="mini">{{ row.statusName}}</el-tag>
            </template>
          </vxe-column>

          <!-- 备注列 -->
          <vxe-column field="remark" title="备注" width="120" align="center">
            <template #default="{ row }">
              <span>{{ row.remark || '-' }}</span>
            </template>
          </vxe-column>

          <!-- 创建时间列 -->
          <vxe-column field="createTime" title="创建时间" width="140" align="center">
            <template #default="{ row }">
              <span>{{ formatDateTime(row.createTime) }}</span>
            </template>
          </vxe-column>

          <!-- 操作列 -->
          <vxe-column title="操作" width="180" align="center" fixed="right">
            <template #default="{ row, rowIndex }">
              <el-button type="text" size="mini" @click="editPlan(row, rowIndex)">编辑</el-button>
              <el-button type="text" size="mini" @click="handleSubmitPlan(row, rowIndex)" v-if="row.status === '0'">下发</el-button>
              <el-button type="text" size="mini" style="color: #f56c6c;" @click="deletePlan(row, rowIndex)">删除</el-button>
            </template>
          </vxe-column>
        </vxe-table>
      </div>

      <!-- 分页导航 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  addMonthPlan,
  updateMonthPlan,
  getMonthPlan,
  addProductPlan,
  listProductPlan,
  listProductPlanYear,
  getProductPlan,
  updateProductPlan,
  delProductPlan,
  submitProductPlan,
  issueProductPlan,
  exportProductPlan,



  delProductPlanDay
} from "@/api/feedingplan/mps";

export default {
  name: "PlanCreat",
  data() {
    return {
      // 基本信息表单
      basicForm: {
        planYear: '', // 计划时间（年月）- 前端显示格式：2025-06
        workingDays: 28 // 作业天数
      },

      // 格式化后的日期（用于后端传递）
      planYearFormatted: '', // 后端传递格式：2025-06-01

      // 计划数据表单
      planForm: {
        ensureMonthlyTarget: 0, // 必保目标月产计划
        ensureDailyTarget: 0, // 必保目标作业日产
        striveMonthlyTarget: 0, // 力争目标月产计划
        striveDailyTarget: 0, // 力争目标作业日产
        remark: '' // 备注
      },

      // 表格形式的计划数据
      planData: {
        // 烧结数据
        sinter: {
          cpes01: {
            monthEnsureTarget: 0,
            monthStriveTarget: 0,
            dayEnsureTarget: 0,
            dayStriveTarget: 0
          },
          cpes02: {
            monthEnsureTarget: 0,
            monthStriveTarget: 0,
            dayEnsureTarget: 0,
            dayStriveTarget: 0
          }
        },
        // 高炉数据
        blastFurnace: {
          ipes01: {
            monthEnsureTarget: 0,
            monthStriveTarget: 0,
            dayEnsureTarget: 0,
            dayStriveTarget: 0
          },
          ipes02: {
            monthEnsureTarget: 0,
            monthStriveTarget: 0,
            dayEnsureTarget: 0,
            dayStriveTarget: 0
          }
        }
      },

      // 表单验证规则
      basicRules: {
        planYear: [
          { required: true, message: '请选择计划时间', trigger: 'change' }
        ],
        workingDays: [
          { required: true, message: '请输入作业天数', trigger: 'blur' },
          { type: 'number', min: 1, max: 31, message: '作业天数必须在1-31之间', trigger: 'blur' }
        ]
      },

      planRules: {
        ensureMonthlyTarget: [
          { required: true, message: '请输入必保目标月产计划', trigger: 'blur' },
          { type: 'number', min: 0, message: '月产计划不能小于0', trigger: 'blur' }
        ],
        ensureDailyTarget: [
          { required: true, message: '请输入必保目标作业日产', trigger: 'blur' },
          { type: 'number', min: 0, message: '作业日产不能小于0', trigger: 'blur' }
        ],
        striveMonthlyTarget: [
          { required: true, message: '请输入力争目标月产计划', trigger: 'blur' },
          { type: 'number', min: 0, message: '月产计划不能小于0', trigger: 'blur' }
        ],
        striveDailyTarget: [
          { required: true, message: '请输入力争目标作业日产', trigger: 'blur' },
          { type: 'number', min: 0, message: '作业日产不能小于0', trigger: 'blur' }
        ]
      },

      // 编辑模式
      isEdit: false,
      planId: null,

      // 弹窗控制
      createDialogVisible: false,

      // 查询表单
      queryForm: {
        timeRange: [], // 时间范围
        status: ''     // 状态
      },

      // 分页配置
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },

      // 表格数据
      planTableData: [],
      fullTableData: [],

      // 初始化planIds对象，用于存储每个生产中心的planId
      planIds: {
          sinter: {
          cpes01: null,
          cpes02: null
          },
          blastFurnace: {
          ipes01: null,
          ipes02: null
        }
      },
      tableHeight: 500, // 默认表格高度
    };
  },



  created() {
    this.init();
  },

  mounted() {
    // 在mounted中初始化分页，确保数据已经准备好
    this.initPagination();
    // 加载初始数据
    this.loadInitialData();
    this.calculateTableHeight();
    window.addEventListener('resize', this.calculateTableHeight);
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.calculateTableHeight);
  },

  methods: {
    /** 初始化 */
    init() {
      this.initDefaultValues();
      this.checkEditMode();
    },

    /** 检查是否为编辑模式 */
    checkEditMode() {
      const planId = this.$route.query.planId;
      if (planId) {
        this.isEdit = true;
        this.planId = planId;
        this.loadPlanData(planId);
      }
    },

    /** 加载计划数据（编辑模式） */
    async loadPlanData(planId) {
      try {
        const response = await getMonthPlan(planId);
        const data = response.data;

        // 填充基本信息
        this.basicForm = {
          planTime: data.planTime,
          workingDays: data.workingDays
        };

        // 填充计划数据
        this.planForm = {
          ensureMonthlyTarget: data.ensureMonthlyTarget,
          ensureDailyTarget: data.ensureDailyTarget,
          striveMonthlyTarget: data.striveMonthlyTarget,
          striveDailyTarget: data.striveDailyTarget,
          remark: data.remark
        };

      } catch (error) {
        this.$modal.msgError('加载计划数据失败');
        console.error(error);
      }
    },

    /** 初始化默认值 */
    initDefaultValues() {
      // 设置默认计划时间为当前月份
      const now = new Date();
      this.basicForm.planYear = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;

      // 设置默认作业天数为28天
      this.basicForm.workingDays = 28;
    },

    /** 初始化分页 */
    initPagination() {
      // 如果fullTableData为空，则使用planTableData的数据
      if (this.fullTableData.length === 0) {
        this.fullTableData = [...this.planTableData];
      }
      this.pagination.total = this.fullTableData.length;
      this.updateTableData();
    },

    /** 处理返回数据为前端需要的格式 */
    processApiData(apiData) {
      if (!Array.isArray(apiData) || apiData.length === 0) return [];
      
      // 每组生产中心数据
      return apiData.map(centerGroup => {
        // 确保 centerGroup 是数组且有数据
        if (!Array.isArray(centerGroup) || centerGroup.length === 0) return null;
        
        // 获取第一条记录的基本信息
        const baseInfo = centerGroup[0];
        
        // 构建基础数据结构
        const processedRow = {
          id: baseInfo.planId,
          planYear: baseInfo.planYear,
          workDate: baseInfo.workDate,
          status: baseInfo.status?.toString() || '0',
          statusName: baseInfo.statusName || '保存',
          remark: baseInfo.remark,
          createTime: baseInfo.createTime,
          
          // 初始化烧结和高炉数据结构
          sinter: {
            cpes01: { monthEnsureTarget: 0, monthStriveTarget: 0, dayEnsureTarget: 0, dayStriveTarget: 0, planId: null },
            cpes02: { monthEnsureTarget: 0, monthStriveTarget: 0, dayEnsureTarget: 0, dayStriveTarget: 0, planId: null }
          },
          blastFurnace: {
            ipes01: { monthEnsureTarget: 0, monthStriveTarget: 0, dayEnsureTarget: 0, dayStriveTarget: 0, planId: null },
            ipes02: { monthEnsureTarget: 0, monthStriveTarget: 0, dayEnsureTarget: 0, dayStriveTarget: 0, planId: null }
          },
          expandData: [] // 存储原始数据
        };

        // 处理每个生产中心的数据
        centerGroup.forEach(item => {
          // 存储原始数据到expandData
          processedRow.expandData.push({
            prodCenterCode: item.prodCenterCode,
            monthEnsureTarget: item.monthEnsureTarget,
            monthStriveTarget: item.monthStriveTarget,
            dayEnsureTarget: item.dayEnsureTarget,
            dayStriveTarget: item.dayStriveTarget,
            planId: item.planId
          });

          // 根据生产中心代码分配数据
          const centerCode = item.prodCenterCode.toLowerCase();
          const centerData = {
            monthEnsureTarget: item.monthEnsureTarget || 0,
            monthStriveTarget: item.monthStriveTarget || 0,
          dayEnsureTarget: item.dayEnsureTarget || 0,
            dayStriveTarget: item.dayStriveTarget || 0,
            planId: item.planId // 存储planId
          };

          if (centerCode.startsWith('cpes')) {
            processedRow.sinter[centerCode] = centerData;
          } else if (centerCode.startsWith('ipes')) {
            processedRow.blastFurnace[centerCode] = centerData;
          }
        });

        return processedRow;
      }).filter(Boolean); // 过滤掉null值
    },

    /** 从日期字符串中提取月份信息 */
    extractMonthFromDate(dateString) {
      if (!dateString) return '-';
      try {
        // 处理 "2025-06-01 00:00:00" 格式
        const match = dateString.match(/^(\d{4})-(\d{2})/);
        if (match) {
          return `${match[1]}-${match[2]}`;
        }
        // 如果不是预期格式，尝试用 Date 对象处理
        const date = new Date(dateString);
        return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      } catch (error) {
        console.warn('日期解析失败:', dateString);
        return '-';
      }
    },

    /** 分页大小改变 */
    async handleSizeChange(val) {
      this.pagination.pageSize = val;
      await this.loadInitialData(); 
    },

    /** 当前页改变 */
    async handleCurrentChange(val) {
      this.pagination.currentPage = val;
      await this.loadInitialData(); 
    },

    /** 加载初始数据 */
    async loadInitialData() {
      try {
        const queryParams = {
          pageNums: this.pagination.currentPage,
          pageSizes: this.pagination.pageSize
        };
        
        const response = await listProductPlanYear(queryParams);
        
        if (response.code === 200) {
          // 处理数据
          const processedData = this.processApiData(response.data || []);
          this.planTableData = processedData;
          this.pagination.total = response.total || 0;
        } else {
          this.$message.warning(response.msg || '加载数据失败');
        }
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败');
      }
},

    /** 计算烧结矿汇总数据 */
    calculateSinterTotal(row, targetType) {
      if (!row || !row.sinter) {
        return 0;
      }

      let total = 0;

      try {
        // 根据目标类型计算对应的字段
        let fieldName = '';
        switch (targetType) {
          case 'monthEnsure':
            fieldName = 'monthEnsureTarget';
            break;
          case 'dayEnsure':
            fieldName = 'dayEnsureTarget';
            break;
          case 'monthStrive':
            fieldName = 'monthStriveTarget';
            break;
          case 'dayStrive':
            fieldName = 'dayStriveTarget';
            break;
          default:
            return 0;
        }

        // 计算1#烧结 + 2#烧结的汇总
        const cpes01Value = (row.sinter.cpes01 && row.sinter.cpes01[fieldName]);
        const cpes02Value = (row.sinter.cpes02 && row.sinter.cpes02[fieldName]);

        total = cpes01Value + cpes02Value;


      } catch (error) {
        console.warn('计算烧结矿汇总数据失败:', error);
        return 0;
      }

      return total;
    },

    /** 计算铁水（高炉）汇总数据 */
    calculateBlastFurnaceTotal(row, targetType) {
      if (!row || !row.blastFurnace) {
        return 0;
      }

      let total = 0;

      try {
        // 根据目标类型计算对应的字段
        let fieldName = '';
        switch (targetType) {
          case 'monthEnsure':
            fieldName = 'monthEnsureTarget';
            break;
          case 'dayEnsure':
            fieldName = 'dayEnsureTarget';
            break;
          case 'monthStrive':
            fieldName = 'monthStriveTarget';
            break;
          case 'dayStrive':
            fieldName = 'dayStriveTarget';
            break;
          default:
            return 0;
        }

        // 计算1#高炉 + 2#高炉的汇总
        const ipes01Value = (row.blastFurnace.ipes01 && row.blastFurnace.ipes01[fieldName]);
        const ipes02Value = (row.blastFurnace.ipes02 && row.blastFurnace.ipes02[fieldName]);

        total = ipes01Value + ipes02Value;


      } catch (error) {
        console.warn('计算铁水汇总数据失败:', error);
        return 0;
      }

      return total;
    },

    /** 更新表格数据（分页） */
    updateTableData() {
      if (!this.fullTableData || !Array.isArray(this.fullTableData)) {
        console.warn('fullTableData 不是有效的数组');
        this.planTableData = [];
        return;
      }

      const start = (this.pagination.currentPage - 1) * this.pagination.pageSize;
      const end = start + this.pagination.pageSize;
      this.planTableData = this.fullTableData.slice(start, end);
    },

    /** 显示新增弹窗 */
    showCreateDialog() {
      // 重置编辑状态
      this.isEdit = false;
      this.planId = null;
      
      // 打开弹窗
      this.createDialogVisible = true;
      
      // 重置表单数据
      this.handleReset();
    },

    /** 查询数据 */
    async handleQuery() {
      try {
        // 重置到第一页
        this.pagination.currentPage = 1;

        // 构建查询参数
        const queryParams = {
          pageNums: this.pagination.currentPage,
          pageSizes: this.pagination.pageSize
        };

        // 添加时间范围查询条件
        if (this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
          const [startTime, endTime] = this.queryForm.timeRange;
          // 将年月格式转换为年月日格式
          queryParams.startTime = `${startTime}-01`;
          queryParams.endTime = `${endTime}-01`;
        }

        // 添加状态查询条件
        if (this.queryForm.status !== '') {
          queryParams.status = this.queryForm.status;
        }

        // 调用查询API
        const response = await listProductPlanYear(queryParams);

        if (response.code === 200) {
          // 处理数据
          const processedData = this.processApiData(response.data || []);
          this.planTableData = processedData;
          this.pagination.total = response.total;
        } else {
          this.$message.warning(response.msg || '加载数据失败');
        }
      } catch (error) {
        console.error('查询失败:', error);
        this.$message.error('查询失败');
      }
    },

    /** 本地查询（后备方案） */
    handleLocalQuery() {
      // 这里可以根据查询条件过滤表格数据
      let filteredData = [...this.fullTableData];

      // 按时间范围过滤
      if (this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
        const [startTime, endTime] = this.queryForm.timeRange;
        filteredData = filteredData.filter(item => {
          const planTime = item.planYear;
          return planTime >= startTime && planTime <= endTime;
        });
      }

      // 按状态过滤
      if (this.queryForm.status !== '') {
        filteredData = filteredData.filter(item => {
          return item.status === this.queryForm.status;
        });
      }

      // 更新过滤后的数据
      this.fullTableData = filteredData;
      this.pagination.total = filteredData.length;
      this.pagination.currentPage = 1;
      this.updateTableData();

      this.$message.success(`本地查询完成，共找到 ${filteredData.length} 条记录`);
    },

    /** 导出数据 */
    async handleExport() {
      try {
        // 构建导出参数
        const exportParams = {
          ...this.queryForm
        };

        // 添加时间范围
        if (this.queryForm.timeRange && this.queryForm.timeRange.length === 2) {
          exportParams.startTime = this.queryForm.timeRange[0];
          exportParams.endTime = this.queryForm.timeRange[1];
          delete exportParams.timeRange; // 删除原始时间范围字段
        }

        this.$message.info('正在导出数据，请稍候...');

        // 调用导出API
        const response = await exportProductPlan(exportParams);

        // 创建下载链接
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `生产计划_${new Date().toISOString().slice(0, 10)}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);

        this.$message.success('导出成功');
      } catch (error) {
        console.error('导出失败:', error);
        this.$message.error('导出失败');
      }
    },

    /** 重置查询条件 */
    handleResetQuery() {
      this.queryForm = {
        timeRange: [],
        status: ''
      };

      // 重置数据为原始数据
      this.fullTableData = [
        {
          id: 1,
          planYear: '2025-06',
          planTime: '2025-06',
          // 烧结矿数据
          sinterMonthEnsure: 560200,
          sinterDayEnsure: 12012,
          sinterMonthStrive: 65000,
          sinterDayStrive: 13000,
          // 铁水数据（暂无）
          ballMonthEnsure: null,
          ballDayEnsure: null,
          ballMonthStrive: null,
          ballDayStrive: null,
          // 详细数据
          sinter: {
            cpes01: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            },
            cpes02: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            }
          },
          blastFurnace: {
            cpbf01: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            },
            cpbf02: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            }
          },
          remark: '正常生产计划',
          status: '0',
          statusName: '已保存',
          createTime: new Date()
        },
        {
          id: 2,
          planYear: '2025-06',
          planTime: '2025-06',
          // 烧结矿数据
          sinterMonthEnsure: 500200,
          sinterDayEnsure: 12012,
          sinterMonthStrive: 65000,
          sinterDayStrive: 13000,
          // 铁水数据（暂无）
          ballMonthEnsure: null,
          ballDayEnsure: null,
          ballMonthStrive: null,
          ballDayStrive: null,
          // 详细数据
          sinter: {
            cpes01: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            },
            cpes02: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            }
          },
          blastFurnace: {
            cpbf01: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            },
            cpbf02: {
              monthEnsureTarget: 10700,
              monthStriveTarget: 29200,
              dayEnsureTarget: 12012,
              dayStriveTarget: 13000
            }
          },
          remark: '高炉检修计划',
          status: '1',
          statusName: '已下发',
          createTime: new Date()
        }
      ];

      this.pagination.total = this.fullTableData.length;
      this.pagination.currentPage = 1;
      this.updateTableData();

      this.$message.success('查询条件已重置');
    },

    /** 获取生产中心名称 */
    getProdCenterNames() {
      const centerNames = [];

      // 检查planData是否存在
      if (!this.planData || !this.planData.sinter || !this.planData.blastFurnace) {
        return '全部中心';
      }

      // 检查烧结数据
      Object.keys(this.planData.sinter).forEach(centerCode => {
        const centerData = this.planData.sinter[centerCode];
        const ensureTarget = centerData.yearEnsureTarget || centerData.ensureMonthlyTarget;
        const striveTarget = centerData.yearStriveTarget || centerData.striveMonthlyTarget;

        if (ensureTarget > 0 || striveTarget > 0) {
          const centerName = this.getCenterDisplayName(centerCode);
          centerNames.push(centerName);
        }
      });

      // 检查高炉数据
      Object.keys(this.planData.blastFurnace).forEach(centerCode => {
        const centerData = this.planData.blastFurnace[centerCode];
        const ensureTarget = centerData.yearEnsureTarget || centerData.ensureMonthlyTarget;
        const striveTarget = centerData.yearStriveTarget || centerData.striveMonthlyTarget;

        if (ensureTarget > 0 || striveTarget > 0) {
          const centerName = this.getCenterDisplayName(centerCode);
          centerNames.push(centerName);
        }
      });

      return centerNames.length > 0 ? centerNames.join('、') : '全部中心';
    },

    /** 获取生产中心显示名称 */
    getCenterDisplayName(centerCode) {
      const centerMap = {
        'cpes01': '1#烧结',
        'cpes02': '2#烧结',
        'cpbf01': '1#高炉',
        'cpbf02': '2#高炉'
      };
      return centerMap[centerCode.toLowerCase()] || centerCode.toUpperCase();
    },

    /** 获取展开列的值 */
    getExpandValue(row, category, centerCode, targetType) {
      try {
        // 从expandData中获取数据
        if (row && row.expandData) {
          const centerData = row.expandData.find(item => 
            item.prodCenterCode.toLowerCase() === centerCode.toLowerCase()
          );
          if (centerData && centerData[targetType] !== undefined) {
            return centerData[targetType];
          }
        }
        // 如果没有数据，返回默认值或 0
        return 0;
      } catch (error) {
        console.warn('获取展开列数据失败:', error);
        return 0;
      }
    },

    /** 获取月份字段名的辅助方法 */
    getMonthFieldName(monthIndex, targetType) {
      const monthFields = ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'];
      const monthField = monthFields[monthIndex];

      // 根据目标类型构建字段名
      if (targetType === 'ensure') {
        return monthField + 'EnsureTarget';
      } else if (targetType === 'strive') {
        return monthField + 'StriveTarget';
      }
      return null;
    },

    /** 格式化数字显示 */
    formatNumber(value) {
      // 如果值为null、undefined、0或空字符串，返回适当的显示
      if (value === null || value === undefined || value === '' || value === 0) {
        return '0';
      }

      // 如果是数字，格式化显示
      if (typeof value === 'number' || !isNaN(value)) {
        return Number(value).toLocaleString();
      }

      return value;
    },



    /** 计划时间变化处理 */
    handlePlanTimeChange(value) {
      if (value) {
        // 计划时间变化时不自动修改作业天数，保持用户设定的值
        this.calculateDailyTargets();
        // 重新计算表格中的所有日产目标
        this.recalculateAllDailyTargets();
      }
    },



    /** 获取月份工作天数 */
    getWorkingDaysInMonth(year, month) {
      const daysInMonth = new Date(year, month, 0).getDate();
      // 简单计算：总天数减去周末天数（假设每月有8-9个周末）
      const weekends = Math.floor(daysInMonth / 7) * 2;
      return daysInMonth - weekends;
    },

    /** 计算必保目标日产 */
    calculateEnsureDailyTarget() {
      if (this.planForm.ensureMonthlyTarget && this.basicForm.workingDays) {
        this.planForm.ensureDailyTarget = parseFloat((this.planForm.ensureMonthlyTarget / this.basicForm.workingDays).toFixed(2));
      }
    },



    /** 计算力争目标日产 */
    calculateStriveDailyTarget() {
      if (this.planForm.striveMonthlyTarget && this.basicForm.workingDays) {
        this.planForm.striveDailyTarget = parseFloat((this.planForm.striveMonthlyTarget / this.basicForm.workingDays).toFixed(2));
      }
    },



    /** 计算所有日产目标 */
    calculateDailyTargets() {
      this.calculateEnsureDailyTarget();
      this.calculateStriveDailyTarget();
    },

    // ==================== 表格形式相关方法 ====================

    /** 计算表格中的日产目标 */
    calculateDailyTarget(tableType, centerKey, targetType) {
      if (!this.basicForm.workingDays || this.basicForm.workingDays <= 0) {
        return;
      }

      // 检查planData是否存在
      if (!this.planData || !this.planData[tableType] || !this.planData[tableType][centerKey]) {
        return;
      }

      const centerData = this.planData[tableType][centerKey];

      switch (targetType) {
        case 'ensure':
          if (centerData.monthEnsureTarget) {
            centerData.dayEnsureTarget = Math.round(centerData.monthEnsureTarget / this.basicForm.workingDays);
          }
          break;
        case 'strive':
          if (centerData.monthStriveTarget) {
            centerData.dayStriveTarget = Math.round(centerData.monthStriveTarget / this.basicForm.workingDays);
          }
          break;
      }
    },

    /** 获取烧结合计值 */
    getSinterTotalValue(field) {
      let total = 0;
      if (this.planData && this.planData.sinter) {
        Object.keys(this.planData.sinter).forEach(centerKey => {
          const value = this.planData.sinter[centerKey][field];
          if (value && !isNaN(value)) {
            total += parseFloat(value);
          }
        });
      }
      return total;
    },

    /** 获取高炉合计值 */
    getBlastFurnaceTotalValue(field) {
      let total = 0;
      if (this.planData && this.planData.blastFurnace) {
        Object.keys(this.planData.blastFurnace).forEach(centerKey => {
          const value = this.planData.blastFurnace[centerKey][field];
          if (value && !isNaN(value)) {
            total += parseFloat(value);
          }
        });
      }
      return total;
    },

    /** 重新计算所有表格数据的日产目标 */
    recalculateAllDailyTargets() {
      // 检查planData是否存在
      if (!this.planData) {
        return;
      }

      // 重新计算烧结数据
      if (this.planData.sinter) {
        Object.keys(this.planData.sinter).forEach(centerKey => {
          this.calculateDailyTarget('sinter', centerKey, 'ensure');
          this.calculateDailyTarget('sinter', centerKey, 'strive');
        });
      }

      // 重新计算高炉数据
      if (this.planData.blastFurnace) {
        Object.keys(this.planData.blastFurnace).forEach(centerKey => {
          this.calculateDailyTarget('blastFurnace', centerKey, 'ensure');
          this.calculateDailyTarget('blastFurnace', centerKey, 'strive');
        });
      }
    },

    /** 重置表单 */
    handleReset() {
      
        // 重置表单数据
        this.basicForm = {
          planYear: '',
          workingDays: 28
        };
        // 重置格式化日期
        this.planYearFormatted = '';
        this.planForm = {
          yearEnsureTarget: 0,
          dayEnsureTarget: 0,
          yearStriveTarget: 0,
          dayStriveTarget: 0,
          remark: ''
        };
        // 重置表格数据
        this.planData = {
          // 烧结数据
          sinter: {
            cpes01: {
              monthEnsureTarget: 0,
              monthStriveTarget: 0,
              dayEnsureTarget: 0,
              dayStriveTarget: 0
            },
            cpes02: {
              monthEnsureTarget: 0,
              monthStriveTarget: 0,
              dayEnsureTarget: 0,
              dayStriveTarget: 0
            }
          },
          // 高炉数据
          blastFurnace: {
            ipes01: {
              monthEnsureTarget: 0,
              monthStriveTarget: 0,
              dayEnsureTarget: 0,
              dayStriveTarget: 0
            },
            ipes02: {
              monthEnsureTarget: 0,
              monthStriveTarget: 0,
              dayEnsureTarget: 0,
              dayStriveTarget: 0
            }
          }
        };
        this.initDefaultValues();
       // this.$message.success('重置成功');
      
    },

    /** 保存计划 */
    async handleSave() {
      try {
        // 基本验证
        if (this.basicForm.planYear) {
          // 因后端需要，将年月格式转换为年月日格式
          this.planYearFormatted = `${this.basicForm.planYear}-01`;
      
        } else {
          this.$message.warning('请选择计划时间');
          return;
        }

        // 验证表格数据
        if (!this.validateTableData()) {
          return;
        }

        // 构建请求参数数组
        const requestData = this.buildSaveRequestData();

        let response;
        if (this.isEdit) {
          // 编辑模式：调用更新API
          response = await updateProductPlan(requestData);
          if (response.code === 200) {
            this.$message.success('计划更新成功');
          }
        } else {
          // 新增模式：调用新增API
          response = await addProductPlan(requestData);
        if (response.code === 200) {
          this.$message.success('计划保存成功');
          }
        }

        if (response.code === 200) {
          // 刷新列表数据
          await this.handleQuery();

          // 关闭弹窗
          this.createDialogVisible = false;
          // 重置表单和编辑状态
          this.handleReset();
          this.isEdit = false;
        }
      } catch (error) {
        console.error('保存计划失败:', error);
        this.$message.error(this.isEdit ? '更新计划失败' : '保存计划失败');
      }
    },



    /** 构建保存请求数据 */
    buildSaveRequestData() {
      const requestData = [];

      // 烧结数据
      ['cpes01', 'cpes02'].forEach(centerCode => {
        const centerData = this.planData.sinter[centerCode];
        if (centerData) {
          requestData.push({
            planId: this.isEdit ? centerData.planId : undefined, // 使用存储在centerData中的planId
            planYear: this.planYearFormatted,
            prodCenterCode: centerCode.toUpperCase(),
            monthEnsureTarget: centerData.monthEnsureTarget,
            monthStriveTarget: centerData.monthStriveTarget,
            dayEnsureTarget: centerData.dayEnsureTarget,
            dayStriveTarget: centerData.dayStriveTarget,
            workingDays: this.basicForm.workingDays,
            remark: this.planForm.remark,
            status: 0 // 保存状态
          });
        }
      });

      // 高炉数据
      ['ipes01', 'ipes02'].forEach(centerCode => {
        const centerData = this.planData.blastFurnace[centerCode];
        if (centerData) {
          requestData.push({
            planId: this.isEdit ? centerData.planId : undefined, // 使用存储在centerData中的planId
            planYear: this.planYearFormatted,
            prodCenterCode: centerCode.toUpperCase(),
            monthEnsureTarget: centerData.monthEnsureTarget,
            monthStriveTarget: centerData.monthStriveTarget,
            dayEnsureTarget: centerData.dayEnsureTarget,
            dayStriveTarget: centerData.dayStriveTarget,
            workingDays: this.basicForm.workingDays,
            remark: this.planForm.remark,
            status: 0 // 保存状态
          });
        }
      });

      return requestData;
    },

    /** 验证表格数据 */
    validateTableData() {
      let hasData = false;


      // 检查烧结数据
      Object.values(this.planData.sinter).forEach(centerData => {

        if (centerData.monthEnsureTarget > 0 || centerData.monthStriveTarget > 0) {
          hasData = true;
        }
      });

      // 检查高炉数据
      Object.values(this.planData.blastFurnace).forEach(centerData => {

        if (centerData.monthEnsureTarget > 0 || centerData.monthStriveTarget > 0) {
          hasData = true;
        }
      });


      if (!hasData) {
        this.$message.warning('请至少输入一个生产中心的计划数据');
        return false;
      }

      return true;
    },





    /** 格式化日期时间 */
    formatDateTime(date) {
      if (!date) return '-';
      // 如果后端只返回日期（长度为10，形如 YYYY-MM-DD），则直接返回
      if (typeof date === 'string' && date.length === 10) {
        return date;
      }
      // 处理中包含时间戳或完整 ISO 字符串的情况
      const d = new Date(date);
      if (isNaN(d.getTime())) {
        // 解析失败，直接返回原始值
        return date;
      }
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      // 仅当时间不为 00:00 时显示时分
      if (hours === '00' && minutes === '00') {
        return `${year}-${month}-${day}`;
      }
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },

    /** 获取状态类型 */
    getStatusType(status) {
      switch (status) {
        case '0': return 'info';    // 已保存
        case '1': return 'success'; // 已提交
        default: return 'info';
      }
    },

    /** 添加数据到表格 */
    addToTable(planData) {
      // 检查是否已存在相同的计划（相同时间）
      const existingIndex = this.planTableData.findIndex(item =>
        item.planYear === planData.planYear
      );

      if (existingIndex >= 0) {
        // 如果存在，则更新
        this.$set(this.planTableData, existingIndex, planData);
      } else {
        // 如果不存在，则添加
        this.planTableData.unshift(planData);
      }
    },

    /** 更新单条表格数据 */
    updateSingleTableData(planData) {
      const index = this.planTableData.findIndex(item => item.id === planData.id);
      if (index >= 0) {
        this.$set(this.planTableData, index, planData);
      }
    },

    /** 编辑计划 */
    editPlan(row, rowIndex) {
      // 打开弹窗
      this.createDialogVisible = true;
      
      // 设置编辑模式
      this.isEdit = true;

      // 填充基本信息
      this.basicForm = {
        planYear: this.extractMonthFromDate(row.workDate),
        workingDays: 28
      };

      // 设置格式化后的计划年月（用于后端传递）
      this.planYearFormatted = row.workDate ? row.workDate.split(' ')[0] : '';

      // 填充备注
      this.planForm.remark = row.remark || '';

      // 先从expandData中获取每个生产中心的planId
      const planIds = {};
      if (row.expandData && row.expandData.length > 0) {
        const expandDataArray = JSON.parse(JSON.stringify(row.expandData));
        
        // 创建一个映射来存储最新的planId
        const latestPlanIds = {};
        expandDataArray.forEach((item, index) => {
          if (item.prodCenterCode && item.planId !== undefined) {
            latestPlanIds[item.prodCenterCode] = item.planId;
          }
        });

        // 使用最新的planId
        Object.entries(latestPlanIds).forEach(([code, id]) => {
          planIds[code.toLowerCase()] = id;
        });
      }

      // 填充表格数据，同时存储每个生产中心的planId
      this.planData = {
        sinter: {
          cpes01: {
            monthEnsureTarget: row.sinter?.cpes01?.monthEnsureTarget || 0,
            monthStriveTarget: row.sinter?.cpes01?.monthStriveTarget || 0,
            dayEnsureTarget: row.sinter?.cpes01?.dayEnsureTarget || 0,
            dayStriveTarget: row.sinter?.cpes01?.dayStriveTarget || 0,
            planId: planIds['cpes01'] || row.id // 如果在expandData中找不到，使用row.id
          },
          cpes02: {
            monthEnsureTarget: row.sinter?.cpes02?.monthEnsureTarget || 0,
            monthStriveTarget: row.sinter?.cpes02?.monthStriveTarget || 0,
            dayEnsureTarget: row.sinter?.cpes02?.dayEnsureTarget || 0,
            dayStriveTarget: row.sinter?.cpes02?.dayStriveTarget || 0,
            planId: planIds['cpes02']
          }
        },
        blastFurnace: {
          ipes01: {
            monthEnsureTarget: row.blastFurnace?.ipes01?.monthEnsureTarget || 0,
            monthStriveTarget: row.blastFurnace?.ipes01?.monthStriveTarget || 0,
            dayEnsureTarget: row.blastFurnace?.ipes01?.dayEnsureTarget || 0,
            dayStriveTarget: row.blastFurnace?.ipes01?.dayStriveTarget || 0,
            planId: planIds['ipes01']
          },
          ipes02: {
            monthEnsureTarget: row.blastFurnace?.ipes02?.monthEnsureTarget || 0,
            monthStriveTarget: row.blastFurnace?.ipes02?.monthStriveTarget || 0,
            dayEnsureTarget: row.blastFurnace?.ipes02?.dayEnsureTarget || 0,
            dayStriveTarget: row.blastFurnace?.ipes02?.dayStriveTarget || 0,
            planId: planIds['ipes02']
          }
        }
      };
    },

    /** 下发单个计划 */
    async handleSubmitPlan(row, rowIndex) {
      try {
        this.$confirm(`确定要下发该计划吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            // 构造四个生产中心的下发参数
            const payload = [
              {
                workDate: row.workDate || row.planYear,
                prodCenterCode: 'CPES01',
                planId: row.sinter?.cpes01?.planId,
                status: 1
              },
              {
                workDate: row.workDate || row.planYear,
                prodCenterCode: 'CPES02',
                planId: row.sinter?.cpes02?.planId,
                status: 1
              },
              {
                workDate: row.workDate || row.planYear,
                prodCenterCode: 'IPES01',
                planId: row.blastFurnace?.ipes01?.planId,
                status: 1
              },
              {
                workDate: row.workDate || row.planYear,
                prodCenterCode: 'IPES02',
                planId: row.blastFurnace?.ipes02?.planId,
                status: 1
              }
            ].filter(item => item.planId); // 去掉无planId的数据

            if (payload.length === 0) {
              this.$message.warning('缺少计划ID，无法下发');
              return;
            }

            // 调用下发API
            await issueProductPlan(payload);

            // 更新状态为已下发
            row.status = '1';
            row.statusName = '已下发';

            this.$message.success('计划下发成功');
          } catch (error) {
            console.error('下发计划API调用失败:', error);
            this.$message.error('下发计划失败');
          }
        }).catch(() => {});
      } catch (error) {
        this.$message.error('下发计划失败');
      }
    },

    /** 删除计划 */
    async deletePlan(row, rowIndex) {
      try {
        // 从expandData中获取所有planId
        if (!row.expandData || !Array.isArray(row.expandData)) {
          this.$message.error('无法获取计划数据');
          return;
        }

        // 构建删除请求数据
        const deleteData = row.expandData.map(item => ({
          status: 0,
          planId: item.planId
        }));

        // 确认删除
        await this.$confirm(`确定要删除该计划吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });

        // 调用删除API
        const response = await delProductPlan(deleteData);

        if (response.code === 200) {
          // 从表格中移除
          this.planTableData.splice(rowIndex, 1);

          // 同时从完整数据中移除
          const fullDataIndex = this.fullTableData.findIndex(item => item.id === row.id);
          if (fullDataIndex >= 0) {
            this.fullTableData.splice(fullDataIndex, 1);
            this.pagination.total = this.fullTableData.length;
            this.updateTableData();
          }

          this.$message.success('删除成功');

          // 如果删除的是当前编辑的计划，重置表单
          if (this.planId === row.id) {
            this.handleReset();
            this.isEdit = false;
            this.planId = null;
          }
        } else {
          this.$message.error(response.msg || '删除失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除计划失败:', error);
          this.$message.error('删除计划失败');
        }
      }
    },

    /** 重置按钮操作 */
    handleReset() {
      // 重置查询表单
      this.queryForm = {
        timeRange: [], // 重置时间范围
        status: ''    // 重置状态
      };
      
      // 重置分页参数
      this.pagination.currentPage = 1;
      this.pagination.pageSize = 20; // 重置为默认每页条数
      
      // 重新加载数据
      this.handleQuery();
    },

    /** 计算表格高度 */
    calculateTableHeight() {
      // 获取视窗高度
      const windowHeight = window.innerHeight;
      // 调整计算方式，预留更多空间给导航栏和其他元素
      this.tableHeight = windowHeight - 280; // 预留更多空间
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  min-height: calc(100vh - 120px); // 调整高度，确保导航栏可见
  display: flex;
  flex-direction: column;
  padding: 10px; // 添加内边距
  
  .simple-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-bottom: 0;
    
    .query-section {
      padding: 16px 16px 0;
      background-color: #fff;
      margin-bottom: 10px;
    }
    
    .table-container {
      flex: 1;
      position: relative;
      padding: 0 16px;
      
      .vxe-table {
        // 设置表格最大高度而不是固定高度
        max-height: calc(100vh - 280px);
      }
    }
    
    .pagination-container {
      padding: 15px 16px;
      background-color: #fff;
      border-top: 1px solid #e8e8e8;
      display: flex;
      justify-content: center;
      align-items: center;
      
      .el-pagination {
        padding: 0;
        margin: 0;
        font-weight: normal;
        
        .btn-prev,
        .btn-next {
          border-radius: 4px;
          padding: 0 10px;
        }
        
        .el-pager li {
          border-radius: 4px;
          margin: 0 3px;
          
          &.active {
            background-color: #409eff;
            color: #fff;
          }
        }
        
        .el-pagination__sizes {
          margin-right: 15px;
        }
      }
    }
  }
}

// 响应式布局
@media screen and (max-width: 768px) {
  .pagination-container {
    padding: 10px 0;
    
    .el-pagination {
      .el-pagination__sizes {
        display: none;
      }
    }
  }
}

.page-header {
  margin-bottom: 20px;

  h2 {
    margin: 0 0 8px 0;
    color: #303133;
    font-size: 24px;
    font-weight: 600;
  }

  .page-desc {
    margin: 0;
    color: #909399;
    font-size: 14px;
  }
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .header-buttons {
    float: right;

    .el-button {
      margin-left: 8px;
    }
  }
}

// 简洁卡片样式
.simple-card {
  .el-card__header {
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
  }
}

// 查询区域样式
.query-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px 0;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 16px;

  .query-form {
    flex: 1;
    margin-right: 20px;

    .el-form-item {
      margin-bottom: 0;
      margin-right: 16px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }



  @media (max-width: 1200px) {
    flex-direction: column;
    gap: 16px;

    .query-form {
      margin-right: 0;
    }

   
  }
}

// 展开内容样式
.expand-content {
  padding: 20px;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-radius: 8px;
  margin: 12px 16px;
  border: 1px solid #b3e0ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);

  .expand-detail {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .detail-row {
      display: flex;
      gap: 16px;

      .detail-item {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: linear-gradient(135deg, #ffffff 0%, #fafcff 100%);
        border-radius: 8px;
        border: 1px solid #d1e9ff;
        box-shadow: 0 2px 4px rgba(24, 144, 255, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        white-space: nowrap;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: linear-gradient(180deg, #1890ff 0%, #40a9ff 100%);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          border-color: #91d5ff;
        }

        .label {
          font-weight: 600;
          color: #1890ff;
          min-width: 70px;
          font-size: 13px;
          text-shadow: 0 1px 2px rgba(24, 144, 255, 0.1);
          flex-shrink: 0;
        }

        .value {
          color: #262626;
          font-size: 12px;
          font-weight: 500;
          margin-right: 6px;
          background-color: #f6ffed;
          padding: 2px 6px;
          border-radius: 4px;
          border: 1px solid #d9f7be;
          flex-shrink: 0;
        }

        .info {
          color: #8c8c8c;
          font-size: 12px;
          font-style: italic;
          flex-shrink: 0;
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 16px;
    margin: 8px 12px;

    .expand-detail {
      gap: 10px;

      .detail-row {
        flex-direction: column;
        gap: 10px;

        .detail-item {
          padding: 8px 12px;
          gap: 6px;

          .label {
            min-width: 60px;
            font-size: 12px;
          }

          .value {
            font-size: 11px;
            margin-right: 4px;
            padding: 1px 4px;
          }

          .info {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// 分页导航样式
.pagination-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 25px 20px;
  margin: 20px 0 0 0;
  border-top: 1px solid #e8e8e8;
  background-color: #fafafa;
  text-align: center;
  box-sizing: border-box;

  .el-pagination {
    margin: 0 auto !important;
    display: inline-flex !important;
    align-items: center;
    justify-content: center;
    position: static !important;
    right: auto !important;
    left: auto !important;

    .el-pagination__total {
      color: #606266;
      font-weight: 500;
      font-size: 13px;
    }

    .el-pagination__sizes {
      .el-select {
        .el-input__inner {
          border-radius: 6px;
          font-size: 13px;
          height: 32px;
          line-height: 30px;
        }
      }
    }

    .btn-prev,
    .btn-next {
      border-radius: 6px;
      transition: all 0.3s ease;
      padding: 0 12px;
      height: 32px;
      line-height: 30px;

      &:hover {
        background-color: #409eff;
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }

    .el-pager {
      li {
        border-radius: 6px;
        margin: 0 3px;
        transition: all 0.3s ease;
        height: 32px;
        line-height: 30px;
        min-width: 32px;

        &:hover {
          background-color: #f5f7fa;
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background-color: #409eff;
          color: white;
          font-weight: 600;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
        }
      }
    }

    .el-pagination__jump {
      color: #606266;
      font-size: 13px;

      .el-input__inner {
        border-radius: 6px;
        text-align: center;
        width: 50px;
        height: 32px;
        line-height: 30px;
      }
    }

    .el-pagination__classifier {
      font-size: 13px;
      color: #606266;
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    padding: 20px 15px;

    .el-pagination {
      .el-pagination__jump {
        .el-input__inner {
          width: 45px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    padding: 15px 10px;

    .el-pagination {
      .el-pagination__sizes,
      .el-pagination__jump {
        display: none;
      }

      .btn-prev,
      .btn-next {
        padding: 0 8px;
        height: 28px;
        line-height: 26px;
        font-size: 12px;
      }

      .el-pager li {
        height: 28px;
        line-height: 26px;
        min-width: 28px;
        margin: 0 2px;
        font-size: 12px;
      }

      .el-pagination__total {
        font-size: 12px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px 8px;

    .el-pagination {
      .el-pagination__total {
        display: none;
      }

      .btn-prev,
      .btn-next {
        padding: 0 6px;
        height: 26px;
        line-height: 24px;
      }

      .el-pager li {
        height: 26px;
        line-height: 24px;
        min-width: 26px;
        margin: 0 1px;
      }
    }
  }
}

// 简洁表单样式
.simple-form {
  .el-form-item {
    margin-bottom: 18px;
  }

  .el-form-item__label {
    font-weight: 500;
    color: #606266;
  }

  .el-input__inner,
  .el-textarea__inner {
    border-radius: 4px;
  }
}

// 计划表格容器样式
.plan-table-container {
  .basic-info-row {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .form-item {
      margin-bottom: 0;

      .form-label {
        display: block;
        margin-bottom: 5px;
        font-size: 13px;
        font-weight: 500;
        color: #606266;

        .required {
          color: #f56c6c;
          margin-left: 2px;
        }
      }

      .button-group {
        display: flex;
        gap: 12px;

        .el-button {
          width: 80px;
          flex: none;
        }
      }
    }
  }

  // 并排表格容器
  .tables-container {
    display: flex;
    gap: 20px;

    @media (max-width: 1200px) {
      flex-direction: column;
      gap: 15px;
    }
  }

  .plan-data-table {
    &.half-width {
      flex: 1;
      min-width: 0; // 防止flex项目溢出
    }

    .table-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
      padding: 12px 16px;
      background: linear-gradient(135deg, #f5f7fa 0%, #e8eaf6 100%);
      border: 1px solid #d1d5db;
      border-radius: 6px;
      text-align: center;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      i {
        margin-right: 6px;
        color: #409eff;
      }
    }

    .production-plan-table {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid #dcdfe6;
      font-size: 12px;

      &.compact {
        font-size: 11px;
      }

      th, td {
        border: 1px solid #dcdfe6;
        padding: 6px 3px;
        text-align: center;
        vertical-align: middle;
      }

      th {
        background-color: #f5f7fa;
        font-weight: bold;
        color: #303133;
        font-size: 11px;
      }

      .center-header {
        font-weight: bold;
        width: 60px;
      }

      .group-header {
        font-weight: bold;
        font-size: 11px;
      }

      .sub-header {
        font-size: 10px;
        width: 60px;
      }

      .center-cell {
        background-color: #fafafa;
        font-weight: 500;
        color: #303133;
        width: 60px;
        font-size: 11px;
      }

      .input-cell {
        padding: 1px;
        width: 60px;
        background-color: #fff;

        .el-input-number {
          width: 100%;

          .el-input__inner {
            padding: 0 4px;
            text-align: center;
            font-size: 11px;
            height: 26px;
            line-height: 26px;
            border: none !important;
            background-color: transparent;
            box-shadow: none !important;

            &:focus {
              border: none !important;
              box-shadow: none !important;
              outline: none;
            }

            &:hover {
              border: none !important;
              box-shadow: none !important;
            }
          }

          .el-input-number__decrease,
          .el-input-number__increase {
            display: none;
          }
        }
      }

      .display-cell {
        background-color: #fafafa;
        color: #666;
        font-size: 11px;
        width: 60px;
      }

      .total-row {
        .total-cell {
          background-color: #f5f7fa;
          font-weight: bold;
          color: #303133;
          font-size: 11px;
        }
      }

      tbody tr:hover {
        background-color: #f9f9f9;
      }
    }
  }
}



.basic-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}



// 弹窗样式
.plan-create-dialog {
  .el-dialog {
    border-radius: 8px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    .el-dialog__close {
      color: white;
      font-size: 20px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
    background-color: #fafbfc;
  }

  .el-dialog__footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 16px 24px;
  }

  .dialog-footer {
    text-align: center;

    .el-button {
      margin: 0 8px;
      min-width: 90px;
      font-weight: 500;
    }
  }
}

// 基本信息区域样式
.basic-info-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .basic-form {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

// 生产计划表格区域样式
.plan-tables-section {
  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 16px;
    padding-left: 12px;
    border-left: 4px solid #409eff;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .plan-create-dialog {
    .el-dialog {
      width: 95% !important;
      margin: 0 auto;
    }
  }
}

// 表单项样式优化
.el-form-item {
  .el-input-number {
    .el-input__inner {
      text-align: left;
    }
  }
}

// 卡片头部样式
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

// 输入框后缀样式
.el-input-group__append {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #909399;
  font-size: 12px;
}

// 表格样式
::v-deep .vxe-table {
  font-size: 13px;

  .vxe-header--column {
    background-color: #f5f7fa;
    font-weight: bold;
  }

  .vxe-body--row:hover {
    background-color: #f5f7fa;
  }
}

// 操作按钮样式
::v-deep .vxe-table .el-button--text {
  padding: 0;
  margin: 0 4px;
  font-size: 12px;
}

// 标签样式
::v-deep .el-tag--mini {
  font-size: 11px;
}

// 表格中输入框样式 - 去掉边框
::v-deep .production-plan-table .input-cell .el-input-number .el-input__inner {
  border: none !important;
  box-shadow: none !important;
  background-color: transparent !important;

  &:focus {
    border: none !important;
    box-shadow: none !important;
    outline: none !important;
  }

  &:hover {
    border: none !important;
    box-shadow: none !important;
  }
}

// 隐藏数字输入框的加减按钮
::v-deep .production-plan-table .input-cell .el-input-number__decrease,
::v-deep .production-plan-table .input-cell .el-input-number__increase {
  display: none !important;
}

// 紧凑表格的额外样式
::v-deep .production-plan-table.compact .input-cell .el-input-number .el-input__inner {
  font-size: 11px !important;
  height: 26px !important;
  line-height: 26px !important;
  padding: 0 4px !important;
}
</style>