<template>
  <div class="add-material-form">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-form-item label="物料名称" prop="materialName">
        <el-input v-model="form.materialName" placeholder="请输入物料名称" />
      </el-form-item>

      <el-form-item label="物料编码" prop="materialCode">
        <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重量(T)" prop="weight">
            <el-input-number 
              v-model="form.weight" 
              :min="0" 
              :max="9999" 
              :precision="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密度" prop="density">
            <el-input-number 
              v-model="form.density" 
              :min="0" 
              :max="10" 
              :precision="2"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="位置(px)" prop="x">
            <el-input-number 
              v-model="form.x" 
              :min="0" 
              :max="2000" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="宽度(px)" prop="w">
            <el-input-number 
              v-model="form.w" 
              :min="20" 
              :max="500" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="供应商" prop="supplier">
        <el-select v-model="form.supplier" placeholder="请选择供应商" style="width: 100%">
          <el-option label="澳洲矿业" value="澳洲矿业" />
          <el-option label="巴西矿业" value="巴西矿业" />
          <el-option label="焦化厂A" value="焦化厂A" />
          <el-option label="石灰石厂B" value="石灰石厂B" />
          <el-option label="其他" value="其他" />
        </el-select>
      </el-form-item>

      <el-form-item label="到货时间" prop="arrivalTime">
        <el-date-picker
          v-model="form.arrivalTime"
          type="datetime"
          placeholder="选择到货时间"
          style="width: 100%"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <!-- 预设模板 -->
      <div class="templates">
        <h4>快速模板</h4>
        <el-row :gutter="10">
          <el-col :span="6" v-for="template in templates" :key="template.name">
            <el-button 
              size="small" 
              type="info" 
              plain 
              @click="applyTemplate(template)"
              style="width: 100%; margin-bottom: 8px;"
            >
              {{ template.name }}
            </el-button>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button @click="handleReset">重 置</el-button>
      <el-button type="primary" @click="handleSave">添 加</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AddMaterialForm',
  data() {
    return {
      form: {
        materialName: '',
        materialCode: '',
        weight: 100,
        density: 1.5,
        x: 600,
        w: 80,
        supplier: '',
        arrivalTime: '',
        remark: ''
      },
      rules: {
        materialName: [
          { required: true, message: '请输入物料名称', trigger: 'blur' }
        ],
        materialCode: [
          { required: true, message: '请输入物料编码', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' }
        ],
        density: [
          { required: true, message: '请输入密度', trigger: 'blur' }
        ]
      },
      templates: [
        {
          name: '铁矿石',
          materialName: '铁矿石',
          materialCode: 'IRN',
          weight: 500,
          density: 2.1,
          supplier: '澳洲矿业'
        },
        {
          name: '焦炭',
          materialName: '焦炭',
          materialCode: 'COK',
          weight: 200,
          density: 0.8,
          supplier: '焦化厂A'
        },
        {
          name: '石灰石',
          materialName: '石灰石',
          materialCode: 'LIM',
          weight: 150,
          density: 1.2,
          supplier: '石灰石厂B'
        },
        {
          name: '烧结矿',
          materialName: '烧结矿',
          materialCode: 'SIN',
          weight: 300,
          density: 1.8,
          supplier: '本厂'
        }
      ]
    }
  },
  mounted() {
    // 设置默认到货时间为当前时间
    this.form.arrivalTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
  },
  methods: {
    applyTemplate(template) {
      // 应用模板，但保留位置和宽度
      const currentX = this.form.x;
      const currentW = this.form.w;
      
      this.form = {
        ...this.form,
        ...template,
        x: currentX,
        w: currentW,
        materialCode: template.materialCode + this.generateRandomSuffix(),
        arrivalTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };
      
      this.$message.success(`已应用${template.name}模板`);
    },
    
    generateRandomSuffix() {
      return String(Math.floor(Math.random() * 1000)).padStart(3, '0');
    },
    
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit('save', this.form);
        } else {
          this.$message.error('请检查表单数据');
        }
      });
    },
    
    handleCancel() {
      this.$emit('cancel');
    },
    
    handleReset() {
      this.$refs.form.resetFields();
      this.form.arrivalTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
      this.$message.info('表单已重置');
    }
  }
}
</script>

<style lang="scss" scoped>
.add-material-form {
  .templates {
    margin: 20px 0;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }
    
    .el-button {
      font-size: 12px;
      height: 28px;
      
      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  .dialog-footer {
    margin-top: 20px;
    text-align: right;
    
    .el-button {
      margin-left: 10px;
    }
  }
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  border-radius: 4px;
}

::v-deep .el-input-number {
  width: 100%;
}
</style>
