<template>
  <div ref="lineChart" style="width: 1600px; height: 380px;"></div>
</template>

<script>
  import * as echarts from 'echarts';

  export default {
    name: 'LineChart',
    props: {
      LineChartData: {
        type: Object,
        default: () => {}
      }
    },
    mounted() {
      this.initChart();
    },
    methods: {
      initChart() {
        // 基于准备好的dom，初始化echarts实例
        const myChart = echarts.init(this.$refs.lineChart);

        // 指定图表的配置项和数据
        const option = {
          title: {
            text: this.LineChartData.description || []
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            // data:  ['销量']
            data:  [this.LineChartData.legendName]
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.LineChartData.xData || []
          },
          yAxis:[
            {
              type: 'value',
              position: 'left',
              alignTicks: true,  // 使两个 y 轴对齐刻度线，可选
              axisLine: {
                lineStyle: {
                  color: '#333'
                }
              },
            },
            {
              type: 'value',
              position: 'right', // 将第二个 y 轴放在右侧
              alignTicks: true,  // 使两个 y 轴对齐刻度线，可选
              axisLine: {
                lineStyle: {
                  color: '#2f4554'
                }
              },
            },
          ],

          series: [
            {
              name: this.LineChartData.seriesName1,
              type: 'line',
              stack: '总量',
              yAxisIndex: 0, // 使用第一个Y轴（索引为0）
              smooth: true, // 使折线平滑显示，可选属性，默认为false，即折线是直线。true为曲线。
              // areaStyle: {}, //  图像填充阴影
              // emphasis: {
              //   focus: 'series'
              // },
              data:this.LineChartData.yData || []
            },
            {
              name: '系列2',
              type: 'line',
              smooth: true, // 使折线平滑显示，可选属性，默认为false，即折线是直线。true为曲线。
              yAxisIndex: 1, // 使用第二个Y轴（索引为1）
              data:this.LineChartData.yData1 || []
            }
          ]
        };

        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
      },

    },
    watch: {
      LineChartData: {
        deep: true,
        handler(newVal) {
          this.$nextTick(() => {
            this.initChart(); // 重新初始化图表以应用新的数据
          });
        }
      }
    }
  };
</script>


