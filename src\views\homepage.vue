<template>
  <div class="dashboard-container">


  </div>
</template>

<script>
import CountTo from 'vue-count-to'
export default {
  name: "homepage",
  components: {
    CountTo
  },
  data() {
    return {


    };
  },
  created() {

  },
  mounted() {


  },
  methods: {

  }
};
</script>
<style lang="scss" scoped>
.panel-group {
  margin-top: 10px;

  .card-panel-col {
    // margin-bottom: 10px;
    padding-left: 10px;
  }

  .card-panel {
    padding-top: 10px;
    padding-bottom: 10px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);


    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }



    .card-panel-icon-wrapper {
      float: left;
      margin: 10px 0 0 10px;
      padding: 5px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 55px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 10px;
      margin-left: 0px;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 18px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 18px;
      }
    }
  }
}

::v-deep .tableInfo .el-table .el-table__cell {
  padding-top: 3px;
  padding-bottom: 2px;
}
</style>
