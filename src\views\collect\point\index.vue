<template>
  <div class="app-container" style="padding: 10px;">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="存储编号" prop="storeCode">
        <el-select v-model="queryParams.storeCode" placeholder="请输入存储编号" clearable @keyup.enter.native="handleQuery">
          <el-option v-for="item in storeList" :key="item.storeCode" :label="item.storeName" :value="item.storeCode">
            <span style="float: left">{{ item.storeName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{ item.storeCode }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位工序" prop="pointProcess">
        <el-select v-model="queryParams.pointProcess" placeholder="请输入点位业务" allow-create clearable filterable
          @keyup.enter.native="handleQuery">
          <el-option v-for="item in processList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位业务" prop="pointOperation">
        <el-select v-model="queryParams.pointOperation" placeholder="请输入点位工序" allow-create clearable filterable
          @keyup.enter.native="handleQuery">
          <el-option v-for="item in operList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="点位编号" prop="pointCode">
        <el-input v-model="queryParams.pointCode" placeholder="请输入点位编号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="点位名称" prop="pointName">
        <el-input v-model="queryParams.pointName" placeholder="请输入点位名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="点位描述" prop="pointDesc">
        <el-input v-model="queryParams.pointDesc" placeholder="请输入点位名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_new_table">
      <dlTable refName="dlTable" :stripe="true" :border="true" :height="dltableHeight" :columns="columns"
        :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig" @handleOrder="getList"
        @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
        @page-current-change="numChange">
      </dlTable>
    </div>

    <!-- 添加或修改点位配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="存储编号" prop="storeCode">
          <el-select v-model="form.storeCode" placeholder="请输入存储编号" style="width:100%">
            <el-option v-for="item in storeList" :key="item.storeCode" :label="item.storeName" :value="item.storeCode">
              <span style="float: left">{{ item.storeName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.storeCode }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位工序" prop="pointProcess">
          <el-select v-model="form.pointProcess" placeholder="请输入点位业务" allow-create clearable filterable
            style="width:100%" @keyup.enter.native="handleQuery">
            <el-option v-for="item in processList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位业务" prop="pointOperation">
          <el-select v-model="form.pointOperation" placeholder="请输入点位工序" allow-create clearable filterable
            style="width:100%" @keyup.enter.native="handleQuery">
            <el-option v-for="item in operList" :key="item" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点位编号" prop="pointCode">
          <el-input v-model="form.pointCode" placeholder="请输入点位编号" />
        </el-form-item>
        <el-form-item label="点位名称" prop="pointName">
          <el-input v-model="form.pointName" placeholder="请输入点位名称" />
        </el-form-item>
        <el-form-item label="开始采集时间" prop="startCollectionTime">
          <el-date-picker clearable v-model="form.startCollectionTime" type="datetime" placeholder="请选择开始采集时间"
            style="width:100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择包含内容" style="width: 100%;">
            <el-option v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url " :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-alert title="1、根据点位编码区分新增和更行" type="warning" :closable="false" />
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板
          </el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import {
  listPoint,
  getPoint,
  getProcess,
  getOperList,
  saveOrUpdate,
  delPoint
} from "@/api/collect/point";
import {
  getAllList
} from "@/api/collect/store";
import dayjs from 'dayjs';

export default {
  name: "CollectPoint",
  dicts: ["record_status"],
  data() {
    return {
      storeList: [],
      processList: [],
      operList: [],
      dltableHeight: 660,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 点位配置表格数据
      pointList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        storeCode: null,
        pointCode: null,
        pointName: null,
        pointProcess: null,
        pointOperation: null,
        pointDesc: null
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/api/collect/point/importData'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        storeCode: [
          {
            required: true, message: "存储编号 不能为空", trigger: "blur"
          }
        ],
        pointProcess: [
          {
            required: true, message: "点位工序 不能为空", trigger: "blur"
          }
        ],
        pointOperation: [
          {
            required: true, message: "点位业务 不能为空", trigger: "blur"
          }
        ],
        pointCode: [
          {
            required: true, message: "点位编号 不能为空", trigger: "blur"
          }
        ],
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: false // 表头排序是否保存在localStorage中
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      columns: [
        {
          label: '存储编号', // 表头描述
          fieldIndex: 'storeCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STORE_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位工序', // 表头描述
          fieldIndex: 'pointProcess', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_PROCESS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位业务', // 表头描述
          fieldIndex: 'pointOperation', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_OPERATION',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位编号', // 表头描述
          fieldIndex: 'pointCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位名称', // 表头描述
          fieldIndex: 'pointName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },


        {
          label: '开始采集时间', // 表头描述
          fieldIndex: 'startCollectionTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'START_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '最后采集时间', // 表头描述
          fieldIndex: 'lastCollectionTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'LAST_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '状态', // 表头描述
          fieldIndex: 'status', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'STATUS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'REMARK',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '点位描述', // 表头描述
          fieldIndex: 'pointDesc', // 表格显示内容绑定值
          width: 300,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'POINT_DESC',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建人', // 表头描述
          fieldIndex: 'createdBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建时间', // 表头描述
          fieldIndex: 'createdTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'CREATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新人', // 表头描述
          fieldIndex: 'updatedBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_BY',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新时间', // 表头描述
          fieldIndex: 'updatedTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: 'UPDATED_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        }

      ],
      tableData: [],
      selectVO: '',
    };
  },
  created() {
    getAllList().then(response => {
      this.storeList = response.data;
    });
    getProcess().then(response => {
      this.processList = response.data;
    });
    getOperList().then(response => {
      this.operList = response.data;
    });
    this.getList(null);
  },
  methods: {
    /** pageNum事件 */
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum;
      this.queryParams.pageNum = pageNum;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize;
      this.queryParams.pageSize = pageSize;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** 查询点位配置列表 */
    getList(selectVO) {
      this.loading = true;
      if (selectVO) {
        this.selectVO = selectVO;
      }
      this.queryList();
    },
    queryList() {
      listPoint(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.rows
        this.pageConfig.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        pointId: null,
        storeCode: null,
        pointCode: null,
        pointName: null,
        pointProcess: null,
        pointOperation: null,
        startCollectionTime: dayjs().add(-1, 'hour').format('YYYY-MM-DD HH:mm:ss'),
        status: "生效",
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.pointId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加点位配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const pointId = row.pointId || this.ids
      getPoint(pointId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改点位配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form))
          obj.startCollectionTime = dayjs(obj.obj).add(-1, 'hour').format('YYYY-MM-DD HH:mm:ss')
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess('保存成功')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const pointId = row.pointId || this.ids
      this.$modal
        .confirm(
          '确定删除吗?'
        )
        .then(function () {
          return delPoint(pointId)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/api/collect/point/export', {
        ...this.queryParams
      }, `point_${new Date().getTime()}.xlsx`)
    },
     /** 导入按钮操作 */
     handleImport() {
      this.upload.title = '点位配置导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('/api/collect/point/importTemplate', {}, `点位配置-导入模版-${new Date().getTime()}.xlsx`)
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      this.$alert('<div style=\'overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;\'>' + response.msg + '</div>', '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.$nextTick(() => {
        /*mes_new_table 到顶部的高炉 */
        let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
        /*mes_new_table 屏幕高度-mes_new_table到顶部的高炉-50（分页控件） */
        this.dltableHeight = document.body.clientHeight - topValue - 50;
      })
    })
  },
}
  ;
</script>
