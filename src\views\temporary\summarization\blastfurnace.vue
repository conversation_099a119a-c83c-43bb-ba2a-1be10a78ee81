<template>
  <div class="sum-container">
    <div class="sum-content-up">
      <div class="sum-content-up01">
        <div class="sum-content-up01-left">
            <ECharts4 :title="title4" :type="type4"></ECharts4>
        </div>
        <div class="sum-content-up01-right">
          <div class="right-son01">
            <EChartsH></EChartsH>
          </div>
          <div class="right-son02">
            <EChartsG></EChartsG>
          </div>
        </div>
      </div>
      <div class="sum-content-up02">
        <div class="sum-content-up02-left">
          <EChartsB></EChartsB>
        </div>
        <div class="sum-content-up02-right">
          <EChartsLB></EChartsLB>
        </div>
      </div>
    </div>
    <div class="sum-content-down">
      <EChartsL :title="titleL" :type="type4"></EChartsL>
    </div>
  </div>
</template>
    
<script>
import EChartsH from '@/components/BlastFurnaceCurve/echartsH'
import EChartsG from '@/components/BlastFurnaceCurve/echartGauge'
import EChartsLB from '@/components/BlastFurnaceCurve/echartsLB'
import EChartsL from '@/components/BlastFurnaceCurve/echartsL'
import EChartsB from '@/components/BlastFurnaceCurve/echartsB'
import ECharts4 from '@/components/BlastFurnaceCurve/echarts4'

export default {
  components: {ECharts4, EChartsH, EChartsG,EChartsLB,EChartsL,EChartsB},
  data() {
    return {
      title4: '铁水产量数据总览',
      titleL: '原料消耗',
      type4: '高炉'
    }
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
  },
  watch: {
  }
}
</script>
<style scoped>
.sum-container {
  padding: 20px 15px;
  background-color: #f7f8fa;
}
.sum-content-up {
  height: 65vh;
  width: 100%;
  display: flex;
  position: sticky;
  flex-direction: column;
  flex-wrap: nowrap;
}
.sum-content-up01 {
  width: 100%;
  height: 66.25%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.sum-content-up01-left,
.sum-content-up02-left {
  flex: 1;
  height: 100%;
  background-color: #fff;
  border-radius: 5px;
  margin: 0 5px 0 0;
}
.sum-content-up01-right,
.sum-content-up02-right {
  width: 500px;
  height: 100%;
  display: flex;
  position: sticky;
  flex-direction: column;
  flex-wrap: nowrap;
  margin: 0 0 0 5px;
}
.right-son01,
.right-son02 {
  width: 100%;
  height: 49%;
  background-color: #fff;
  border-radius: 5px;
}
.right-son02 {
  margin: 2% 0 0 0;
}
.sum-content-up02 {
  width: 100%;
  height: 33.25%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  margin: 0.5% 0 0 0;
}
.sum-content-up02-right {
  background-color: #fff;
  border-radius: 5px;
}
.sum-content-down {
  height: 30vh;
  width: 100%;
  display: flex;
  position: sticky;
  margin: 15px 0 0 0;
  background-color: #fff;
  border-radius: 5px;
  border: 1px solid #e9eaed;
}
</style>