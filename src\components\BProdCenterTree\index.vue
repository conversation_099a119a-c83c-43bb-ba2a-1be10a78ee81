<template>
  <el-table class="prodTable" row-key="prodCenterId" highlight-current-row :height="height"
            :data="productcenterList" :border="true"
            @current-change="handleCurrentChange"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
  >
    <el-table-column min-width="150" fixed="left" label="编码" align="center" prop="prodCenterCode"/>
    <el-table-column min-width="150" fixed="left" label="名称" align="center" prop="prodCenterName"/>
  </el-table>
</template>
<script>
import { listProductcenter } from '@/api/md/productcenter'

export default {
  name: 'BProdCenterTree',
  props: {
    height: {
      default: 400
    }
  },
  data() {
    return {
      productcenterList: [],
      currentProdCenter: null,
    }
  },

  created() {
    var prodQueryparm = { status: '生效' }
    listProductcenter(prodQueryparm).then(response => {
      this.productcenterList = this.handleTree(response.data, 'prodCenterId')
    })
  },

  methods: {
    handleCurrentChange(selection) {
      this.currentProdCenter = selection;
      this.$emit('currentChange', this.currentProdCenter);
    },
  },
}
</script>
