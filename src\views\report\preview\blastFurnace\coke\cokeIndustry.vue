<template>
  <div class="app-container" style="padding: 0px;margin-left: 20px;">
    <div class="block" >
        <span >发布时间:</span>
        <el-date-picker style="margin-left: 10px;width: 392px;"
                        v-model="publishTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        <el-button type="primary" icon="el-icon-search" style="margin-left: 100px;" @click="handleQuery" size="mini">搜索</el-button>
    </div>
    <div>
      <vxe-table
        style="margin-left: 20px;margin-right: 20px;"
        align="center"
        border
        height="600"
        :data="tableData">
        <vxe-column field="mateName" title="物料名称"></vxe-column>
        <vxe-column field="publishTime" title="发布时间" width="auto"></vxe-column>
        <vxe-column field="supplierName" title="供应商"></vxe-column>
        <vxe-column field="a" title="A"></vxe-column>
        <vxe-column field="v" title="V"></vxe-column>
        <vxe-column field="c固" title="C固"></vxe-column>
        <vxe-column field="s" title="S"></vxe-column>
        <vxe-column field="h2O" title="H2O"></vxe-column>
        <vxe-column field="csr" title="CSR"></vxe-column>
        <vxe-column field="cri" title="CRI"></vxe-column>
        <vxe-column field="m40" title="M40"></vxe-column>
        <vxe-column field="m25" title="M25"></vxe-column>
        <vxe-column field="m10" title="M10"></vxe-column>
      </vxe-table>
    </div>

  </div>
</template>

<script>
  import {
    cokeIndustryList,
  } from "@/api/report/preview/blastFurnace/cokeIndustry";
  import dayjs from "dayjs";
    export default {
        name: "cokeIndustry",
      data(){
          return{
            tableData:[],
            publishTimeArr:[],
            selectParam:{
              startTime:'',
              endTime:'',
            }

          }
      },
      created() {
        this.publishTimeArr.push(dayjs(new Date()).add(-1, "day"));
        this.publishTimeArr.push(dayjs(new Date()).add(1, "day"));

        this.queryList();
      },
      methods:{

        /*搜索按钮*/
        handleQuery(){
          if(this.publishTimeArr != null){
            if (this.publishTimeArr.length == 2) {
              this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          this.queryList();
        },

          /* 查询数据列表 */
          queryList(){
            if(this.publishTimeArr != null){
              if (this.publishTimeArr.length == 2) {
                this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
                  "YYYY-MM-DD HH:mm:ss"
                );
                this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
                  "YYYY-MM-DD HH:mm:ss"
                );
              }
            }
            console.log("this.selectParam:",this.selectParam)
            cokeIndustryList(this.selectParam).then(response=>{
              this.tableData = response.data
              console.log("response:",JSON.stringify(response))
            });
          }

      }
    }
</script>

<style scoped>
  .block{
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 10px;
  }

</style>
