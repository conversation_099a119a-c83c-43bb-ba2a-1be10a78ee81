<template>
  <div class="echartsLB_box">
    <div class="title-bar">
      <div class="title-group">
        <div class="title-text" :class="{active: active === '点火温度'}" @click="chagngePrecent('点火温度')">点火温度</div>
        <div class="title-text" :class="{active: active === '终点温度'}" @click="chagngePrecent('终点温度')">终点温度</div>
        <div class="title-text" :class="{active: active === '煤气流量'}" @click="chagngePrecent('煤气流量')">煤气流量</div>
        <div class="title-text long" :class="{active: active === '主管废气温度'}" @click="chagngePrecent('主管废气温度')">主管废气温度</div>
        <div class="title-text long" :class="{active: active === '主管废气压力'}" @click="chagngePrecent('主管废气压力')">主管废气压力</div>
        <div class="title-text" :class="{active: active === '混合料温度'}" @click="chagngePrecent('混合料温度')">混合料温度</div>
        <div class="title-text" :class="{active: active === '混合料水分'}" @click="chagngePrecent('混合料水分')">混合料水分</div>
      </div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一烧结'}" @click="chagngeGl('一烧结')">一烧结</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二烧结'}" @click="chagngeGl('二烧结')">二烧结</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsLs" class="echartsLs"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { cpesScreenMetrics } from "@/api/analyse/sintering";

export default {
  name: 'EChartsLs',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      active: '点火温度',
      activeGL: '一烧结',
      chart: null,
      param: {
        prodCenterCode: 'CPES01',
        type: '1',
      },
      minLine: 950,
      maxLine: 1050,
      isWinter: false
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    initValue() {
      cpesScreenMetrics(this.param).then(res => {
        if (res.code === 200) {
          var xData = [];
          var yData = [];
          var yData1 = [];
          var minLineMark = 0;
          var maxLineMark = 0;
          minLineMark = this.minLine;
          maxLineMark = this.maxLine;
          var data = res.data;
          if (this.param.type == '4' || this.param.type == '5') {
            var dataNew1 = data.主管废气温度1 || data.主管废气负压1;
            var dataNew2 = data.主管废气温度2 || data.主管废气负压2;
            const result1 = Object.keys(dataNew1).map(key => ({
              date: key,
              value: dataNew1[key]
            }));
            const result2 = Object.keys(dataNew2).map(key => ({
              date: key,
              value: dataNew2[key]
            }));
            result1.forEach((item, index) => {
              xData[index] = item.date || 0;
              yData[index] = Number(item.value).toFixed(2) || 0;
            });
            result2.forEach((item, index) => {
              xData[index] = item.date || 0;
              yData1[index] = Number(item.value).toFixed(2) || 0;
            });
          } else {
            const result = Object.keys(data).map(key => ({
              date: key,
              value: data[key]
            }));
            result.forEach((item, index) => {
              xData[index] = item.date || 0;
              yData[index] = Number(item.value).toFixed(2) || 0;
            });
          }
          this.isWinter = this.isDateInRange(yData[0], 11, 3);
          if (this.isWinter) {
            console.log(this.isWinter ? "在范围内" : "不在范围内");
            if (this.active == '混合料温度') {
              minLineMark = 50;
            }
            if (this.active == '主管废气温度') {
              minLineMark = 120;
            }
          }
          this.$nextTick(() => {
            this.initChart(xData, yData, yData1, minLineMark, maxLineMark);
            window.addEventListener('resize', this.resizeChart);
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(xData, yData, yData1, minLineMark, maxLineMark) {
      let that = this;
      const chartDom = this.$refs.echartsLs;
      this.chart = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: xData.length > 0 && yData.length > 0
          }]
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            // let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getFullYear()} - ${date.getMonth() + 1}-${date.getDate()}</div>`;
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;"> ${params[0].name} </div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;"> ${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        grid: {
          top: '8%',
          left: '1.5%',
          right: '3%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#DCDDDE',
              width: 2
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#3E4B5C',
              fontSize: 12
            },
            formatter: function (value) {
              return echarts.format.formatTime('hh:mm', value);
            }
          },
          axisTick: { show: true },
          splitLine: { show: false },
          data: xData
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#979A9F',
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F3F3F4'
            }
          },
        },
        series: [
          {
            name: yData1.length > 0 ? '主管废气1' : '',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#21CCFF'
            },
            data: yData,
            markLine: {
              symbol: 'none',
              data: [
                {
                  yAxis: minLineMark,
                  name: '下限',
                  label: {
                    show: true,
                    position: 'end',
                    //formatter: '下限' + `{c}`,
                    formatter: `{c}`,
                    color: '#3D3D3D',
                    fontSize: 13,
                  },
                  lineStyle: {
                    type: 'dashed',
                    color: '#FF0000',
                    width: 3,
                  },
                },
                {
                  yAxis: maxLineMark,
                  name: '上限',
                  label: {
                    show: maxLineMark == '-' ? false : true,
                    position: 'end',
                    //formatter: maxLineMark == '-' ? '基准线' + `{c}` : '上限' + `{c}`,
                    formatter: `{c}`,
                    color: '#3D3D3D',
                    fontSize: 13,
                  },
                  lineStyle: {
                    type: 'dashed',
                    color: '#FF0000',
                    width: maxLineMark == '-' ? 0 : 3,
                  },
                },
              ],
            },
          },
          {
            name: yData1.length > 0 ? '主管废气2' : '',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: '#FFAECE'
            },
            data: yData1,
          }
        ],
      };
      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    chagngePrecent(name) {
      this.active = name
      if (this.active == '点火温度') {
        this.param.type = '1'
        this.minLine = 950;
        this.maxLine = 1050;
        this.initValue()
      }
      if (this.active == '终点温度') {
        this.param.type = '2'
        this.minLine = 380;
        this.maxLine = '-';
        this.initValue()
      }
      if (this.active == '煤气流量') {
        this.param.type = '3'
        this.minLine = 1200;
        this.maxLine = 1400;
        this.initValue()
      }
      if (this.active == '主管废气温度') {
        this.param.type = '4'
        this.minLine = 115;
        this.maxLine = 160;
        this.initValue()
      }
      if (this.active == '主管废气压力') {
        this.param.type = '5'
        this.minLine = 15000;
        this.maxLine = 30000;
        this.initValue()
      }
      if (this.active == '混合料温度') {
        this.param.type = '6'
        this.minLine = 60;
        this.maxLine = '-';
        this.initValue()
      }
      if (this.active == '混合料水分') {
        this.param.type = '7'
        this.minLine = 7.6;
        this.maxLine = 8.0;
        this.initValue()
      }
    },
    chagngeGl(name) {
      this.activeGL = name
      if (this.activeGL == '一烧结') {
        this.param.prodCenterCode = 'CPES01';
        this.initValue()
      }
      if (this.activeGL == '二烧结') {
        this.param.prodCenterCode = 'CPES02';
        this.initValue()
      }
    },
    /** 判断日期是否在指定范围内 */
    isDateInRange(targetDate, startMonth, endMonth) {
      // 将目标日期字符串转换为 Date 对象
      const date = new Date(targetDate);
      // 创建范围的起始和结束日期
      const startYear = date.getFullYear();
      const endYear = date.getFullYear() + (endMonth < startMonth ? 1 : 0);
      const startDate = new Date(startYear, startMonth - 1, 1); // 月份从0开始
      const endDate = new Date(endYear, endMonth, 0); // 下个月的第0天即本月的最后一天
      // 判断日期是否在范围内
      return date >= startDate && date <= endDate;
    },
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsLB_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px 0 15px;
  justify-content: space-between;
}
.title-group {
  width: 800px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  align-items: center;
  justify-content: space-between;
}
.title-text {
  width: 100px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
  cursor: pointer;
}
.title-text.long {
  width: 120px;
}
.title-text:hover,
.title-text.active {
  color: #3c83ff;
  text-decoration: underline solid #3c83ff;
  text-underline-offset: 5px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 5px 0 0 0;
}
.echartsLs {
  width: 100%;
  height: 100%;
}
</style>