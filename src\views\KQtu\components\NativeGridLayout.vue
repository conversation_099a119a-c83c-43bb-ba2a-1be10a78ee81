<template>
  <div class="native-grid-layout">
    <!-- 标题区域 -->
    <div class="grid-title" :style="{ 'writing-mode': mainTitle === '混匀' ? 'vertical-rl' : 'normal' }">
      {{ mainTitle }}
    </div>

    <!-- 主要库区区域 -->
    <div class="grid-main" ref="container">
      <!-- 计划层（如果是对比视图） -->
      <div v-if="viewMode === 'both'" class="plan-layer">
        <div
          v-for="item in layoutData"
          :key="'plan-' + item.i"
          class="material-block plan-block"
          :style="{
            transform: `translateX(${item.x1}px)`,
            width: item.w1 + 'px',
            height: '80px',
            display: item.x1 == 0 ? 'none' : 'block'
          }"
        >
          <div class="material-content">
            <div class="material-name">{{ item.materialName }}</div>
            <div class="material-label">计划</div>
          </div>
        </div>
      </div>

      <!-- 实际层 -->
      <div class="actual-layer">
        <div
          v-for="(item, index) in layoutData"
          :key="item.i"
          class="material-block actual-block"
          :class="{
            selected: selectedItem === item,
            dragging: isDragging && dragData.item === item
          }"
          :style="{
            transform: `translateX(${item.x}px)`,
            width: item.w + 'px',
            height: '80px',
            display: item.x == 0 ? 'none' : 'block'
          }"
          @contextmenu.prevent="onRightClick(index, $event, item)"
          @mouseenter="showInfo(item)"
          @mouseleave="hideInfo"
          @click="selectMaterial(item)"
          @mousedown.stop="startDrag($event, item)"
        >
          <!-- 物料信息 -->
          <div class="material-content">
            <div class="material-name">{{ item.materialName }}</div>
            <div class="material-weight">{{ item.weight || 495 }} <span>T</span></div>
          </div>

          <!-- 悬停提示 -->
          <transition name="fade">
            <div v-if="hoverItem === item" class="material-tooltip">
              <div>物料: {{ item.materialName }}</div>
              <div>位置: {{ item.x }}px</div>
              <div>宽度: {{ item.w }}px</div>
              <div>重量: {{ item.weight || 495 }}T</div>
              <div>供应商: {{ item.supplier || '-' }}</div>
            </div>
          </transition>

          <!-- 左侧调整手柄 -->
          <div
            class="resize-handle resize-handle-left"
            @mousedown.stop="startResize($event, item, 'left')"
          ></div>

          <!-- 右侧调整手柄 -->
          <div
            class="resize-handle resize-handle-right"
            @mousedown.stop="startResize($event, item, 'right')"
          ></div>
        </div>
      </div>
    </div>

    <!-- 右侧数据面板 -->
    <div 
      class="grid-right" 
      :style="{ 
        width: rightPartVisible ? '320px' : '0', 
        padding: rightPartVisible ? '0 10px 0 10px' : '0' 
      }"
    >
      <div v-if="rightPartVisible" class="data-panel">
        <div class="panel-header">
          <h3>物料数据</h3>
          <el-button size="mini" @click="exportPanelData">导出</el-button>
        </div>
        
        <div class="data-list">
          <div class="list-header">
            <span class="col-name">物料名称</span>
            <span class="col-position">位置</span>
            <span class="col-width">宽度</span>
            <span class="col-weight">重量</span>
          </div>

          <div class="list-body">
            <div
              v-for="item in layoutData"
              :key="item.i"
              class="list-item"
              :class="{ active: selectedItem === item }"
              @click="selectMaterial(item)"
            >
              <div class="col-name" :title="item.materialName">
                {{ item.materialName }}
              </div>
              <div class="col-position">
                <el-input
                  v-model.number="item.x"
                  size="mini"
                  style="width: 60px;"
                  @change="updateLayout"
                />
              </div>
              <div class="col-width">
                <el-input
                  v-model.number="item.w"
                  size="mini"
                  style="width: 60px;"
                  @change="updateLayout"
                />
              </div>
              <div class="col-weight">
                <el-input
                  v-model="item.weight"
                  size="mini"
                  style="width: 50px;"
                />T
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div 
      v-show="contextMenu.visible" 
      class="context-menu" 
      :style="{
        left: contextMenu.x + 'px',
        top: contextMenu.y + 'px'
      }"
    >
      <div class="menu-item" @click="handleMenuAction('入库')">
        <i class="el-icon-upload2"></i>入库
      </div>
      <div class="menu-item" @click="handleMenuAction('出库')">
        <i class="el-icon-download"></i>出库
      </div>
      <div class="menu-item" @click="handleMenuAction('盘点数据')">
        <i class="el-icon-data-analysis"></i>盘点数据
      </div>
      <div class="menu-item" @click="handleMenuAction('倒垛')">
        <i class="el-icon-refresh"></i>倒垛
      </div>
      <div class="menu-divider"></div>
      <div class="menu-item" @click="handleMenuAction('删除')">
        <i class="el-icon-delete"></i>删除
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NativeGridLayout',
  props: {
    layoutData: {
      type: Array,
      default: () => []
    },
    mainTitle: {
      type: String,
      default: "库区图"
    },
    rightPartVisible: {
      type: Boolean,
      default: false
    },
    viewMode: {
      type: String,
      default: 'actual' // 'plan' | 'actual' | 'both'
    }
  },
  data() {
    return {
      // 右键菜单
      contextMenu: {
        visible: false,
        x: 0,
        y: 0,
        item: null
      },
      
      // 悬停和选择
      hoverItem: null,
      selectedItem: null,
      
      // resize相关
      isResizing: false,
      resizeData: {
        item: null,
        direction: null,
        startX: 0,
        startWidth: 0,
        startPosition: 0,
        containerWidth: 0
      },

      // 拖拽移动相关
      isDragging: false,
      dragData: {
        item: null,
        startX: 0,
        startY: 0,
        startPosition: 0,
        offsetX: 0,
        offsetY: 0
      },

      // 性能优化
      rafId: null, // requestAnimationFrame ID
      lastMoveTime: 0 // 上次移动时间
    }
  },
  mounted() {
    this.updateContainerWidth();
    window.addEventListener('resize', this.updateContainerWidth);
    document.addEventListener('click', this.closeContextMenu);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateContainerWidth);
    document.removeEventListener('click', this.closeContextMenu);
    
    // 清理resize事件
    if (this.isResizing) {
      document.removeEventListener('mousemove', this.handleResize);
      document.removeEventListener('mouseup', this.stopResize);
    }

    // 清理拖拽事件
    if (this.isDragging) {
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    }

    // 清理动画帧
    if (this.rafId) {
      cancelAnimationFrame(this.rafId);
    }
  },
  methods: {
    // ==================== 基础方法 ====================
    
    /** 更新容器宽度 */
    updateContainerWidth() {
      if (this.$refs.container) {
        this.resizeData.containerWidth = this.$refs.container.clientWidth;
      }
    },

    /** 显示悬停信息 */
    showInfo(item) {
      this.hoverItem = item;
    },

    /** 隐藏悬停信息 */
    hideInfo() {
      this.hoverItem = null;
    },

    /** 选择物料 */
    selectMaterial(item) {
      this.selectedItem = item;
      this.$emit('material-selected', item);
    },

    /** 更新布局 */
    updateLayout() {
      this.$emit('layout-updated', this.layoutData);
    },

    /** 导出面板数据 */
    exportPanelData() {
      const data = this.layoutData.map(item => ({
        物料名称: item.materialName,
        位置: item.x + 'px',
        宽度: item.w + 'px',
        重量: item.weight + 'T'
      }));
      console.log('导出面板数据:', data);
      this.$message.success('数据已导出到控制台');
    },

    // ==================== Resize功能 ====================

    /** 开始调整大小 */
    startResize(event, item, direction) {
      event.preventDefault();
      event.stopPropagation();

      this.isResizing = true;
      this.resizeData = {
        item: item,
        direction: direction,
        startX: event.clientX,
        startWidth: item.w,
        startPosition: item.x,
        containerWidth: this.$refs.container.clientWidth
      };

      document.addEventListener('mousemove', this.handleResize);
      document.addEventListener('mouseup', this.stopResize);
      document.body.style.userSelect = 'none';

      console.log(`开始调整 ${direction} 侧 - 位置: ${item.x}, 宽度: ${item.w}`);
    },

    /** 处理调整大小 */
    handleResize(event) {
      if (!this.isResizing) return;

      const { item, direction, startX, startWidth, startPosition } = this.resizeData;
      const deltaX = event.clientX - startX;

      if (direction === 'right') {
        // 右侧调整：固定左边界，只改变宽度
        const newWidth = Math.max(20, startWidth + deltaX);
        item.w = newWidth;
        // 位置保持不变
        item.x = startPosition;

      } else if (direction === 'left') {
        // 左侧调整：固定右边界，改变位置和宽度
        const rightBoundary = startPosition + startWidth;
        const newWidth = Math.max(20, startWidth - deltaX);
        const newPosition = Math.max(0, rightBoundary - newWidth);

        item.w = newWidth;
        item.x = newPosition;
      }

      console.log(`调整中 - 位置: ${item.x}, 宽度: ${item.w}`);
    },

    /** 停止调整大小 */
    stopResize() {
      if (!this.isResizing) return;

      this.isResizing = false;
      document.removeEventListener('mousemove', this.handleResize);
      document.removeEventListener('mouseup', this.stopResize);
      document.body.style.userSelect = '';

      console.log('调整结束');
      this.updateLayout();
    },

    // ==================== 右键菜单 ====================

    /** 右键菜单 */
    onRightClick(index, event, item) {
      const { clientX, clientY } = event;
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const menuWidth = 160;
      const menuHeight = 180;

      this.contextMenu = {
        visible: true,
        x: clientX + menuWidth > viewportWidth ? viewportWidth - menuWidth : clientX,
        y: clientY + menuHeight > viewportHeight ? viewportHeight - menuHeight : clientY,
        item: item
      };
    },

    /** 关闭右键菜单 */
    closeContextMenu() {
      this.contextMenu.visible = false;
      this.contextMenu.item = null;
    },

    /** 处理菜单操作 */
    handleMenuAction(action) {
      const item = this.contextMenu.item;
      if (!item) return;

      this.$emit('context-menu', item, action);
      this.closeContextMenu();
    },

    // ==================== 拖拽移动功能 ====================

    /** 开始拖拽 */
    startDrag(event, item) {
      console.log('startDrag 被调用', event.target, item.materialName);

      // 如果点击的是resize手柄，不启动拖拽
      if (event.target.classList.contains('resize-handle') ||
          event.target.closest('.resize-handle')) {
        console.log('点击了resize手柄，不启动拖拽');
        return;
      }

      // 如果已经在resize，不启动拖拽
      if (this.isResizing) {
        console.log('正在resize，不启动拖拽');
        return;
      }

      event.preventDefault();
      event.stopPropagation();

      this.isDragging = true;
      this.dragData = {
        item: item,
        startX: event.clientX,
        startY: event.clientY,
        startPosition: item.x,
        offsetX: 0,
        offsetY: 0
      };

      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.stopDrag);
      document.body.style.userSelect = 'none';

      console.log(`开始拖拽物料: ${item.materialName}, 初始位置: ${item.x}`);
    },

    /** 处理拖拽移动 */
    handleDrag(event) {
      if (!this.isDragging || !this.dragData.item) {
        return;
      }

      // 节流处理，避免过于频繁的更新
      const now = Date.now();
      if (now - this.lastMoveTime < 16) { // 约60fps
        return;
      }
      this.lastMoveTime = now;

      // 取消之前的动画帧
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
      }

      // 使用requestAnimationFrame优化性能
      this.rafId = requestAnimationFrame(() => {
        if (!this.isDragging || !this.dragData.item) return;

        const { item, startX, startPosition } = this.dragData;
        const deltaX = event.clientX - startX;

        // 计算新位置
        let newPosition = Math.max(0, startPosition + deltaX);

        // 获取容器宽度，确保不超出边界
        const containerWidth = this.$refs.container.clientWidth;
        const maxPosition = Math.max(0, containerWidth - item.w - 20); // 留20px边距

        newPosition = Math.min(newPosition, maxPosition);

        // 直接更新位置，暂时不使用碰撞检测
        item.x = newPosition;

        // 强制Vue更新
        this.$forceUpdate();
      });
    },

    /** 碰撞检测和位置调整 */
    checkCollisionAndAdjust(draggedItem, newPosition) {
      const draggedLeft = newPosition;
      const draggedRight = newPosition + draggedItem.w;

      // 检查与其他物料块的碰撞
      for (const otherItem of this.layoutData) {
        if (otherItem.i === draggedItem.i || otherItem.x === 0) continue;

        const otherLeft = otherItem.x;
        const otherRight = otherItem.x + otherItem.w;

        // 检查是否有重叠
        if (!(draggedRight <= otherLeft || draggedLeft >= otherRight)) {
          // 有碰撞，调整位置
          if (newPosition > draggedItem.x) {
            // 向右拖拽，放在右侧
            return otherRight + 5; // 留5px间距
          } else {
            // 向左拖拽，放在左侧
            return Math.max(0, otherLeft - draggedItem.w - 5);
          }
        }
      }

      return newPosition;
    },

    /** 停止拖拽 */
    stopDrag() {
      if (!this.isDragging) return;

      // 取消动画帧
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
        this.rafId = null;
      }

      this.isDragging = false;
      document.removeEventListener('mousemove', this.handleDrag);
      document.removeEventListener('mouseup', this.stopDrag);
      document.body.style.userSelect = '';

      console.log('拖拽结束');
      this.updateLayout();

      // 清空拖拽数据
      this.dragData = {
        item: null,
        startX: 0,
        startY: 0,
        startPosition: 0,
        offsetX: 0,
        offsetY: 0
      };

      this.lastMoveTime = 0;
    }
  }
}
</script>

<style lang="scss" scoped>
.native-grid-layout {
  width: 100%;
  height: 100%;
  display: flex;
  background: #0a1428;
  color: #fff;
  font-family: 'Arial', sans-serif;
}

// ==================== 标题区域 ====================
.grid-title {
  width: 60px;
  background: linear-gradient(135deg, #1e3a8a, #3b82f6);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  color: #40e4ff;
  text-shadow: 0 0 10px rgba(64, 228, 255, 0.8);
  border-right: 2px solid #40e4ff;
  position: relative;
  z-index: 2;
}

// ==================== 主要库区区域 ====================
.grid-main {
  flex: 1;
  position: relative;
  background: #0f1b2e;
  border: 1px solid #2d3748;
  overflow: hidden;
  min-height: 400px;
}

.plan-layer,
.actual-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.plan-layer {
  z-index: 1;
}

.actual-layer {
  z-index: 2;
}

// ==================== 物料块样式 ====================
.material-block {
  position: absolute;
  top: 20px;
  border-radius: 4px;
  cursor: grab;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  will-change: transform, left; // 启用硬件加速
  backface-visibility: hidden; // 防止闪烁

  &:hover {
    transform: translateY(-2px);
  }

  &:active {
    cursor: grabbing;
  }

  // 拖拽时禁用过渡效果，提高性能
  &.dragging {
    transition: none !important;
  }
}

.plan-block {
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border: 2px dashed #40e4ff;
  opacity: 0.6;

  .material-content {
    padding: 10px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
  }

  .material-name {
    font-size: 12px;
    color: #a0aec0;
    margin-bottom: 5px;
  }

  .material-label {
    font-size: 10px;
    color: #40e4ff;
    background: rgba(64, 228, 255, 0.2);
    padding: 2px 6px;
    border-radius: 2px;
  }
}

.actual-block {
  background: linear-gradient(135deg, #1a365d, #2d5a87);
  border: 2px solid #40e4ff;

  &:hover {
    border-color: #72eaff;
    box-shadow: 0 0 15px rgba(64, 228, 255, 0.5);
  }

  &.selected {
    border-color: #f6ad55;
    box-shadow: 0 0 15px rgba(246, 173, 85, 0.5);
  }

  &:active {
    cursor: grabbing;
  }

  // 拖拽状态样式
  &.dragging {
    z-index: 1000;
    transform: scale(1.02) translateZ(0); // 强制硬件加速
    box-shadow: 0 8px 25px rgba(64, 228, 255, 0.6);
    cursor: grabbing;
    transition: none; // 拖拽时禁用过渡
    will-change: transform, left;
  }
}

.material-content {
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.material-name {
  font-size: 14px;
  font-weight: bold;
  color: #e2e8f0;
  margin-bottom: 5px;
}

.material-weight {
  font-size: 16px;
  color: #40e4ff;
  font-weight: bold;

  span {
    font-size: 12px;
    color: #a0aec0;
  }
}

// ==================== 悬停提示 ====================
.material-tooltip {
  position: absolute;
  top: -80px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: #40e4ff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 10;
  border: 1px solid #40e4ff;

  &::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #40e4ff;
  }

  div {
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// ==================== 调整手柄 ====================
.resize-handle {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 12px;
  background: transparent;
  z-index: 5;
  cursor: col-resize;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 24px;
    background: linear-gradient(
      to bottom,
      transparent 0%,
      transparent 30%,
      #40e4ff 30%,
      transparent 35%,
      transparent 50%,
      #40e4ff 50%,
      transparent 55%,
      transparent 70%,
      #40e4ff 70%,
      transparent 75%,
      transparent 100%
    );
    border-radius: 2px;
    transition: all 0.2s ease;
  }

  &:hover::after {
    background: linear-gradient(
      to bottom,
      transparent 0%,
      transparent 20%,
      #72eaff 20%,
      transparent 25%,
      transparent 40%,
      #72eaff 40%,
      transparent 45%,
      transparent 60%,
      #72eaff 60%,
      transparent 65%,
      transparent 80%,
      #72eaff 80%,
      transparent 85%,
      transparent 100%
    );
    box-shadow: 0 0 8px rgba(64, 228, 255, 0.8);
  }
}

.resize-handle-left {
  left: -6px;
  cursor: w-resize;

  &::after {
    left: 2px;
  }
}

.resize-handle-right {
  right: -6px;
  cursor: e-resize;

  &::after {
    right: 2px;
  }
}

// ==================== 右侧面板 ====================
.grid-right {
  background: #1a202c;
  border-left: 2px solid #40e4ff;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  z-index: 3;
}

.data-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px 0;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px 16px;
  border-bottom: 1px solid #2d3748;

  h3 {
    margin: 0;
    color: #40e4ff;
    font-size: 16px;
    font-weight: 600;
  }
}

.data-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  padding: 12px 16px;
  background: rgba(64, 228, 255, 0.1);
  border-bottom: 1px solid #2d3748;
  font-size: 12px;
  font-weight: 600;
  color: #40e4ff;

  .col-name { width: 80px; }
  .col-position { width: 70px; }
  .col-width { width: 70px; }
  .col-weight { width: 60px; }
}

.list-body {
  flex: 1;
  overflow-y: auto;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(64, 228, 255, 0.05);
  }

  &.active {
    background: rgba(246, 173, 85, 0.1);
    border-left: 3px solid #f6ad55;
  }

  .col-name {
    width: 80px;
    font-size: 12px;
    color: #e2e8f0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .col-position,
  .col-width {
    width: 70px;

    .el-input-number {
      width: 100%;

      ::v-deep .el-input__inner {
        background: transparent;
        border: 1px solid #4a5568;
        color: #e2e8f0;
        font-size: 11px;
        height: 24px;
        line-height: 24px;

        &:focus {
          border-color: #40e4ff;
        }
      }

      ::v-deep .el-input-number__increase,
      ::v-deep .el-input-number__decrease {
        background: #2d3748;
        border-color: #4a5568;
        color: #a0aec0;

        &:hover {
          background: #40e4ff;
          color: white;
        }
      }
    }
  }

  .col-weight {
    width: 60px;
    font-size: 12px;
    color: #40e4ff;
    display: flex;
    align-items: center;

    .el-input {
      width: 40px;
      margin-right: 2px;

      ::v-deep .el-input__inner {
        background: transparent;
        border: 1px solid #4a5568;
        color: #e2e8f0;
        font-size: 11px;
        height: 20px;
        line-height: 20px;
        padding: 0 4px;

        &:focus {
          border-color: #40e4ff;
        }
      }
    }
  }
}

// ==================== 右键菜单 ====================
.context-menu {
  position: fixed;
  background: rgba(26, 32, 44, 0.95);
  border: 1px solid #40e4ff;
  border-radius: 4px;
  padding: 4px 0;
  z-index: 1000;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  min-width: 140px;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;
  color: #e2e8f0;
  font-size: 14px;
  transition: all 0.2s ease;

  i {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
  }

  &:hover {
    background: rgba(64, 228, 255, 0.1);
    color: #40e4ff;
  }

  &:last-child {
    color: #f56c6c;

    &:hover {
      background: rgba(245, 108, 108, 0.1);
      color: #f56c6c;
    }
  }
}

.menu-divider {
  height: 1px;
  background: #2d3748;
  margin: 4px 0;
}

// ==================== 动画效果 ====================
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

// ==================== 响应式设计 ====================
@media (max-width: 1200px) {
  .grid-right {
    width: 280px !important;
  }

  .list-header,
  .list-item {
    .col-name { width: 70px; }
    .col-position { width: 60px; }
    .col-width { width: 60px; }
    .col-weight { width: 50px; }
  }
}

@media (max-width: 768px) {
  .grid-title {
    width: 40px;
    font-size: 14px;
  }

  .material-block {
    .material-content {
      padding: 6px;
    }

    .material-name {
      font-size: 12px;
    }

    .material-weight {
      font-size: 14px;
    }
  }

  .resize-handle {
    width: 8px;

    &::after {
      width: 4px;
      height: 16px;
    }
  }
}
</style>
