import request from '@/utils/request'

// 获取表单布局(渲染)
export function getformjson(pageKey) {
  return request({
    url: '/formtemp/common/searchboxjson/query',
    method: 'get',
    params: { pageKey: pageKey }
  })
}

// 获取表单布局(设计器)
export function getformjsonDesigner(key,mode) {
  return request({
    url: '/formtemp/common/formjson/query',
    method: 'get',
    params: { key: key,mode:mode }
  })
}

// 保存页面json
export function saveformjson(key, formjson) {
  formjson["pagekey"] = key;
  return request({
    url: '/formtemp/common/formjson/save',
    method: 'post',
    data: formjson
  })
}

// 保存页面json
export function saveformjsonadmin(key, formjson) {
  formjson["pagekey"] = key;
  return request({
    url: '/formtemp/common/formjson/saveadmin',
    method: 'post',
    data: formjson
  })
}

// 表单数据初始化
export function getformdata(filterStr, pageKey) {
  return request({
    url: '/formtemp/common/data/query',
    method: 'get',
    params: { filterStr: JSON.stringify(filterStr), pageKey: pageKey }
  })
}

// 更新数据
export function saveformdata(filterParam, bizDatajson) {
  const postBody = {
    filterParam: filterParam,
    data: bizDatajson
  };

  return request({
    url: '/formtemp/common/data/save',
    method: 'post',
    data: postBody
  })
}