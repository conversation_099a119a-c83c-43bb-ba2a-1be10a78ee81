<template>
    <div style="margin: 10px;">
        <vxe-grid ref="gridRef" v-bind="gridOptions" @page-change="pageChangeEvent" @edit-closed="editClosedEvent">
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="searchFormOptions">
                    <template #action>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="searchEvent">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetEvent">重置</el-button>
                    </template>
                </vxe-form>
            </template>

            <template #toolbarButtons>
                <el-button type="success" plain size="mini" @click="inEvent">手动入库</el-button>
                <el-button type="danger" plain size="mini" @click="outEvent">手动出库</el-button>
                <!-- <vxe-button status="warning" @click="stackEvent">倒垛</vxe-button> -->
                <el-button type="info" plain size="mini" @click="checkEvent">盘点</el-button>
            </template>
        </vxe-grid>

        <vxe-modal resize destroy-on-close show-footer show-confirm-button show-cancel-button v-model="showEditPopup"
            :title="formTitle" :confirm-closable="false" :esc-closable="true" @confirm="confirmEvent"
            @cancel="cancelEvent" @close="closeEvent">
            <vxe-form ref="formRef" v-bind="formOptions"></vxe-form>
        </vxe-modal>

        <vxe-modal resize destroy-on-close show-footer show-confirm-button show-cancel-button v-model="showStackPopup"
            title="倒垛" :confirm-closable="false" :esc-closable="true" @confirm="stackConfirmEvent" @cancel="cancelEvent"
            @close="closeEvent">
            <vxe-form ref="stackFormRef" v-bind="stackFormOptions"></vxe-form>
        </vxe-modal>

        <vxe-modal resize destroy-on-close show-footer show-confirm-button show-cancel-button v-model="showCheckPopup"
            title="盘点" :confirm-closable="false" :esc-closable="true" @confirm="checkConfirmEvent" @cancel="cancelEvent"
            @close="closeEvent">
            <vxe-form ref="checkFormRef" v-bind="checkFormOptions"></vxe-form>
        </vxe-modal>
    </div>
</template>

<script>
import { initMaterialList } from "@/api/md/material";
import {
    selectStockReal,
    updateStockWeightAndRemark,
    saveOrUpdate,
    selectStockByCrossRegionAndStackingPosition,
    stackChange,
    checkStockWeight,
} from "@/api/wms/stockreal"
import { VxeUI } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'

const defaultData = {
    stockRealId: null,
    prodCenterCode: '',
    prodCenterName: '',
    storehouseCode: '',
    storehouseName: '',
    mateCode: '',
    mateName: '',
    crossRegion: '',
    stackingPosition: '',
    weight: null,
    remark: '',
    inOrOut: '',
    operType: '',
}

export default {
    name: 'StockTest',
    data() {
        const materialEditRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
            },
            options: [],
        }

        const stripEditRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                // { value: 'A', label: 'A' },
                // { value: 'B', label: 'B' },
                // { value: 'C', label: 'C' },
                // { value: 'D', label: 'D' },
                // { value: 'E', label: 'E' },
            ]
        }

        const numberInputEditRender = {
            name: 'VxeNumberInput',
            props: {
                disabled: false,
            }
        }

        const operTypeEditRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: []
        }

        const gridOptions = {
            columns: [
                { type: 'checkbox', width: 50 },
                { type: 'seq', width: 50 },
                { field: 'stockRealId', visible: false },
                { field: 'storehouseCode', title: '仓库编码', visible: false },
                { field: 'storehouseName', title: '库存名称', },
                { field: 'crossRegion', title: '料条', width: 80 },
                // { field: 'stackingPosition', title: '垛位', width: 80 },
                { field: 'mateCode', title: '物料编码', visible: false, },
                { field: 'mateName', title: '物料名称', },
                { field: 'stockWeight', title: '库存重量', editRender: { name: 'VxeNumberInput' } },
                { field: 'remark', title: '备注', editRender: { name: 'VxeInput' } },
                { field: 'purchaseWeight', title: '采购入库' },
                { field: 'feedOutWeight', title: '供料出库' },
                { field: 'transferIn', title: '调拨入库' },
                { field: 'transferOut', title: '调拨出库' },
            ],
            data: [
                {
                    stockRealId: 1, storehouseCode: 'YLC001', storehouseName: '原料库1#料场',
                    crossRegion: 'A', stackingPosition: '1', mateCode: '1030300001', mateName: '外购焦丁', stockWeight: 10, remark: '',
                    purchaseIn: 0, feedOut: 0, transferIn: 0, transferOut: 0
                },
            ],

            align: 'center',
            border: true,
            strip: true,
            showOverflow: true,
            keepSource: true,
            loading: false,
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            toolbarConfig: {
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },
            editConfig: {
                mode: 'row',
                trigger: 'dblclick',
                showStatus: true,
            },
            editRules: {
                stockWeight: [
                    { required: true, message: '必须填写' }
                ],
            },
        }

        const searchFormOptions = {
            data: {
                crossRegion: '',
                mateCode: '',
            },
            items: [
                { field: 'crossRegion', title: '料条', itemRender: stripEditRender },
                { field: 'mateCode', title: '物料名称', itemRender: materialEditRender },
                { slots: { default: 'action' } }
            ]
        }

        const formOptions = {
            data: {},
            items: [
                { field: 'mateCode', title: '物料', span: 24, itemRender: materialEditRender },
                { field: 'crossRegion', title: '料条', span: 24, itemRender: stripEditRender },
                // { field: 'stackingPosition', title: '垛位', span: 24, itemRender: numberInputEditRender },
                { field: 'weight', title: '重量', span: 24, itemRender: { name: 'VxeNumberInput' } },
                { field: 'operType', title: '类型', span: 24, itemRender: operTypeEditRender },
            ],
            rules: {
                mateCode: [
                    { required: true, message: '必须填写' }
                ],
                crossRegion: [
                    { required: true, message: '必须填写' }
                ],
                // stackingPosition: [
                //     { required: true, message: '必须填写' }
                // ],
                weight: [
                    { required: true, message: '必须填写' }
                ],
                operType: [
                    { required: true, message: '必须填写' }
                ],
            }
        }

        const stackFormOptions = {
            data: {},
            items: [
                { field: 'mateCode', title: '物料', span: 24, itemRender: materialEditRender },
                { field: 'crossRegion', title: '料条', span: 24, itemRender: numberInputEditRender },
                // { field: 'stackingPosition', title: '垛位', span: 24, itemRender: numberInputEditRender },
                { field: 'stockWeight', title: '原重量', span: 24, itemRender: numberInputEditRender, },

                { field: 'newCrossRegion', title: '倒入料条', span: 24, itemRender: stripEditRender },
                { field: 'newStackingPosition', title: '倒入垛位', span: 24, itemRender: { name: 'VxeNumberInput', } },
                { field: 'newWeight', title: '倒出重量', span: 24, itemRender: { name: 'VxeNumberInput' } },
            ],
            rules: {
                newCrossRegion: [
                    { required: true, message: '必须填写' }
                ],
                newStackingPosition: [
                    { required: true, message: '必须填写数字', type: 'number', min: 1 }
                ],
                newWeight: [
                    { required: true, message: '必须填写数字', type: 'number' }
                ],
            },
            titleWidth: '90',
            titleAlign: 'center',
        }

        const checkFormOptions = {
            data: {},
            items: [
                { field: 'mateCode', title: '物料', span: 24, itemRender: materialEditRender },
                { field: 'crossRegion', title: '料条', span: 24, itemRender: numberInputEditRender },
                // { field: 'stackingPosition', title: '垛位', span: 24, itemRender: numberInputEditRender },
                { field: 'stockWeight', title: '原重量', span: 24, itemRender: numberInputEditRender, },

                { field: 'weight', title: '盘点重量', span: 24, itemRender: { name: 'VxeNumberInput' } },
            ],
            titleWidth: '90',
            titleAlign: 'center',
        }

        return {
            gridOptions,
            searchFormOptions,
            formOptions,
            stackFormOptions,
            checkFormOptions,

            materialEditRender,
            stripEditRender,
            numberInputEditRender,
            operTypeEditRender,

            showEditPopup: false,
            formTitle: '',

            showStackPopup: false,
            showCheckPopup: false,
        }
    },
    methods: {
        //#region grid
        searchGridList() {
            this.handlePageData()
        },
        handlePageData() {
            this.gridOptions.loading = true
            let queryData = this.searchFormOptions.data
            queryData.storehouseCode = this.getStoreHouseCode()
            selectStockReal(queryData).then(response => {
                let result = response.data
                const { pageSize, currentPage } = this.gridOptions.pagerConfig
                this.gridOptions.pagerConfig.total = result.length
                this.gridOptions.data = result.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.gridOptions.loading = false
            })
        },
        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage
            this.gridOptions.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },
        editClosedEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const records = $grid.getUpdateRecords()
                if (records.length != 1) {
                    return;
                }

                updateStockWeightAndRemark(records[0]).then(() => {
                    this.searchGridList()
                })
            }
        },
        //#endregion

        //#region 查询
        searchEvent() {
            this.searchGridList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchGridList()
            }
        },
        //#endregion

        //#region 工具栏
        // 入库按钮
        inEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const records = $grid.getCheckboxRecords()
                if (records.length > 1) {
                    VxeUI.modal.alert({
                        content: '请选择一条记录...',
                        escClosable: true
                    })
                    return
                }
                this.operTypeEditRender.options = [
                    { value: '调拨入库', label: '调拨入库' },
                    { value: '采购入库', label: '采购入库' },
                ]

                if (records.length === 1) {
                    this.materialEditRender.props.disabled = true
                    this.stripEditRender.props.disabled = true
                    this.numberInputEditRender.props.disabled = true

                    this.formOptions.data = Object.assign(XEUtils.clone(defaultData, true), records[0])
                    this.formOptions.data.inOrOut = '入库'
                }
                else {
                    this.materialEditRender.props.disabled = false
                    this.stripEditRender.props.disabled = false
                    this.numberInputEditRender.props.disabled = false

                    defaultData.prodCenterCode = this.getProdCenterCode()
                    defaultData.storehouseCode = this.getStoreHouseCode()

                    this.formOptions.data = XEUtils.clone(defaultData, true)
                    this.formOptions.data.inOrOut = '入库'
                }

                this.formTitle = '入库'
                this.showEditPopup = true
            }
        },
        // 出库按钮
        outEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const records = $grid.getCheckboxRecords()
                if (records.length != 1) {
                    VxeUI.modal.alert({
                        content: '请选择一条记录...',
                        escClosable: true
                    })
                    return
                }

                this.operTypeEditRender.options = [
                    { value: '调拨出库', label: '调拨出库' },
                    { value: '供料出库', label: '供料出库' },
                ]

                if (records.length === 1) {
                    this.materialEditRender.props.disabled = true
                    this.stripEditRender.props.disabled = true
                    this.numberInputEditRender.props.disabled = true

                    this.formOptions.data = Object.assign(XEUtils.clone(defaultData, true), records[0])
                    this.formOptions.data.inOrOut = '出库'
                }

                this.formTitle = '出库'
                this.showEditPopup = true
            }
        },
        // 倒垛按钮
        stackEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const record = $grid.getCheckboxRecords()
                if (record.length != 1) {
                    VxeUI.modal.alert({
                        content: '请选择一条记录...',
                        escClosable: true
                    })
                    return;
                }

                if (record.length === 1) {
                    this.stackFormOptions.data = Object.assign(XEUtils.clone(defaultData, true), record[0])

                    this.materialEditRender.props.disabled = true
                    this.numberInputEditRender.props.disabled = true

                    this.showStackPopup = true
                }
            }
        },
        //盘点按钮
        checkEvent() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const record = $grid.getCheckboxRecords()
                if (record.length != 1) {
                    VxeUI.modal.alert({
                        content: '请选择一条记录...',
                        escClosable: true
                    })
                    return;
                }

                if (record.length === 1) {
                    this.checkFormOptions.data = Object.assign(XEUtils.clone(defaultData, true), record[0])

                    this.materialEditRender.props.disabled = true
                    this.numberInputEditRender.props.disabled = true

                    this.showCheckPopup = true
                }
            }
        },
        //#endregion

        //#region 弹出窗口
        // 入库，出库确定
        async confirmEvent() {
            const $form = this.$refs.formRef
            if ($form) {
                const errMap = await $form.validate()
                if (errMap) {
                    return
                }
                const record = this.formOptions.data
                if (record.stockRealId != null) {
                    saveOrUpdate(record).then(() => {
                        this.closeEvent()
                        this.searchGridList()
                        VxeUI.modal.message({ content: '保存成功', status: 'success' })
                    })
                }
                else {
                    selectStockByCrossRegionAndStackingPosition(record).then(response => {
                        let result = response.data
                        if (result != null && result.length > 0) {
                            let isExist = false
                            for (let index = 0; index < result.length; index++) {
                                if (result[index].mateCode === record.mateCode) {
                                    isExist = true
                                }
                            }

                            if (!isExist) {
                                VxeUI.modal.confirm({
                                    content: '入库物料与库存同料条同垛位中的物料不同，请确认是否混垛堆放？',
                                    escClosable: true,
                                    cancelButtonText: '否',
                                    confirmButtonText: '是',
                                }).then(type => {
                                    if (type === 'confirm') {
                                        record.isMix = true
                                    } else if (type == 'cancel') {
                                        record.isMix = false
                                    } else {
                                        return
                                    }
                                    saveOrUpdate(record).then(() => {
                                        this.closeEvent()
                                        this.searchGridList()
                                        VxeUI.modal.message({ content: '保存成功', status: 'success' })
                                    })
                                })
                            } else {
                                saveOrUpdate(record).then(() => {
                                    this.closeEvent()
                                    this.searchGridList()
                                    VxeUI.modal.message({ content: '保存成功', status: 'success' })
                                })
                            }
                        } else {
                            saveOrUpdate(record).then(() => {
                                this.closeEvent()
                                this.searchGridList()
                                VxeUI.modal.message({ content: '保存成功', status: 'success' })
                            })
                        }
                    })
                }
            }
        },
        cancelEvent() {
            this.closeEvent()
        },
        closeEvent() {
            this.materialEditRender.props.disabled = false
            this.stripEditRender.props.disabled = false
            this.numberInputEditRender.props.disabled = false

            const $form = this.$refs.formRef
            if ($form) {
                $form.reset()
                this.showEditPopup = false
            }

            const $stackForm = this.$refs.stackFormRef
            if ($stackForm) {
                $stackForm.reset()
                this.showStackPopup = false
            }

            const $checkForm = this.$refs.checkFormRef
            if ($checkForm) {
                $checkForm.reset()
                this.showCheckPopup = false
            }
        },
        // 倒垛确定
        async stackConfirmEvent() {
            const $form = this.$refs.stackFormRef
            if ($form) {
                const errMap = await $form.validate()
                if (errMap) {
                    return
                }
                let stackData = this.stackFormOptions.data
                let record = {
                    storehouseCode: stackData.storehouseCode,
                    crossRegion: stackData.newCrossRegion,
                    stackingPosition: stackData.newStackingPosition,
                    mateCode: stackData.mateCode,
                }
                selectStockByCrossRegionAndStackingPosition(record).then(response => {
                    let result = response.data
                    if (result != null && result.length > 0) {
                        let isExist = false
                        for (let index = 0; index < result.length; index++) {
                            if (result[index].mateCode === record.mateCode) {
                                isExist = true
                            }
                        }

                        if (!isExist) {
                            VxeUI.modal.confirm({
                                content: '入库物料与库存同料条同垛位中的物料不同，请确认是否混垛堆放？',
                                escClosable: true,
                                cancelButtonText: '否',
                                confirmButtonText: '是',
                            }).then(type => {
                                if (type === 'confirm') {
                                    stackData.isMix = true
                                } else if (type == 'cancel') {
                                    stackData.isMix = false
                                } else {
                                    return
                                }
                                stackChange(stackData).then(() => {
                                    this.closeEvent()
                                    this.searchGridList()
                                    VxeUI.modal.message({ content: '保存成功', status: 'success' })
                                })
                            })
                        } else {

                            stackChange(stackData).then(() => {
                                this.closeEvent()
                                this.searchGridList()
                                VxeUI.modal.message({ content: '保存成功', status: 'success' })
                            })
                        }
                    } else {

                        stackChange(stackData).then(() => {
                            this.closeEvent()
                            this.searchGridList()
                            VxeUI.modal.message({ content: '保存成功', status: 'success' })
                        })
                    }
                })
            }
        },
        // 盘点确定
        checkConfirmEvent() {
            const $form = this.$refs.checkFormRef
            if ($form) {
                let data = this.checkFormOptions.data
                checkStockWeight(data).then(() => {
                    this.closeEvent()
                    this.searchGridList()
                    VxeUI.modal.message({ content: '保存成功', status: 'success' })
                })
            }
        },
        //#endregion


        //#region 初始化数据
        initData() {
            this.materialEditRender.props.loading = true
            initMaterialList().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].materialNumber}`,
                        label: `${data[i].shortName ? data[i].shortName : data[i].materialName}`
                    })
                }
                this.materialEditRender.options = list
                console.log(this.materialEditRender.options)
                this.materialEditRender.props.loading = false
            })
        },


        getProdCenterCode() {
            return this.$route.query.prodCenterCode
        },

        getStoreHouseCode() {
            return this.$route.query.storehouseCode
        },
    },
    created() {
        this.initData()
    },
    mounted() {
        this.searchGridList()
    },
    beforeDestroy() {

    },
}
</script>