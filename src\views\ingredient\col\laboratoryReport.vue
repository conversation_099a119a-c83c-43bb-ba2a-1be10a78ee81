<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block">
      <span >化验结果</span>
      <span style="margin-left: 10px;">{{mateName}}</span>
      <span style="margin-left: 2px;">[</span>
      <span >{{mateCode}}</span>
      <span >]</span>
      <span style="margin-left: 4px;">堆比重:{{bulkWeight}}</span>
    </div>
    <hr class="hrStyle"/>
    <div>
      <!-- 统计列表 -->
      <vxe-table
        border
        align="center"
        :data="planEle">
        <vxe-column field="typeName" title="类型"  width='auto'></vxe-column>
        <vxe-column field="remark" title="备注" width='auto'></vxe-column>
        <vxe-column field="tfe" title="TFE" width='auto'></vxe-column>
        <vxe-column field="feO" title="FeO" width='auto'></vxe-column>
        <vxe-column field="siO2" title="SiO2" width='auto'></vxe-column>
        <vxe-column field="al2O3" title="Al2O3" width='auto'></vxe-column>
        <vxe-column field="caO" title="CaO" width='auto'></vxe-column>
        <vxe-column field="mgO" title="MgO" width='auto'></vxe-column>
        <vxe-column field="tiO2" title="TiO2" width='auto'></vxe-column>
        <vxe-column field="p" title="P" width='auto'></vxe-column>
        <vxe-column field="s" title="S" width='auto'></vxe-column>
        <vxe-column field="mn" title="Mn" width='auto'></vxe-column>
        <vxe-column field="zn" title="Zn" width='auto'></vxe-column>
        <vxe-column field="h2O" title="H2O" width='auto'></vxe-column>
        <vxe-column field="c固" title="C固" width='auto'></vxe-column>
        <vxe-column field="a" title="A" width='auto'></vxe-column>
        <vxe-column field="v" title="V" width='auto'></vxe-column>
      </vxe-table>
    </div>
    <hr class="hrStyle"/>
    <div  style="margin-top:8px;margin-bottom: 8px;">
      <span >发布时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;"
                      v-model="publishTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <el-button type="primary" style="margin-left: 50px;" @click="handleQuery" size="mini">搜索</el-button>
      <el-button type="primary" @click="handleSubmit"  size="mini">完成</el-button>

    </div>
    <div>
      <!-- 化学成分拉取列表 -->
      <vxe-table
        border
        height="600"
        align="center"
        :edit-config="{trigger: 'click', mode: 'row'}"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange"
        :data="materiaEle">
        <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
        <!-- <vxe-column field="mateName" title="物料名称" width='auto' fixed="left"></vxe-column>
        <vxe-column field="mateCode" title="物料编码" width='auto' fixed="left"></vxe-column> -->
        <vxe-column field="publishTime" title="发布时间" width='auto' fixed="left"></vxe-column>
        <vxe-column field="orderCode" title="核心单据" width='auto' fixed="left"></vxe-column>
        <!-- <vxe-column field="" title="送样工厂" width='auto' fixed="left"></vxe-column> -->
        <!-- <vxe-column field="supplierName" title="厂家" width='auto' fixed="left"></vxe-column> -->
        <vxe-column field="tfe" title="TFE" width='auto'></vxe-column>
        <vxe-column field="feO" title="FeO" width='auto'></vxe-column>
        <vxe-column field="siO2" title="SiO2" width='auto'></vxe-column>
        <vxe-column field="al2O3" title="Al2O3" width='auto'></vxe-column>
        <vxe-column field="caO" title="CaO" width='auto'></vxe-column>
        <vxe-column field="mgO" title="MgO" width='auto'></vxe-column>
        <vxe-column field="tiO2" title="TiO2" width='auto'></vxe-column>
        <vxe-column field="p" title="P" width='auto'></vxe-column>
        <vxe-column field="s" title="S" width='auto'></vxe-column>
        <vxe-column field="mn" title="Mn" width='auto'></vxe-column>
        <vxe-column field="zn" title="Zn" width='auto'></vxe-column>
        <vxe-column field="h2O" title="H2O" width='auto'></vxe-column>
        <vxe-column field="c固" title="C固" width='auto'></vxe-column>
        <vxe-column field="a" title="A" width='auto'></vxe-column>
        <vxe-column field="v" title="V" width='auto'></vxe-column>
      </vxe-table>
    </div>
  </div>
</template>

<script>
  import {
    listLaboratoryReportSelect,
  } from "@/api/ingredient/col/laboratoryReport";
    import dayjs from "dayjs";
    export default {
        name: "laboratoryReport",
        // props:['mateCode','planEleCurrentObj'],
        props:['planEleCurrentObj'],
      data() {
          return{
            mateName: '',
            mateCode: '',
            bulkWeight: '',
            materiaEle: [],
            // 下料计划
            planEle: [
              {
                typeName: '当前',
                remark: null,
                tfe: null,
                feO: null,
                siO2: null,
                al2O3: null,
                caO: null,
                mgO: null,
                tiO2: null,
                p: null,
                s: null,
                mn: null,
                zn: null,
                h2O: null,
                c固:null,
                a:null,
                v:null,
              },
              {
                typeName: '平均值',
                remark: null,
                tfe: null,
                feO: null,
                siO2: null,
                al2O3: null,
                caO: null,
                mgO: null,
                tiO2: null,
                p: null,
                s: null,
                mn: null,
                zn: null,
                h2O: null,
                c固:null,
                a:null,
                v:null,
              },
            ],
            // 发布时间
            publishTimeArr:[],
            selectParam:{
              publishTimeStart:'',
              publishTimeEnd:'',
              mateCode:'',
            },
            selectionArr:[],
            selectionCleanArr:[],
            selectionNum:0,
            avgOrCurrentFlag:false,

          }
      },
      created() {
        this.publishTimeArr.push(dayjs(new Date()).add(-1000, "day"));
        this.publishTimeArr.push(dayjs(new Date()).add(1, "day"));
        // 获取物料编码/物料名称/堆比重
        this.mateName = this.planEleCurrentObj.materialname;
        this.mateCode = this.planEleCurrentObj.materialnumber;
        this.bulkWeight = this.planEleCurrentObj.density;
        // 展示页面数据
        this.queryLists()
        this.planEleCurrent()

      },
      methods:{

          /* 提交按钮*/
        handleSubmit(){
          // console.log("this.planEle[1]",JSON.stringify(this.planEle[1]))
          // 对象格式转换
          // console.log("this.planEle[1]",JSON.stringify(this.planEle[1]));

          if(this.planEle[1].siO2!=null){
          this.planEleCurrentObj.remark = this.planEle[1].remark
          this.planEleCurrentObj.elemTfe = this.planEle[1].tfe
          this.planEleCurrentObj.elemFeo = this.planEle[1].feO
          this.planEleCurrentObj.elemSio2 = this.planEle[1].siO2
          this.planEleCurrentObj.elemAl2o3 = this.planEle[1].al2O3
          this.planEleCurrentObj.elemCao = this.planEle[1].caO
          this.planEleCurrentObj.elemMgo = this.planEle[1].mgO
          this.planEleCurrentObj.elemTio2 = this.planEle[1].tiO2
          this.planEleCurrentObj.elemP = this.planEle[1].p
          this.planEleCurrentObj.elemS = this.planEle[1].s
          this.planEleCurrentObj.elemMn = this.planEle[1].mn
          this.planEleCurrentObj.elemZn = this.planEle[1].zn
          this.planEleCurrentObj.elemH2o = this.planEle[1].h2O
          this.planEleCurrentObj.elemC固 = this.planEle[1].c固
          this.planEleCurrentObj.elemA = this.planEle[1].a
          this.planEleCurrentObj.elemV= this.planEle[1].v
          }

          this.$emit('reflashMate', this.planEleCurrentObj);
        },

        /* 展示当前数据 */
        planEleCurrent(){
          console.log("this.planEleCurrentObj:",JSON.stringify(this.planEleCurrentObj))
          // this.planEle[0] = this.planEleCurrentObj
          this.planEle[0].typeName = '当前'
          this.planEle[0].remark = this.planEleCurrentObj.remark
          this.planEle[0].tfe = this.planEleCurrentObj.elemTfe
          this.planEle[0].feO = this.planEleCurrentObj.elemFeo
          this.planEle[0].siO2 = this.planEleCurrentObj.elemSio2
          this.planEle[0].al2O3 = this.planEleCurrentObj.elemAl2o3
          this.planEle[0].caO = this.planEleCurrentObj.elemCao
          this.planEle[0].mgO = this.planEleCurrentObj.elemMgo
          this.planEle[0].tiO2 = this.planEleCurrentObj.elemTio2
          this.planEle[0].p = this.planEleCurrentObj.elemP
          this.planEle[0].s = this.planEleCurrentObj.elemS
          this.planEle[0].mn = this.planEleCurrentObj.elemMn
          this.planEle[0].zn = this.planEleCurrentObj.elemZn
          this.planEle[0].h2O = this.planEleCurrentObj.elemH2o
          this.planEle[0].c固 = this.planEleCurrentObj.elemC固
          this.planEle[0].a = this.planEleCurrentObj.elemA
          this.planEle[0].v= this.planEleCurrentObj.elemV
        },
        /*多选框触发事件*/
        handleCheckboxChange({ records, rowIndex, row,checked }) {
          if(checked  == true){
            this.selectionArr.push(row)
            this.selectionCleanArr.push(row)
            this.selectionNum +=1;
            this.avgOrCurrentFlag = true;
          }else{
            this.selectionNum = this.selectionNum -1;
            this.selectionCleanArr = []
            for(let i=0;i<this.selectionArr.length;i++){
              if(i != rowIndex){
                this.selectionCleanArr.push(this.selectionArr[i])
              }
            }
            if(this.selectionNum<=0){
              this.selectionCleanArr  = []
              this.selectionArr = []
              this.avgOrCurrentFlag = false;
            }
          }
          this.getAvg()
        },
        getAvg() {
          let allOrderCode = ""
          let sumtfe = 0
          let sumfeO = 0
          let sumsiO2 = 0
          let sumal2O3 = 0
          let sumcaO = 0
          let summgO = 0
          let sumtiO2 = 0
          let sump = 0
          let sums = 0
          let summn = 0
          let sumzn = 0
          let sumh2O = 0
          let sumc固 = 0
          let suma = 0
          let sumv = 0
          if(this.selectionCleanArr !=[]){
            // 250731增加核心单据号拼接
            allOrderCode = this.selectionCleanArr.map(i=>i.orderCode).filter(val=>val!=null).join(',')
            this.planEle[1].remark = allOrderCode
            
            for(let i=0;i<this.selectionCleanArr.length;i++){
              sumtfe += Number(this.selectionCleanArr[i].tfe)
              sumfeO += Number(this.selectionCleanArr[i].feO)
              sumsiO2 += Number(this.selectionCleanArr[i].siO2)
              sumal2O3 += Number(this.selectionCleanArr[i].al2O3)
              sumcaO += Number(this.selectionCleanArr[i].caO)
              summgO += Number(this.selectionCleanArr[i].mgO)
              sumtiO2 += Number(this.selectionCleanArr[i].tiO2)
              sump += Number(this.selectionCleanArr[i].p)
              sums += Number(this.selectionCleanArr[i].s)
              summn += Number(this.selectionCleanArr[i].mn)
              sumzn += Number(this.selectionCleanArr[i].zn)
              sumh2O += Number(this.selectionCleanArr[i].h2O)
              sumc固 += Number(this.selectionCleanArr[i].c固)
              suma += Number(this.selectionCleanArr[i].a)
              sumv += Number(this.selectionCleanArr[i].v)

            }
            console.log("this.selectionCleanArr:",JSON.stringify(this.selectionCleanArr))
            let avgtfe = sumtfe / (this.selectionCleanArr.length);
            if (avgtfe >= 0.001) {
              this.planEle[1].tfe = avgtfe.toFixed(3)
            }else{
              this.planEle[1].tfe = avgtfe
            }
            let avgfeO = sumfeO / (this.selectionCleanArr.length);
            if (avgfeO >= 0.001) {
              this.planEle[1].feO = avgfeO.toFixed(3)
            }else{
              this.planEle[1].feO = avgfeO
            }
            let avgsiO2 = sumsiO2 / (this.selectionCleanArr.length);
            if (avgsiO2 >= 0.001) {
              this.planEle[1].siO2 = avgsiO2.toFixed(3)
            }else{
              this.planEle[1].siO2 = avgsiO2
            }
            let avgal2O3 = sumal2O3 / (this.selectionCleanArr.length);
            if (avgal2O3 >= 0.001) {
              this.planEle[1].al2O3 = avgal2O3.toFixed(3)
            }else{
              this.planEle[1].al2O3 = avgal2O3
            }
            let avgcaO = sumcaO/ (this.selectionCleanArr.length);
            if (avgcaO >= 0.001) {
              this.planEle[1].caO = avgcaO.toFixed(3)
            }else{
              this.planEle[1].caO = avgcaO
            }
            let avgmgO= summgO / (this.selectionCleanArr.length);
            if (avgmgO >= 0.001) {
              this.planEle[1].mgO = avgmgO.toFixed(3)
            }else{
              this.planEle[1].mgO = avgmgO
            }
            let avgtiO2 = sumtiO2 / (this.selectionCleanArr.length);
            if (avgtiO2 >= 0.001) {
              this.planEle[1].tiO2 = avgtiO2.toFixed(3)
            }else{
              this.planEle[1].tiO2 = avgtiO2
            }
            let avgp = sump / (this.selectionCleanArr.length);
            if (avgp >= 0.001) {
              this.planEle[1].p = avgp.toFixed(3)
            }else{
              this.planEle[1].p = avgp
            }
            let avgs = sums / (this.selectionCleanArr.length);
            if (avgs >= 0.001) {
              this.planEle[1].s = avgs.toFixed(3)
            }else{
              this.planEle[1].s = avgs
            }
            let avgmn= summn / (this.selectionCleanArr.length);
            if (avgtfe >= 0.001) {
              this.planEle[1].mn = avgmn.toFixed(3)
            }else{
              this.planEle[1].mn = avgmn
            }
            let avgzn= sumzn / (this.selectionCleanArr.length);
            if (avgzn >= 0.001) {
              this.planEle[1].zn = avgzn.toFixed(3)
            }else{
              this.planEle[1].zn = avgzn
            }
            let avgh2O = sumh2O / (this.selectionCleanArr.length);
            if (avgh2O >= 0.001) {
              this.planEle[1].h2O = avgh2O.toFixed(3)
            }else{
              this.planEle[1].h2O = avgh2O
            }
            let avgc固 = sumc固 / (this.selectionCleanArr.length);
            if (avgc固 >= 0.001) {
              this.planEle[1].c固 = avgc固.toFixed(3)
            }else{
              this.planEle[1].c固 = avgc固
            }

            let avgca = suma / (this.selectionCleanArr.length);
            if (avgca >= 0.001) {
              this.planEle[1].a = avgca.toFixed(3)
            }else{
              this.planEle[1].a = avgca
            }

            let avgcv = sumv / (this.selectionCleanArr.length);
            if (avgcv >= 0.001) {
              this.planEle[1].v = avgcv.toFixed(3)
            }else{
              this.planEle[1].v = avgcv
            }
          }else{
            this.planEle[1]=[]
          }
        },

        /*搜索按钮*/
        handleQuery(){
          if(this.publishTimeArr != null){
            if (this.publishTimeArr.length == 2) {
              this.selectParam.publishTimeStart = dayjs(this.publishTimeArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.selectParam.publishTimeEnd = dayjs(this.publishTimeArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          this.queryLists();
        },
        queryLists(){
          if(this.publishTimeArr != null){
            if (this.publishTimeArr.length == 2) {
              this.selectParam.publishTimeStart = dayjs(this.publishTimeArr[0]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
              this.selectParam.publishTimeEnd = dayjs(this.publishTimeArr[1]).format(
                "YYYY-MM-DD HH:mm:ss"
              );
            }
          }
          this.selectParam.mateCode = this.mateCode
          listLaboratoryReportSelect(this.selectParam).then(response=>{
            console.log("response：",JSON.stringify(response))
            this.materiaEle = response.data
            this.loading = false;
          });
        },
      },
    }
</script>

<style scoped>
  .block{
    margin-top:8px;
    font-weight: bold;
    font-size: large;
  }
  .hrStyle{
    border: 1px solid #409eff;
  }

</style>
