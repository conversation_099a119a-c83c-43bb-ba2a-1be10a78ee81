import request from '@/utils/request'

// 查询烧结物料配比子列表
export function listDetail(query,selectVO) {
  return request({
    url: '/api/cpes/ratiodetail/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询烧结物料配比子详细
export function getDetail(mateRatioDetailId) {
  return request({
    url: '/api/cpes/ratiodetail/' + mateRatioDetailId,
    method: 'get'
  })
}

// 新增烧结物料配比子
export function addDetail(data) {
  return request({
    url: '/api/cpes/ratiodetail',
    method: 'post',
    data: data
  })
}

// 修改烧结物料配比子
export function updateDetail(data) {
  return request({
    url: '/api/cpes/ratiodetail',
    method: 'put',
    data: data
  })
}

// 删除烧结物料配比子
export function delDetail(mateRatioDetailId) {
  return request({
    url: '/api/cpes/ratiodetail/' + mateRatioDetailId,
    method: 'delete'
  })
}
