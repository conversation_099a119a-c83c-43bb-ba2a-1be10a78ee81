import request from '@/utils/request'

// 查询企微预警推送列表
export function listPush(query, selectVO) {
  return request({
    url: '/api/warn/TWarnPush/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询企微预警推送详细
export function getPush(warnId) {
  return request({
    url: '/api/warn/TWarnPush/warnPushById/' + warnId,
    method: 'get'
  })
}

// 新增企微预警推送
export function addPush(data) {
  return request({
    url: '/api/warn/TWarnPush/addWarnPush',
    method: 'post',
    data: data
  })
}

// 修改企微预警推送
export function updatePush(data) {
  return request({
    url: '/api/warn/TWarnPush/updateWarnPush',
    method: 'put',
    data: data
  })
}

// 删除企微预警推送
export function delPush(warnId) {
  return request({
    url: '/api/warn/TWarnPush/removeByIds/' + warnId,
    method: 'delete'
  })
}


// 新增企微预警推送
export function saveOrUpdate(data) {
  return request({
    url: '/api/warn/TWarnPush/saveOrUpdate',
    method: 'post',
    data: data
  })
}