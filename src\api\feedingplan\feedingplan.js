import request from '@/utils/request'

// 查询配料计划列表
/* queryjson:{
  beginworkdate: null,
  endworkdate: null,
  prodcode:null,
  planNo: null,
  status: null,
  pageNum: null,
  pageSize: null,
  pagekey: null,
} */
export function listFeedingPlan(query) {
  return request({
    url: '/cpesfeed/bat/list',
    method: 'get',
    params: query
  })
}
// 获取筛选条件（加工中心和状态）
export function getFilterOptions() {
  return request({
    url: '/cpesfeed/bat/bindctl',
    method: 'get'
  })
}
// 查询配料计划详细
export function getFeedingPlan(id) {
  return request({
    url: '/api/feedingplan/' + id,
    method: 'get'
  })
}

// 查询配料计划展开行数据
export function getFeedingPlanDetails(id) {
  return request({
    url: '/api/feedingplan/details/' + id,
    method: 'get'
  })
}

// 获取物料数据（计划总量和剩余量）
export function getBlockStats(planid) {
  return request({
    url: '/cpesfeed/bat/getblockstats/' + planid,
    method: 'get'
  })
}

// 获取block信息
export function getBlockInfo(planid) {
  return request({
    url: '/cpesfeed/bat/getblockinfo/' + planid,
    method: 'get'
  })
}

// 获取block料种参照数据
export function getMaterialOptions(planid) {
  return request({
    url: '/cpesfeed/bat/getblockmate/' + planid,
    method: 'get'
  })
}

// 保存block信息
export function saveBlockInfo(planid, data) {
  return request({
    url: '/cpesfeed/bat/saveblockinfo/' + planid,
    method: 'post',
    data: data
  })
}

// 获取料仓匹配列表参照数据
export function getBinData(prodCenterCode) {
  return request({
    url: '/api/siloconfig/listSiloconfigAll',
    method: 'get',
    params: {
      prodCenterCode: prodCenterCode
    }
  })
}

// 保存物料料仓匹配关系
export function saveMaterialBinMatch(data) {
  return request({
    url: '/cpesfeed/bat/savematerialbinmatch',
    method: 'post',
    data: data
  })
}

// 保存料仓扩展信息
export function saveSiloExtend(data) {
  return request({
    url: '/api/siloextend/siolExtendAdd',
    method: 'post',
    data: data
  })
}

// 查询料仓扩展信息列表
export function getSiloExtendList(planId) {
  return request({
    url: '/api/siloextend/siolExtendSelectList',
    method: 'post',
    data: { planId: planId }
  })
}

// 配料计划进入下一步
export function goNextStep(data) {
  return request({
    url: '/cpesfeed/bat/gonext',
    method: 'post',
    data: data
  })
}

// 拆分block
export function splitBlock(planid, blockCount) {
  return request({
    url: `/cpesfeed/plan/splitblock/${planid}/${blockCount}`,
    method: 'get'
  })
}


