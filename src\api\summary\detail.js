import request from '@/utils/request'

// 查询汇总配置明细列表
export function listDetail(query,selectVO) {
  return request({
    url: '/api/summary/configdetial/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询汇总配置明细详细
export function getDetail(configDetailId) {
  return request({
    url: '/api/summary/configdetial/' + configDetailId,
    method: 'get'
  })
}

// 新增汇总配置明细
export function addDetail(data) {
  return request({
    url: '/api/summary/configdetial',
    method: 'post',
    data: data
  })
}

// 修改汇总配置明细
export function updateDetail(data) {
  return request({
    url: '/api/summary/configdetial',
    method: 'put',
    data: data
  })
}

// 删除汇总配置明细
export function delDetail(configDetailId) {
  return request({
    url: '/api/summary/configdetial/' + configDetailId,
    method: 'delete'
  })
}

export function getByConfigID(configDetailId) {
  return request({
    url: '/api/summary/configdetial/getByConfigID/' + configDetailId,
    method: 'get'
  })
}
