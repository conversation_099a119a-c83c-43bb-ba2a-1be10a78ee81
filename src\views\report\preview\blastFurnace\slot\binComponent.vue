<template>
  <div class="bin-component-container">
    <div class="bin-header">
      <h3>料仓装货状态监控</h3>
      <span class="bin-count">共 {{ siloData.length }} 个料仓</span>
    </div>

    <div class="bin-grid">
      <div
        v-for="(silo, index) in siloData"
        :key="`silo-${silo.binId}-${index}`"
        class="bin-item">

        <!-- 料仓名称标签 -->
        <div class="bin-name-tag">{{ silo.binName }}</div>

        <!-- 物料名标签 -->
        <div class="bin-type-tag" :style="{ background: getTypeColor(silo.binType) }">
          {{ silo.materialName || getTypeLabel(silo.binType) }}
        </div>

        <!-- 料仓主体 -->
        <div class="bin-body">
          <!-- 料仓外壳 -->
          <div class="bin-shell">
            <!-- 物料填充区域 -->
            <div
              class="bin-material"
              :style="{
                height:  calculatePercentage(silo),
                background: getMaterialGradient(silo.binType)
              }">
              <!-- 料位百分比显示 -->
              <div class="percentage-display">
                {{ calculatePercentage(silo)}}
              </div>
            </div>

            <!-- 空白区域 -->
            <div class="bin-empty"></div>
          </div>

          <!-- 料仓底部尖角 -->
          <div class="bin-bottom"></div>
        </div>

        <!-- 料仓信息 -->
        <div class="bin-info">
          <div class="info-row">
            <span class="info-label">重量:</span>
            <span class="info-value">{{ silo.materialWeight ? silo.materialWeight.toFixed(1) + 't' : '0.0t' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">容量:</span>
            <span class="info-value">{{ silo.binSolvent ? silo.binSolvent + 't' : '100t' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BinComponent',
  props: {
    siloData: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    /* 料位百分比 */
    calculatePercentage(silo) {
      if (silo.materialPercentage) {
        return silo.materialPercentage
      }else{
        return 0
      }


    },

/* 获取料仓百分比（用于显示） */
   /* getPercentage(silo) {
      const percentage = this.calculatePercentage(silo)
      return Math.round(percentage)
    },*/

    /* 根据物料类型获取标签文字 */
    getTypeLabel(binType) {
      const labelMap = {
        '焦槽': '焦炭',
        '矿槽': 'GZ',
        'J10启动': 'J10',
        'J10运行': 'J10',
        'J7启动': 'J7',
        'J7运行': 'J7',
        '返焦料位': '返焦',
        '返矿料位': '返矿'
      }
      return labelMap[binType] || binType
    },

    /* 根据物料类型获取标签颜色 */
    getTypeColor(binType) {
      const colorMap = {
        '焦槽': '#4A90E2',
        '矿槽': '#4A90E2',
        'J10启动': '#E74C3C',
        'J10运行': '#27AE60',
        'J7启动': '#F39C12',
        'J7运行': '#3498DB',
        '返焦料位': '#9B59B6',
        '返矿料位': '#1ABC9C'
      }
      return colorMap[binType] || '#4A90E2'
    },

    /* 根据物料类型获取物料渐变色 */
    getMaterialGradient(binType) {
      const gradientMap = {
        '焦槽': 'linear-gradient(180deg, #2C3E50 0%, #34495E 100%)',
        '矿槽': 'linear-gradient(180deg, #8B4513 0%, #A0522D 100%)',
        'J10启动': 'linear-gradient(180deg, #E74C3C 0%, #C0392B 100%)',
        'J10运行': 'linear-gradient(180deg, #27AE60 0%, #229954 100%)',
        'J7启动': 'linear-gradient(180deg, #F39C12 0%, #E67E22 100%)',
        'J7运行': 'linear-gradient(180deg, #3498DB 0%, #2980B9 100%)',
        '返焦料位': 'linear-gradient(180deg, #9B59B6 0%, #8E44AD 100%)',
        '返矿料位': 'linear-gradient(180deg, #1ABC9C 0%, #16A085 100%)'
      }
      return gradientMap[binType] || 'linear-gradient(180deg, #95A5A6 0%, #7F8C8D 100%)'
    }
  }
}
</script>

<style scoped>
/* 容器样式 */
.bin-component-container {
  margin-top: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

/* 标题区域 */
.bin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #dee2e6;
}

.bin-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

.bin-count {
  color: #6c757d;
  font-size: 14px;
  background: #e9ecef;
  padding: 4px 12px;
  border-radius: 12px;
}

/* 网格布局 */
.bin-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 20px;
  justify-items: center;
}

/* 单个料仓项 */
.bin-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100px;
}

/* 料仓名称标签 */
.bin-name-tag {
  background: #6c757d;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 5px;
  min-width: 40px;
  text-align: center;
}

/* 物料名标签 */
.bin-type-tag {
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 10px;
  min-width: 50px;
  text-align: center;
}

/* 料仓主体 */
.bin-body {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

/* 料仓外壳 */
.bin-shell {
  position: relative;
  width: 80px;
  height: 120px;
  background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
  border: 3px solid #2196F3;
  border-radius: 12px 12px 0 0;
  overflow: hidden;
  display: flex;
  flex-direction: column-reverse;
  box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

/* 物料填充 */
.bin-material {
  position: relative;
  width: 100%;
  transition: height 0.8s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 20px;
}

/* 百分比显示 */
.percentage-display {
  color: white;
  font-size: 13px;
  font-weight: bold;
  text-shadow: 1px 1px 3px rgba(0,0,0,0.7);
  background: rgba(0,0,0,0.2);
  padding: 2px 6px;
  border-radius: 10px;
  backdrop-filter: blur(2px);
}

/* 空白区域 */
.bin-empty {
  flex: 1;
  width: 100%;
}

/* 料仓底部尖角 */
.bin-bottom {
  width: 0;
  height: 0;
  border-left: 42px solid transparent;
  border-right: 42px solid transparent;
  border-top: 25px solid #2196F3;
  margin-top: -3px;
  filter: drop-shadow(0 2px 4px rgba(33, 150, 243, 0.3));
}

/* 料仓信息 */
.bin-info {
  font-size: 11px;
  text-align: center;
  width: 100%;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2px;
  padding: 1px 0;
}

.info-label {
  color: #6c757d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bin-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 15px;
  }

  .bin-item {
    width: 90px;
  }

  .bin-shell {
    width: 70px;
    height: 100px;
  }

  .bin-bottom {
    border-left-width: 35px;
    border-right-width: 35px;
    border-top-width: 18px;
  }
}
</style>
