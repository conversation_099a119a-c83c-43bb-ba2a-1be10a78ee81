<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="班制名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入班制名称" clearable style="width: 240px"
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['work:mode:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['work:mode:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleAdjust">调班</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['work:mode:remove']">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="workModeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="班制主键" align="center" prop="id" />
      <el-table-column label="班制名称" align="center" prop="workModeName" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="修改人" align="center" prop="updateBy" :show-overflow-tooltip="true" />
      <el-table-column label="修改时间" align="center" prop="updateTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-search" @click="handleSearch(scope.row)">查看排版</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['work:mode:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['work:mode:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="班制名称" prop="workModeName">
          <el-input v-model="form.workModeName" placeholder="请输入班制名称" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 调班对话框 -->
    <el-dialog title="调班" :visible.sync="shiftAdjustmentOpen" append-to-body width="340px">
      <el-form ref="adjustForm" :model="adjustForm" label-width="80px" :rules="adjustRules">
        <el-form-item label="班制名称" prop="workModeId">
          <el-input v-model="adjustForm.workModeName" placeholder="请输入班制名称" :disabled="true" />
        </el-form-item>
        <el-form-item label="班别名称" prop="workClass">
          <el-select v-model="adjustForm.workClass" placeholder="请选择班别名称">
            <el-option v-for="item in workClassList" :key="item.workClassName" :label="item.workClassName"
              :value="item.workClassName">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="日期" prop="date">
          <el-date-picker v-model="adjustForm.date" type="date" value-format="yyyy-MM-dd HH:mm:ss"></el-date-picker>
        </el-form-item>
        <el-form-item label="班次名称" prop="workShift">
          <el-select v-model="adjustForm.workShift" placeholder="请选择班次名称">
            <el-option v-for="item in workShiftList" :key="item.workShiftName" :label="item.workShiftName"
              :value="item.workShiftName">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjustForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="排版详情" :visible.sync="rlOpen" append-to-body width="1000px">
      <el-calendar v-model="calendarValue">
        <template slot="dateCell" slot-scope="{date, data}">
          <div class="dayWork" style="width: 100%; height:100%;display: flex;flex-direction: column;position: sticky;">
            <div v-for="(item) in calendarModeList" v-if="parseTime(item.endTime, '{y}-{m}-{d}') == data.day"
              :style="{ 'flex': 1, 'display': 'flex', 'justify-content': 'center', 'align-items': 'center' }">
              {{ item.workClass }}（{{ item.workShift }}）
            </div>
            <span style="position: absolute;">
              {{ data.day.split('-').slice(2).join('-') }}
            </span>
            <!-- 不是本月，就添加一层蒙版。 -->
            <div v-if="parseTime(calendarValue, '{y}-{m}') != data.day.split('-').slice(0, 2).join('-')"
              style="margin:0px; width: 100%;height: 100%;background-color: rgba(240, 240, 240, 0.4);position: absolute;z-index: 2;top: 0;left: 0;">
            </div>
          </div>
        </template>
      </el-calendar>
    </el-dialog>
  </div>
</template>

<script>
import { addClassMode, getWorkModeByMonth, listByName, getWorkMode, updateWorkMode, delWorkMode, shiftAdjustment, listWorkClassByWorkModeId, listWorkShiftByWorkModeId } from "@/api/system/work";

export default {
  name: "WorkMode",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      workModeNames: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参数表格数据
      workModeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: '',
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        workModeName: [
          { required: true, message: "班制名称不能为空", trigger: "blur" }
        ]
      },
      adjustRules: {
        workClass: [
          { required: true, message: "班别名称不能为空", trigger: "blur" }
        ],
        date: [
          { required: true, message: "日期不能为空", trigger: "blur" }
        ],
        workShift: [
          { required: true, message: "班次名称不能为空", trigger: "blur" }
        ],
        workModeId: [
          { required: true, message: "班制名称不能为空", trigger: "blur" }
        ],
      },
      // 调班弹出层是否显示
      shiftAdjustmentOpen: false,
      // 调班参数
      adjustForm: {},
      workClassList: [],
      workShiftList: [],
      // 排版详情对话框是否显示
      rlOpen: false,
      calendarValue: new Date(),
      calendarModeId: undefined,
      calendarModeList: [],
    };
  },
  created() {
    this.getList();
  },
  watch: {
    calendarValue(date) {
      getWorkModeByMonth({ 'queryDate': this.parseTime(date), 'workModeId': this.calendarModeId }).then(res => {
        this.calendarModeList = res.data;
      })
    }
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true;
      listByName(this.queryParams).then(response => {
        this.workModeList = response.rows;
        this.total = response.total;
        this.loading = false;
      }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.shiftAdjustmentOpen = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        workModeName: undefined,
        remark: undefined
      };
      this.adjustForm = {
        workModeId: undefined,
        workModeName: undefined,
        workClass: undefined,
        workShift: undefined,
        date: undefined
      };
      this.resetForm("form");
      this.resetForm("adjustForm")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.name = '';
      this.handleQuery();
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加班制";
    },
    /** 调班按钮操作 */
    handleAdjust() {
      this.reset();
      this.shiftAdjustmentOpen = true;
      this.adjustForm.workModeName = this.workModeNames[0];
      this.adjustForm.workModeId = this.ids[0];
      listWorkClassByWorkModeId(this.ids[0]).then(res => {
        this.workClassList = res.data
      })
      listWorkShiftByWorkModeId(this.ids[0]).then(res => {
        this.workShiftList = res.data
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.workModeNames = selection.map(item => item.workModeName)
      this.single = selection.length != 1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getWorkMode(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改班制";
      });
    },
    submitAdjustForm() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          shiftAdjustment(this.adjustForm.workModeId, this.adjustForm.workClass, this.adjustForm.workShift, this.adjustForm.date).then(res => {
            this.shiftAdjustmentOpen = false;
            this.$modal.msgSuccess("调班成功");
          })
        }
      })
    },
    /** 查询排班详情按钮 */
    handleSearch(row) {
      this.calendarModeId = row.id;
      this.rlOpen = true;
      this.calendarValue = new Date();
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != undefined) {
            updateWorkMode(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addClassMode(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除编号为"' + ids + '"的数据项？').then(function () {
        return delWorkMode(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    }
  }
};
</script>

<style scoped>
.dayWork>div:first-child {
  background-color: #d9ffd9;
  color: #11be11;
}

.dayWork>div:nth-child(2) {
  background-color: #fff0bd;
  color: #fccb2c;
}

.dayWork>div:nth-child(3) {
  background-color: #ddeffb;
  color: #2dabff;
}

.dayWork>div {
  border-radius: 10px;
  margin: 1px;
}

.dayWork>span {
  color: black;
}
</style>
