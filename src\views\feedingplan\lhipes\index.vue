<template>
  <div class="app-container" :style="scaleform()" v-loading="loading" element-loading-text="正在拉取化学成分..."
    @dblclick="handleConDbClick" v-if="loadcomplateflag">
    <!-- 录入表单项目 -->
    <el-form ref="inputform" :model="planObj" label-width="auto"
      style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px;">
      <el-row :gutter="30">
        <el-col :span="4">
          <el-form-item label="高炉" size="medium">
            <el-select v-model="planObj.prod_code" placeholder="选择高炉" clearable class="feedplaninput"
              :disabled="isEmptyStr(propProdCode) ? false : true" @change="prodChange">
              <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="矿批" size="medium">
            <el-input v-model="planObj.oreSumWt" style="padding-left: 2px;padding-right: 2px;"
              class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="焦丁" size="medium">
            <el-input v-model="planObj.cokeNutSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="焦炭" size="medium">
            <el-input v-model="planObj.cokeSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="煤量" size="medium">
            <el-input v-model="planObj.coalSumWt" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="焦水" size="medium">
            <el-input v-model="planObj.cokeWater" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" align="middle" :gutter="30">
        <el-col :span="4">
          <el-form-item label="焦丁水" size="medium">
            <el-input v-model="planObj.cokeNutWater" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="风量" size="medium">
            <el-input v-model="planObj.airVol" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="氧量" size="medium">
            <el-input v-model="planObj.oxygen" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="进风面积" size="medium">
            <el-input v-model="planObj.airArea" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item label="料速" size="medium">
            <el-input v-model="planObj.mateSpeed" class="feedplaninput"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <div style="display: flex;justify-content: flex-end;">
            <el-button-group size="medium">
              <!-- <el-button type="primary" icon="el-icon-cpu" circle :loading="loadingCalcBtn"
              @click="handlerCalc"></el-button>
            <el-button type="success" icon="el-icon-check" circle :loading="loadingSaveBtn"
              @click="handlerSave"></el-button> -->
              <el-button type="primary" :loading="loadingCalcBtn" @click="handlerCalc" size="mini">计算</el-button>
              <el-button type="success" :loading="loadingSaveBtn" @click="handlerSave" size="mini">保存</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
      <!-- <el-row>
        <el-col :span="3">
          <el-form-item label="高炉">
            <el-select v-model="planObj.prod_code" placeholder="选择高炉" clearable>
              <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
    <el-row>
      <el-col :span="17">
        <div style="box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1); padding: 0px; margin-top: 5px;">
          <!-- 矿批 -->
          <!-- @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave" -->
          <!-- :row-style="{ height: '30px' }" :cell-style="{ padding: '0' }"  -->
          <el-table fit show-summary :summary-method="getSummariesOre" v-loading="loading" :data="planObj.listOre"
            @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave" @cell-dblclick="handlerDBClick"
            @selection-change="handleSelectionChange1" style="width: 100%;"
            :header-cell-style="{background:'rgb(140, 197, 255) !important'}"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="rowid"
            :style="{ 'background-color': '#f5f5f5' }" :row-style="{height:'24px'}"
            :cell-style="{padding:'0px',borderBottom: '1px solid #000000',borderRight: '1px solid #000000'}">
            <el-table-column label="原料" fixed="left" align="left" header-align="center" width="220px"
              prop="materialNumber">
              <template slot-scope="scope">
                <i class="el-icon-s-promotion flash" style="color: blue" @click="showelementsOre(scope.row)"></i>
                <!-- <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.materialName" style="width: 140px;"
            placeholder="请输入内容"></el-input> -->
                <el-select v-model="scope.row.materialNumber" placeholder="请选择矿料" filterable
                  :filter-method="filterOptionOre" @change="v => handerMateChange(v, scope.row, oreoptions)"
                  @visible-change="handlerVisibileChangerOre" style="width: 140px;height: 50%;" v-if="scope.row.edit"
                  :popper-append-to-body="true" transfer="true">
                  <!-- <el-option-group v-for="group in oreoptions" :key="group.label" :label="group.label">
                <el-option v-for="item in group.options" :key="item.value" :label="item.showtxt" :value="item.value">
                  <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.value }}</span>
                  <span style="float: left; color: #8492a6; font-size: 13px">{{ item.alias }}</span>
                </el-option>
              </el-option-group> -->
                  <el-option v-for="item in oreoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                      }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                      }}</span>
                  </el-option>
                </el-select>
                <span v-else style="margin-left: 10px">{{ scope.row.materialName }}</span>
                <i class="el-icon-delete" style="color: red" @click="delOre(scope.row.rowid)"></i>
              </template>
              <template slot="header">
                <span>矿料</span>
                <el-button style="width:30px; background-color: rgb(140, 197, 255);color:#000;text-align: center;"
                  icon="el-icon-plus" size="mini" @click="addOre" type="text"></el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column label="是否编辑" align="left" header-align="center" prop="edit" :formatter="formatBoolean" /> -->
            <el-table-column label="配比" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="allRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.allRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.allRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
            </el-table-column>
            <el-table-column label="Fe" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="tfeRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tfeRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tfeRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="SiO2" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="sio2Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sio2Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sio2Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="CaO" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="caoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.caoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.caoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="caoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="MgO" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="mgoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mgoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mgoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="Al2O3" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.al2o3Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.al2o3Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="S" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="sRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="P" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="pRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.pRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.pRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="pWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Zn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="znRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.znRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.znRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="znWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ti" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tiRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tiRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tiRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tiWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mnRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mnRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mnRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mnWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="K" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="kRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.kRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.kRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="kWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Na" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="naRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.naRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.naRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="naWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="madrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.madrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.madrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="madwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mt" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mtrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mtrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mtrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mtwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="adrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.adrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.adrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="adwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="FCad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="fcadrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.fcadrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.fcadrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="fcadwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Vdaf" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="vdafrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.vdafrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.vdafrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="vdafwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Qgr,d" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="qgrdrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.qgrdrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.qgrdrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="qgrdwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="density">
              <template slot="header">
                <div>堆比重</div>
                <div>Kg/m3</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.density"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.density }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="vol">
              <template slot="header">
                <div>体积</div>
                <div>m3</div>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="unitprice" v-if="false">
              <template slot="header">
                <div>单价</div>
                <div>元/吨</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.unitprice"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.unitprice }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="price" v-if="false">
              <template slot="header">
                <div>价格</div>
                <div>元</div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-button style="width:100%;height:40px;background:#004eff;color:#000" icon="el-icon-plus" @click="addOre"
      type="primary">添加矿料</el-button> -->
          <!-- <el-button style="width:100%;height:40px;background-color: rgb(140, 197, 255);color:#000;text-align: left;"
            icon="el-icon-plus" @click="addOre" type="text">添加矿料</el-button> -->

          <!-- 焦 -->
          <!-- @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave" -->
          <el-table fit show-summary :summary-method="getSummariesCoke" v-loading="loading" :data="planObj.listCoke"
            :show-header="showHeader" @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave"
            @cell-dblclick="handlerDBClick" @selection-change="handleSelectionChange1"
            :header-cell-style="{background:'rgb(225, 243, 216) !important'}" style="width: 100%;background: #fff;"
            :row-class-name="tableRowClassName" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            row-key="rowid" :style="{ 'background-color': '#f5f5f5' }" :row-style="{height:'24px'}"
            :cell-style="{padding:'0px',borderBottom: '1px solid #000000',borderRight: '1px solid #000000'}">
            <el-table-column label="原料" fixed="left" align="left" header-align="center" width="220px"
              prop="materialNumber">
              <template slot-scope="scope">
                <i class="el-icon-s-promotion flash" style="color: blue"
                  @click="showelementsNotOre('coke', scope.row)"></i>
                <!-- <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.materialName" style="width: 140px;"
            placeholder="请输入内容"></el-input> -->
                <el-select v-model="scope.row.materialNumber" placeholder="请选择焦类" filterable
                  :filter-method="filterOptionCoke" @change="v => handerMateChange(v, scope.row, cokeoptions)"
                  @visible-change="handlerVisibileChangerCoke" style="width: 140px;height: 50%;" v-if="scope.row.edit"
                  :popper-append-to-body="true" transfer="true">
                  <el-option v-for="item in cokeoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                      }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                      }}</span>
                  </el-option>
                </el-select>
                <span v-else style="margin-left: 10px">{{ scope.row.materialName }}</span>
                <i class="el-icon-delete" style="color: red" @click="delCoke(scope.row.rowid)"></i>
              </template>
              <template slot="header">
                <span>焦类</span>
                <el-button style="width:30px;background-color: rgb(225, 243, 216);color:#000;text-align: center"
                  icon="el-icon-plus" @click="addCoke" size="mini" type="text"></el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column label="是否编辑" align="left" header-align="center" prop="edit" :formatter="formatBoolean" /> -->
            <el-table-column label="配比" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="allRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.allRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.allRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
            </el-table-column>
            <el-table-column label="Fe" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tfeRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tfeRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tfeRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="SiO2" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sio2Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sio2Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sio2Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="CaO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="caoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.caoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.caoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="caoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="MgO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mgoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mgoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mgoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="Al2O3" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.al2o3Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.al2o3Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="S" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="P" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="pRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.pRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.pRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="pWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Zn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="znRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.znRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.znRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="znWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ti" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tiRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tiRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tiRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tiWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mnRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mnRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mnRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mnWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="K" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="kRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.kRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.kRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="kWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Na" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="naRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.naRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.naRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="naWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="madrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.madrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.madrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="madwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mt" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="mtrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mtrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mtrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mtwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="adrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.adrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.adrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="adwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="FCad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="fcadrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.fcadrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.fcadrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="fcadwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Vdaf" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="vdafrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.vdafrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.vdafrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="vdafwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Qgr,d" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="qgrdrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.qgrdrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.qgrdrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="qgrdwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="density">
              <template slot="header">
                <div>堆比重</div>
                <div>Kg/m3</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.density"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.density }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="vol">
              <template slot="header">
                <div>体积</div>
                <div>m3</div>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="unitprice" v-if="false">
              <template slot="header">
                <div>单价</div>
                <div>元/吨</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.unitprice"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.unitprice }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="price" v-if="false">
              <template slot="header">
                <div>价格</div>
                <div>元</div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-button style="width:100%;height:40px;background-color: rgb(225, 243, 216);color:#000;text-align: left;"
            icon="el-icon-plus" @click="addCoke" type="text">添加焦类</el-button> -->

          <!-- 焦丁 -->
          <!-- @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave" -->
          <el-table fit show-summary :summary-method="getSummariesCokeNut" v-loading="loading"
            :data="planObj.listCokeNut" :show-header="showHeader" @cell-mouse-enter="handleCellEnter"
            @cell-mouse-leave="handleCellLeave" @cell-dblclick="handlerDBClick"
            @selection-change="handleSelectionChange1" style="width: 100%;background: #fff;"
            :header-cell-style="{background:'rgb(250, 236, 216) !important'}" :row-class-name="tableRowClassName"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" row-key="rowid"
            :style="{ 'background-color': '#f5f5f5' }" :row-style="{height:'24px'}"
            :cell-style="{padding:'0px',borderBottom: '1px solid #000000',borderRight: '1px solid #000000'}">
            <el-table-column label="原料" fixed="left" align="left" header-align="center" width="220px"
              prop="materialNumber">
              <template slot-scope="scope">
                <i class="el-icon-s-promotion flash" style="color: blue"
                  @click="showelementsNotOre('cokenut', scope.row)"></i>
                <!-- <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.materialName" style="width: 140px;"
            placeholder="请输入内容"></el-input> -->
                <el-select v-model="scope.row.materialNumber" placeholder="请选焦丁" filterable
                  :filter-method="filterOptionCokenut" @change="v => handerMateChange(v, scope.row, cokenutoptions)"
                  @visible-change="handlerVisibileChangerCokenut" style="width: 140px;height: 50%;"
                  v-if="scope.row.edit" :popper-append-to-body="true" transfer="true">
                  <el-option v-for="item in cokenutoptions" :key="item.value" :label="item.showtxt" :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                      }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                      }}</span>
                  </el-option>
                </el-select>
                <span v-else style="margin-left: 10px">{{ scope.row.materialName }}</span>
                <i class="el-icon-delete" style="color: red" @click="delCokeNut(scope.row.rowid)"></i>
              </template>
              <template slot="header">
                <span>焦丁</span>
                <el-button style="width:30px; background-color: rgb(250, 236, 216);color:#000;text-align: center;"
                  icon="el-icon-plus" size="mini" @click="addCokeNut" type="text"></el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column label="是否编辑" align="left" header-align="center" prop="edit" :formatter="formatBoolean" /> -->
            <el-table-column label="配比" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="allRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.allRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.allRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
            </el-table-column>
            <el-table-column label="Fe" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tfeRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tfeRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tfeRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="SiO2" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sio2Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sio2Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sio2Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="CaO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="caoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.caoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.caoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="caoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="MgO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mgoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mgoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mgoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="Al2O3" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.al2o3Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.al2o3Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="S" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="P" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="pRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.pRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.pRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="pWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Zn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="znRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.znRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.znRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="znWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ti" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tiRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tiRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tiRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tiWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mnRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mnRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mnRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mnWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="K" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="kRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.kRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.kRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="kWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Na" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="naRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.naRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.naRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="naWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="madrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.madrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.madrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="madwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mt" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="mtrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mtrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mtrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mtwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="adrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.adrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.adrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="adwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="FCad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="fcadrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.fcadrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.fcadrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="fcadwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Vdaf" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="vdafrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.vdafrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.vdafrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="vdafwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Qgr,d" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="qgrdrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.qgrdrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.qgrdrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="qgrdwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="density">
              <template slot="header">
                <div>堆比重</div>
                <div>Kg/m3</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.density"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.density }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="vol">
              <template slot="header">
                <div>体积</div>
                <div>m3</div>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="unitprice" v-if="false">
              <template slot="header">
                <div>单价</div>
                <div>元/吨</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.unitprice"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.unitprice }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="price" v-if="false">
              <template slot="header">
                <div>价格</div>
                <div>元</div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-button style="width:100%;height:40px;background-color: rgb(250, 236, 216);color:#000;text-align: left;"
            icon="el-icon-plus" @click="addCokeNut" type="text">添加焦丁</el-button> -->

          <!-- 煤矿 -->
          <el-table fit show-summary :summary-method="getSummariesCoal" v-loading="loading" :data="planObj.listCoal"
            :show-header="showHeader" @cell-mouse-enter="handleCellEnter" @cell-mouse-leave="handleCellLeave"
            @cell-dblclick="handlerDBClick" @selection-change="handleSelectionChange1"
            :header-cell-style="{background:'rgb(253, 226, 226) !important'}" style="width: 100%;background: #fff;"
            :row-class-name="tableRowClassName" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            row-key="rowid" :style="{ 'background-color': '#f5f5f5' }" :row-style="{height:'24px'}"
            :cell-style="{padding:'0px',borderBottom: '1px solid #000000',borderRight: '1px solid #000000'}">
            <el-table-column label="原料" fixed="left" align="left" header-align="center" width="220px"
              prop="materialNumber">
              <template slot-scope="scope">
                <i class="el-icon-s-promotion flash" style="color: blue"
                  @click="showelementsNotOre('coal', scope.row)"></i>
                <!-- <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.materialName" style="width: 140px;"
            placeholder="请输入内容"></el-input> -->
                <el-select v-model="scope.row.materialNumber" placeholder="请选择煤" filterable
                  :filter-method="filterOptionCoal" @change="v => handerMateChange(v, scope.row, coaloptions)"
                  @visible-change="handlerVisibileChangerCoal" style="width: 140px;height: 50%;" v-if="scope.row.edit"
                  :popper-append-to-body="true" transfer="true">
                  <el-option v-for="item in coaloptions" :key="item.value" :label="item.showtxt" :value="item.value">
                    <span style="float: left; margin-right: 10px;">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.matenum
                      }}</span>
                    <span style="float: left; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.alias
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.schemeinfo
                      }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px; margin-right: 10px;">{{ item.prodcode
                      }}</span>
                  </el-option>
                </el-select>
                <span v-else style="margin-left: 10px">{{ scope.row.materialName }}</span>
                <i class="el-icon-delete" style="color: red" @click="delCoal(scope.row.rowid)"></i>
              </template>
              <template slot="header">
                <span>煤</span>
                <el-button style="width:30px; background-color: rgb(253, 226, 226);color:#000;text-align: center;"
                  icon="el-icon-plus" size="mini" @click="addCoal" type="text"></el-button>
              </template>
            </el-table-column>
            <!-- <el-table-column label="是否编辑" align="left" header-align="center" prop="edit" :formatter="formatBoolean" /> -->
            <el-table-column label="配比" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="allRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.allRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.allRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
            </el-table-column>
            <el-table-column label="Fe" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tfeRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tfeRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tfeRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="SiO2" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sio2Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sio2Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sio2Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="CaO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="caoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.caoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.caoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="caoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="MgO" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mgoRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mgoRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mgoRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" v-if="wtShowFlag" />
            </el-table-column>
            <el-table-column label="Al2O3" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.al2o3Rate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.al2o3Rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="S" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="sRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.sRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.sRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="P" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="pRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.pRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.pRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="pWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Zn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="znRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.znRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.znRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="znWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ti" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tiRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.tiRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.tiRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tiWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mnRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mnRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mnRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mnWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="K" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="kRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.kRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.kRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="kWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Na" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="naRate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.naRate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.naRate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="naWt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="madrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.madrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.madrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="madwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Mt" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="mtrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.mtrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.mtrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mtwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Ad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="adrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.adrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.adrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="adwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="FCad" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="fcadrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.fcadrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.fcadrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="fcadwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Vdaf" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="vdafrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.vdafrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.vdafrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="vdafwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column label="Qgr,d" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="qgrdrate">
                <template slot-scope="scope">
                  <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.qgrdrate"
                    placeholder=""></el-input>
                  <span v-else style="margin-left: 10px">{{ scope.row.qgrdrate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="qgrdwt" v-if="wtShowFlag" />
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="density">
              <template slot="header">
                <div>堆比重</div>
                <div>Kg/m3</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.density"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.density }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="vol">
              <template slot="header">
                <div>体积</div>
                <div>m3</div>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="unitprice" v-if="false">
              <template slot="header">
                <div>单价</div>
                <div>元/吨</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.unitprice"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.unitprice }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="price" v-if="false">
              <template slot="header">
                <div>价格</div>
                <div>元</div>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-button style="width:100%;height:40px;background:rgb(253, 226, 226);color:#000;text-align: left;"
            icon="el-icon-plus" @click="addCoal" type="text">添加煤</el-button> -->

          <!-- 合计 -->
          <el-table fit v-loading="loading" :data="planObj.listStatics" :show-header="showHeader"
            style="width: 100%;background: #fff;" :row-class-name="tableRowClassName"
            :style="{ 'background-color': '#f5f5f5' }" :row-style="{height:'24px'}"
            :cell-style="{padding:'0px',borderBottom: '1px solid #000000',borderRight: '1px solid #000000'}">
            <el-table-column label="原料" fixed="left" align="left" header-align="center" width="220px"
              prop="materialNumber">
            </el-table-column>
            <!-- <el-table-column label="是否编辑" align="left" header-align="center" prop="edit" :formatter="formatBoolean" /> -->
            <el-table-column label="配比" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="allRate">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="allWt" />
            </el-table-column>
            <el-table-column label="Fe" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="tfeRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tfeWt" />
            </el-table-column>
            <el-table-column label="SiO2" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="sio2Rate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sio2Wt" />
            </el-table-column>
            <el-table-column label="CaO" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="caoRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="caoWt" />
            </el-table-column>
            <el-table-column label="MgO" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="mgoRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mgoWt" />
            </el-table-column>
            <el-table-column label="Al2O3" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="al2o3Rate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="al2o3Wt" />
            </el-table-column>

            <el-table-column label="S" align="left" header-align="center">
              <el-table-column label="%" align="left" header-align="center" prop="sRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="sWt" />
            </el-table-column>

            <el-table-column label="P" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="pRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="pWt" />
            </el-table-column>

            <el-table-column label="Zn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="znRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="znWt" />
            </el-table-column>

            <el-table-column label="Ti" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="tiRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="tiWt" />
            </el-table-column>

            <el-table-column label="Mn" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mnRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mnWt" />
            </el-table-column>

            <el-table-column label="K" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="kRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="kWt" />
            </el-table-column>

            <el-table-column label="Na" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="naRate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="naWt" />
            </el-table-column>

            <el-table-column label="Mad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="madrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="madwt" />
            </el-table-column>

            <el-table-column label="Mt" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="mtrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="mtwt" />
            </el-table-column>

            <el-table-column label="Ad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="adrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="adwt" />
            </el-table-column>

            <el-table-column label="FCad" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="fcadrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="fcadwt" />
            </el-table-column>

            <el-table-column label="Vdaf" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="vdafrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="vdafwt" />
            </el-table-column>

            <el-table-column label="Qgr,d" align="left" header-align="center" v-if="wtShowFlag">
              <el-table-column label="%" align="left" header-align="center" prop="qgrdrate" v-if="false">
              </el-table-column>
              <el-table-column label="kg" align="left" header-align="center" prop="qgrdwt" />
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="density">
              <template slot="header">
                <div>堆比重</div>
                <div>Kg/m3</div>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="vol">
              <template slot="header">
                <div>体积</div>
                <div>m3</div>
              </template>
            </el-table-column>

            <el-table-column align="center" header-align="center" prop="unitprice" v-if="false">
              <template slot="header">
                <div>单价</div>
                <div>元/吨</div>
              </template>
              <template slot-scope="scope">
                <el-input v-if="scope.row.edit" class="itemfeedinput" v-model="scope.row.unitprice"
                  placeholder=""></el-input>
                <span v-else style="margin-left: 10px">{{ scope.row.unitprice }}</span>
              </template>
            </el-table-column>
            <el-table-column align="center" header-align="center" prop="price" v-if="false">
              <template slot="header">
                <div>价格</div>
                <div>元</div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="7" style="text-align: center; ">
        <el-row style="margin-top: 6px;margin-left: 6px">
          <!-- 各种指标 -->
          <el-form ref="zbform" :model="planObj" label-width="auto" class="dataForm"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 0px; background-color: rgb(253, 246, 236);">
            <!-- <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>各种指标</label>
                </el-form-item></el-row>
            </el-col>
          </el-row> -->

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="R2=CaO/SiO2" placement="left">
                  <el-form-item label="R2" label-width="auto">
                    {{ planObj.mainTargets.r2 }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="R4=(CaO+MgO)/(SiO2+Al2O3)" placement="left">
                  <el-form-item label="R4" label-width="auto">
                    {{ planObj.mainTargets.r4 }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="R3=(CaO+MgO)/SiO2" placement="left">
                  <el-form-item label="R3">
                    {{ planObj.mainTargets.r3 }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="负荷">
                  {{ planObj.mainTargets.load }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="综合负荷">
                  {{ planObj.mainTargets.comLoad }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="渣量Kg/tFe" placement="left">
                  <el-form-item label="渣量" label-width="auto">
                    {{ planObj.mainTargets.slagWt }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="风量">
                  {{ planObj.airVol }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="氧量">
                  {{ planObj.oxygen }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="综合焦比Kg/tFe" placement="left">
                  <el-form-item label="综合焦比" label-width="auto">
                    {{ planObj.mainTargets.comRatioOfCoke }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="焦比Kg/tFe" placement="left">
                  <el-form-item label="焦比" label-width="auto">
                    {{ planObj.mainTargets.ratioOfCoke }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-tooltip class="itemfeedinput" effect="dark" content="煤比Kg/tFe" placement="left">
                  <el-form-item label="煤比" label-width="auto">
                    {{ planObj.mainTargets.ratioOfCoal }}
                  </el-form-item>
                </el-tooltip>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="焦丁比">
                  {{ planObj.mainTargets.coke_nutRatio }}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="料速" label-width="auto">
                  {{ planObj.mateSpeed }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="标准风速">
                  {{ planObj.mainTargets.stdWindSpeed }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="进风面积" label-width="auto">
                  {{ planObj.airArea }}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="富氧率">
                  {{ planObj.mainTargets.oxRate }}
                </el-form-item>
              </el-col>

              <el-col :span="8" class="formItemDuty">
                <el-form-item label="周期-批" label-width="auto">
                  {{ planObj.mainTargets.periodBat }}
                </el-form-item>
              </el-col>

              <el-col :span="8" class="formItemDuty">
                <el-form-item label="周期-时" label-width="auto">
                  {{ planObj.mainTargets.periodHour }}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="去焦丁" label-width="auto">
                  {{ planObj.mainTargets.removeCoke_nut }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="矿耗">
                  {{ planObj.mainTargets.oreConsume }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="综合品位">
                  {{ planObj.mainTargets.comGrade }}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="焦层厚度" label-width="auto">
                  {{ planObj.cokeThick }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="矿层厚度">
                  {{ planObj.oreThick }}
                </el-form-item>
              </el-col>
              <el-col :span="8" class="formItemDuty">
                <el-form-item label="料层厚度">
                  {{ planObj.allThick }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-row>
        <el-row style="margin-left: 6px">
          <el-form ref="lzform" :model="planObj" label-width="auto" class="dataForm"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 0px; background-color: rgb(179, 216, 255);">
            <!-- <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>炉渣成分</label>
                </el-form-item></el-row>
            </el-col>
          </el-row> -->

            <el-row type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>炉渣组成物</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <!-- <el-row>
                <el-col>
                  <el-form-item>
                    <label>理论成分</label>
                  </el-form-item>
                </el-col>
              </el-row> -->

                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>理论占比(%)</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>理论重量(kg)</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <!-- <el-row><el-form-item>
                  <label>实际成分</label>
                </el-form-item></el-row> -->
                <el-row><el-form-item>
                    <label>实际重量(kg)</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>SiO2</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.sio2Ratio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.sio2TheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>CaO</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.caoRatio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.caoTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>MgO</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.mgoRatio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.mgoTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>Al2O3</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.al2o3Ratio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.al2o3TheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>矿耗</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.oreRatio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>CaS</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>数量</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.staticsTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>镁铝比</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.furnaceSlag.mg_alRatio }}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>-</label>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row> -->
          </el-form>
        </el-row>
        <el-row style="margin-left: 6px">
          <!-- 生铁成分 -->
          <el-form ref="stform" :model="planObj" label-width="auto" class="dataForm"
            style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 0px; background-color: rgb(225, 243, 216); ">
            <!-- <el-row :gutter="10" type="flex" justify="center" align="middle">
              <el-col>
                <el-row><el-form-item>
                    <label>生铁成分</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row> -->

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>生铁组成元素</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <!-- <el-row>
                  <el-col>
                    <el-form-item>
                      <label>理论成分</label>
                    </el-form-item>
                  </el-col>
                </el-row> -->

                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>理论成分占比(%)</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>理论成分重量(kg)</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <!-- <el-row><el-form-item>
                    <label>实际成分</label>
                  </el-form-item></el-row> -->
                <el-row><el-form-item>
                    <label>实际成分重量(kg)</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>Fe</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <el-input v-model="planObj.pigIronInfo.feRatio" size="mini"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label>-</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>Si</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <el-input v-model="planObj.pigIronInfo.siRatio" size="mini"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.pigIronInfo.siTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label>-</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>Mn</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <el-input v-model="planObj.pigIronInfo.mnRatio" size="mini"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.pigIronInfo.mnTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label>-</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>S</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <el-input v-model="planObj.pigIronInfo.sRatio" size="mini"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label>-</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>P</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <el-input v-model="planObj.pigIronInfo.pRatio" size="mini"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>-</label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label>-</label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle" v-if="false">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>实际焦比</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>{shijijiaobi}</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label> </label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label> </label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle" v-if="false">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>实际综合焦</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label>{shijizonghejiao}</label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label> </label>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-row><el-form-item>
                    <label> </label>
                  </el-form-item></el-row>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label>批料铁量</label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label></label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.pigIronInfo.mateTheoryWt }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  {{ planObj.pigIronInfo.mateActWt }}
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="0" type="flex" justify="center" align="middle">
              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label></label>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-row>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      <label></label>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="formItemDuty">
                    <el-form-item>
                      {{ planObj.pigIronInfo.mateStatics }}
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-col>

              <el-col :span="6" class="formItemDuty">
                <el-form-item>
                  <label></label>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-row>
      </el-col>
    </el-row>
    <!-- 计算项 注释 -->
    <el-row :gutter="10" style="margin-top: 10px;" v-if="false">
      <!-- 生铁成分 -->
      <!-- style="background-color: bisque; border: 1px solid #000; "> -->
      <el-col :span="8" style="text-align: center; ">
        <el-form ref="stform" :model="planObj" label-width="auto"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(225, 243, 216); ">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>生铁成分</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>组成元素</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col>
                  <el-form-item>
                    <label>理论成分</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>%</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>kg</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>实际成分</label>
                </el-form-item></el-row>
              <el-row><el-form-item>
                  <label>kg</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Fe</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.feRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Si</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.siRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.siTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Mn</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.mnRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mnTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>S</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.sRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>P</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <el-input v-model="planObj.pigIronInfo.pRatio"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>-</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>实际焦比</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>{shijijiaobi}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label> </label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label> </label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>实际综合焦</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>{shijizonghejiao}</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label> </label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label> </label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>批料铁量</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label></label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mateTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                {{ planObj.pigIronInfo.mateActWt }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label></label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label></label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.pigIronInfo.mateStatics }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label></label>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>

      <!-- 炉渣成分 -->
      <!-- style="background-color:aquamarine; border: 1px solid #000; " -->
      <el-col :span="8" style="text-align: center; ">
        <el-form ref="lzform" :model="planObj" label-width="auto"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(179, 216, 255);">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>炉渣成分</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>组成物</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col>
                  <el-form-item>
                    <label>理论成分</label>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>%</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>kg</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-row><el-form-item>
                  <label>实际成分</label>
                </el-form-item></el-row>
              <el-row><el-form-item>
                  <label>kg</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>SiO2</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.sio2Ratio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.sio2TheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>CaO</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.caoRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.caoTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>MgO</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.mgoRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.mgoTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>Al2O3</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.al2o3Ratio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.al2o3TheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>矿耗</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.oreRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>CaS</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>数量</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.staticsTheoryWt }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>镁铝比</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    {{ planObj.furnaceSlag.mg_alRatio }}
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-row>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <label>-</label>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>

            <el-col :span="6">
              <el-form-item>
                <label>-</label>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </el-col>

      <!-- 各种指标 -->
      <!-- style="background-color:bisque; border: 1px solid #000; " -->
      <el-col :span="8" style="text-align: center; ">
        <el-form ref="zbform" :model="planObj" label-width="auto" class="form_application"
          style="box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); padding: 10px; background-color: rgb(253, 246, 236);">
          <el-row :gutter="10" type="flex" justify="center" align="middle">
            <el-col>
              <el-row><el-form-item>
                  <label>各种指标</label>
                </el-form-item></el-row>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="R2=CaO/SiO2" placement="left">
                <el-form-item label="R2" label-width="auto">
                  {{ planObj.mainTargets.r2 }}
                </el-form-item>
              </el-tooltip>

            </el-col>
            <el-col :span="12">
              <el-form-item label="负荷">
                {{ planObj.mainTargets.load }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="R4=(CaO+MgO)/(SiO2+Al2O3)" placement="left">
                <el-form-item label="R4" label-width="auto">
                  {{ planObj.mainTargets.r4 }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="综合负荷">
                {{ planObj.mainTargets.comLoad }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="渣量Kg/tFe" placement="left">
                <el-form-item label="渣量" label-width="auto">
                  {{ planObj.mainTargets.slagWt }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="风量">
                {{ planObj.airVol }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="焦比Kg/tFe" placement="left">
                <el-form-item label="焦比" label-width="auto">
                  {{ planObj.mainTargets.ratioOfCoke }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="氧量">
                {{ planObj.oxygen }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="煤比Kg/tFe" placement="left">
                <el-form-item label="煤比" label-width="auto">
                  {{ planObj.mainTargets.ratioOfCoal }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="富氧率">
                {{ planObj.mainTargets.oxRate }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-tooltip class="itemfeedinput" effect="dark" content="综合焦比Kg/tFe" placement="left">
                <el-form-item label="综合焦比" label-width="auto">
                  {{ planObj.mainTargets.comRatioOfCoke }}
                </el-form-item>
              </el-tooltip>
            </el-col>
            <el-col :span="12">
              <el-form-item label="进风面积" label-width="auto">
                {{ planObj.airArea }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="料速" label-width="auto">
                {{ planObj.mateSpeed }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="标准风速">
                {{ planObj.mainTargets.stdWindSpeed }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="冶炼周期（批）" label-width="auto">
                {{ planObj.mainTargets.periodBat }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="焦丁比">
                {{ planObj.mainTargets.coke_nutRatio }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="冶炼周期（小时）" label-width="auto">
                {{ planObj.mainTargets.periodHour }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="综合品位">
                {{ planObj.mainTargets.comGrade }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="去焦丁" label-width="auto">
                {{ planObj.mainTargets.removeCoke_nut }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="矿耗">
                {{ planObj.mainTargets.oreConsume }}
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="8">
              <el-form-item label="焦层厚度" label-width="auto">
                {{ planObj.cokeThick }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="矿层厚度">
                {{ planObj.oreThick }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="料层厚度">
                {{ planObj.allThick }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-col>
    </el-row>

    <el-dialog :title="dialogTitle" :visible.sync="openEle" width="80%" append-to-body>
      <!-- 下料计划当前成分 -->
      <el-table :data="planEle" highlight-current-row @current-change="handleCurrentChange">
        <el-table-column property="ELETYPE" label="类型" width="150"></el-table-column>
        <el-table-column property="TFE_VALUE" label="Fe" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="SIO2_VALUE" label="SiO2" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="CAO_VALUE" label="CaO" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MGO_VALUE" label="MgO" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="AL2O3_VALUE" label="Al2O3" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="S_VALUE" label="S" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="P_VALUE" label="P" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="ZN_VALUE" label="Zn" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="TI_VALUE" label="Ti" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MN_VALUE" label="Mn" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="K_VALUE" label="K" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="NA_VALUE" label="Na" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MAD_VALUE" label="Mad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="MT_VALUE" label="Mt" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="AD_VALUE" label="Ad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="VDAF_VALUE" label="Vdaf" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="FCAD_VALUE" label="FCad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="QGRD_VALUE" label="Qgr,d" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="MATERIAL_CODE" label="物料编码" v-if="false"></el-table-column>
      </el-table>
      <!-- 物料检化验结果 -->
      <el-table :data="materiaEle" @selection-change="handleSelectionChange" height="550px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column property="MATERIAL_NAME" label="物料名称" width="150"></el-table-column>
        <el-table-column property="MATERIAL_CODE" label="物料编码" width="150"></el-table-column>
        <el-table-column property="PUBLISH_TIME" label="时间" width="150"></el-table-column>
        <el-table-column property="DEPARTMENT_NAME" label="送样工厂"></el-table-column>
        <el-table-column property="MATERIAL_SUPPLIER_NAME" label="厂家"></el-table-column>
        <el-table-column property="TFE_VALUE" label="Fe" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="SIO2_VALUE" label="SiO2" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="CAO_VALUE" label="CaO" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MGO_VALUE" label="MgO" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="AL2O3_VALUE" label="Al2O3" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="S_VALUE" label="S" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="P_VALUE" label="P" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="ZN_VALUE" label="Zn" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="TI_VALUE" label="Ti" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MN_VALUE" label="Mn" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="K_VALUE" label="K" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="NA_VALUE" label="Na" v-if="eleOreFlag"></el-table-column>
        <el-table-column property="MAD_VALUE" label="Mad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="MT_VALUE" label="Mt" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="AD_VALUE" label="Ad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="VDAF_VALUE" label="Vdaf" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="FCAD_VALUE" label="FCad" v-if="!eleOreFlag"></el-table-column>
        <el-table-column property="QGRD_VALUE" label="Qgr,d" v-if="!eleOreFlag"></el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="selectedDialogELE">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  queryFormManger,
  getMaterial,
  delMaterial,
  saveOrUpdate,
  getSaveEntity,
} from "@/api/md/material";
import { treeselectByGroup } from "@/api/md/materialCategory";
import { queryByUnitGropCode } from "@/api/md/materialUnit";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { getemptydatamodel, getOrelist, getCokeList, getSolventList, getMateElemJS, getMateElemLT, getCoalList, calc, saveplan } from "@/api/feedingplan/feedingpage";
import { getCokeNutList, getOreAndSolvent, getdensity, bindserchbox, getMateElemALL, } from "@/api/feedingplan/feedingpage";
import json from "highlight.js/lib/languages/json";
import { mount } from "sortablejs";
import { pinyin, match } from 'pinyin-pro';
import { v4 as uuidv4 } from 'uuid';

export default {
  components: { Treeselect },
  props: ['planid', 'srcplanid', 'propProdCode'],
  data() {
    return {
      // 列表标题行显示标记
      showHeader: true,
      // 页面缩放比例
      currentRatio: 1,
      // 遮罩层
      loading: true,
      // 计算按钮loading
      loadingCalcBtn: false,
      // 保存方案按钮loading
      loadingSaveBtn: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openEle: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCategoryCode: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
      },
      // 物料化学成分检化验结果
      materiaEle: [],
      // 弹出dialog显示的物料信息
      dialogTitle: '',
      // 弹出dialog计划化学成分
      planEle: [
        {
          ELETYPE: '当前',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
        {
          ELETYPE: '平均值',
          TFE_VALUE: null,
          SIO2_VALUE: null,
          CAO_VALUE: null,
          MGO_VALUE: null,
          AL2O3_VALUE: null,
          MATERIAL_CODE: '',
          DENSITY: null,
        },
      ],
      // 表单参数
      form: {},
      // 矿批需要合计的列prop
      summaryCols: ["allRate", "allWt", "vol", "tfeWt", "sio2Wt", "caoWt", "mgoWt", "al2o3Wt", "sWt", "pWt", "znWt", "tiWt", "mnWt", "kWt", "naWt", "madwt", "mtwt", "adwt", "fcadwt", "vdafwt", "qgrdwt", "price"],
      weightAvgCols: ["tfeRate", "sio2Rate", "caoRate", "mgoRate", "al2o3Rate", "sRate", "pRate", "znRate", "tiRate", "mnRate", "kRate", "naRate", "madrate", "mtrate", "adrate", "fcadrate", "vdafrate", "qgrdrate"],
      // 计划数据对象
      planObj: {},
      // 空行对象
      materialobj: {},
      // 矿批下拉
      oreoptions: [],
      oreoptionsAll: [],
      oreoptionsSrc: [],
      // 焦下拉
      cokeoptions: [],
      cokeoptionsAll: [],
      cokeoptionsSrc: [],
      // 焦丁下拉
      cokenutoptions: [],
      cokenutoptionsAll: [],
      cokenutoptionsSrc: [],
      // 煤下拉
      coaloptions: [],
      coaloptionsAll: [],
      coaloptionsSrc: [],
      // 当前操作的物料编码
      currMateNum: '',
      // 当前操作的集合
      currOpList: [],
      // 当前操作的行
      currOpRow: null,
      // 计划ID
      // planid: '',
      // 重量显示标记_测试用
      wtShowFlag: false,
      // 加工中心下拉数据
      optionProdCode: [],
      // 渲染数据标记
      loadcomplateflag: false,
      // 矿料化学成分显示标记
      eleOreFlag: false,
    };
  },
  created() {
    this.loading = false;
    if (undefined != this.$route.query.planid) {
      this.planid = this.$route.query.planid;
    }
    bindserchbox(null, null).then(resp => {
      if (resp.code == 200) {
        // console.log(resp);
        this.optionProdCode = resp.data.prod;
        // this.optionStatus = resp.data.status;
        // 加载数据
        this.loadData();
      }
    }).catch(ex => { });
    // console.log("planid=", this.planid, "srcplanid=", this.srcplanid, "propProdCode=", this.propProdCode);
  },
  mounted() {
    // console.log("默认设置:",pinyin('汉语拼音'));
    // console.log("去掉声调:",pinyin('汉语拼音',{toneType: 'none'}));
    // console.log("hanpin连续:",match('汉语拼音', 'hanpin', { continuous: true }));
    // console.log("hupy连续:",match('汉语拼音', 'hypy', { continuous: true }));
    // console.log("py连续:",match('汉语拼音', 'py', { continuous: true }));
    // console.log("hp连续:",match('汉语拼音', 'hp', { continuous: true }));
    // console.log("uuid ",uuidv4());

  },
  methods: {
    // 页面内容加载
    loadData() {
      console.log("this.propProdCode",this.propProdCode);
      getemptydatamodel(this.planid, this.srcplanid, this.propProdCode).then((resp) => {
        // console.log(resp.data);
        // 等待绑定执行结束
        setTimeout(() => {
          if (resp.code === 200) {
            this.planObj = resp.data;
            this.materialobj = this.planObj.materialModel;
            this.planObj.planid = this.planid;
            if (!this.isEmptyStr(this.propProdCode)) {
              this.planObj.prod_code = this.propProdCode;
            }
            //this.materialList = this.planObj.listMaterial;
            //console.log(this.planObj.pigIronInfo.feRatio);
            this.loadcomplateflag = true;
          }
        }, 0);
      });
      // getOreAndSolvent().then(resp => {
      //   // console.log(resp);
      //   if (resp.code === 200) {
      //     this.oreoptions = resp.data;
      //     this.oreoptionsAll = resp.data;
      //   }
      // });
      getOrelist().then(resp => {
        // console.log("oreoptions:",resp);
        if (resp.code === 200) {
          this.oreoptions = resp.data;
          this.oreoptionsAll = resp.data;
          this.oreoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.oreoptions = this.oreoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.oreoptionsAll = this.oreoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCokeList().then(resp => {
        // console.log("cokeoptions",resp); 
        if (resp.code === 200) {
          this.cokeoptions = resp.data;
          this.cokeoptionsAll = resp.data;
          this.cokeoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.cokeoptions = this.cokeoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.cokeoptionsAll = this.cokeoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCoalList().then(resp => {
        if (resp.code === 200) {
          this.coaloptions = resp.data;
          this.coaloptionsAll = resp.data;
          this.coaloptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.coaloptions = this.coaloptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.coaloptionsAll = this.coaloptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
      getCokeNutList().then(resp => {
        // console.log(resp);
        if (resp.code === 200) {
          this.cokenutoptions = resp.data;
          this.cokenutoptionsAll = resp.data;
          this.cokenutoptionsSrc = resp.data;
          if (!this.isEmptyStr(this.propProdCode)) {
            this.cokenutoptions = this.cokenutoptions.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode));
            this.cokenutoptionsAll = this.cokenutoptionsAll.filter(item => item.prodcode == this.propProdCode || this.isEmptyStr(item.prodcode))
          }
        }
      });
    },
    // 物料下拉事件
    handerMateChange(v, data, curroptions) {
      // 获取当前选项对象
      let curroptionObj = curroptions.filter(item => {
        return item.value === v
      })[0];
      // 获取堆比重
      getdensity(curroptionObj.matenum, curroptionObj.schemeno, this.planObj.prod_code).then(resp => {
        // console.log("getdensity响应 ", resp.data);
        if (resp.code == 200) {
          // 堆比重
          data.density = resp.data.mateinfo.densityValue;
          data.density = resp.data.mateinfo["densityValue"+this.planObj.prod_code]
          // 物料名称
          if (this.isEmptyStr(resp.data.mateinfo.aliasName)) {
            data.materialName = resp.data.mateinfo.materialName;
          } else {
            data.materialName = resp.data.mateinfo.aliasName;
          }
          // 方案信息
          // console.log("curroptions.schemeinfo",curroptionObj.schemeinfo);
          data.schemeInfo = curroptionObj.schemeinfo;
          // console.log("data.schemeinfo",data.schemeInfo);
          // 单价
          data.unitprice = resp.data.mateinfo.proposedPrice;
          // 设置展开
          // data.hasChildren = this.isEmptyStr(curroptionObj.schemeno);
          data.children = resp.data.schemeinfo;
          // console.log(data.materialName);
        }
      }).catch(err => {

      });
    },
    // 高炉切换
    prodChange(v) {
      if (!this.isEmptyStr(v)) {
        this.oreoptions = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.oreoptionsAll = this.oreoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.cokeoptions = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.cokeoptionsAll = this.cokeoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.cokenutoptions = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.cokenutoptionsAll = this.cokenutoptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));

        this.coaloptions = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
        this.coaloptionsAll = this.coaloptionsSrc.filter(item => item.prodcode == v || this.isEmptyStr(item.prodcode));
      }
      else {
        this.oreoptions = this.oreoptionsSrc
        this.oreoptionsAll = this.oreoptionsSrc

        this.cokeoptions = this.cokeoptionsSrc
        this.cokeoptionsAll = this.cokeoptionsSrc

        this.cokenutoptions = this.cokenutoptionsSrc
        this.cokenutoptionsAll = this.cokenutoptionsSrc

        this.coaloptions = this.coaloptionsSrc
        this.coaloptionsAll = this.coaloptionsSrc
      }
    },
    // 调试用handler,大容器双击触发
    handleConDbClick() {
      //this.$message.success("双击");
      this.wtShowFlag = !this.wtShowFlag;
      // this.$message.success(this.wtShowFlag);
    },
    handlerCalc() {
      this.loadingCalcBtn = true;
      calc(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.planObj = resp.data;
          this.$message.success("计算完成");
        }
        this.loadingCalcBtn = false;
      }).catch(err => {
        this.loadingCalcBtn = false;
        // this.$message.error(err);
      });
    },
    handlerSave() {

      console.log('计算前:',this.planObj);

      this.loadingSaveBtn = true;
      // 计算
      calc(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.planObj = resp.data;
        }
      });
      console.log('计算后:',this.planObj);
      // 保存
      saveplan(this.planObj).then(resp => {
        if (resp.code === 200) {
          this.$message.success("保存成功");
        }
        this.loadingSaveBtn = false;
      }).catch(err => {
        this.loadingSaveBtn = false;
      });
    },
    handleSelectionChange1() {

    },
    addOre() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listOre.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listOre.planitemid += 1;
    },
    addCoke() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      console.log("modelMate ", modelMate);
      this.planObj.listCoke.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCoke.planitemid += 1;
    },
    addCoal() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listCoal.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCoal.planitemid += 1;
    },
    addCokeNut() {
      let modelMate = this.materialobj;
      modelMate.rowid = uuidv4();
      this.planObj.listCokeNut.push(JSON.parse(JSON.stringify(modelMate)));
      // this.planObj.listCokeNut.planitemid += 1;
    },
    delOre(itemid) {
      this.planObj.listOre = this.planObj.listOre.filter(item => item.rowid !== itemid);
    },
    delCoke(itemid) {
      this.planObj.listCoke = this.planObj.listCoke.filter(item => item.rowid !== itemid);
    },
    delCoal(itemid) {
      this.planObj.listCoal = this.planObj.listCoal.filter(item => item.rowid !== itemid);
    },
    delCokeNut(itemid) {
      this.planObj.listCokeNut = this.planObj.listCokeNut.filter(item => item.rowid !== itemid);
    },
    // copyMateNum(mateNum) {
    //   clipboard.writeText(mateNum);
    //   this.$message.success("已复制物料编码:" + mateNum);
    // },
    /** 鼠标移入cell */
    handleCellEnter(row, column, cell, event) {
      // row.edit = true
    },
    /** 鼠标移出cell */
    handleCellLeave(row, column, cell, event) {
      // row.edit = false
      // row.edit = !row.edit;
    },
    handlerDBClick(row, column, cell, even) {
      row.edit = true;
    },
    cellClick(row) {
      row.edit = !row.edit;
    },
    // 打开物料化学成分窗口
    showelementsNotOre(mtype, rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = false;
      let tempMateNo = rowinfo.materialNumber.split(':')[0];
      // getMateElemALL(mtype, rowinfo.materialNumber).then((resp) => {
      getMateElemALL(mtype, tempMateNo).then((resp) => {
        try {
          if (resp.code == 200) {
            // console.log(JSON.stringify(resp));
            this.materiaEle = resp.data.samples;
            this.openEle = true;
            this.dialogTitle = '化验结果 ';
            this.dialogTitle += resp.data.MATERIAL_NAME;
            this.dialogTitle += ' 物料编码:' + rowinfo.materialNumber + ' ';
            // this.dialogTitle += ' 堆比重:' + resp.data.DENSITY_VALUE;
            let selectedDen = resp.data["DENSITY_VALUE_"+this.planObj.prod_code];
            selectedDen = this.isEmptyStr(selectedDen)?"0.0":selectedDen;
            this.dialogTitle += ' 堆比重:' + selectedDen;
            this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
            this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
            this.currMateNum = rowinfo.materialNumber;
          }
        } catch (error) {
          this.loading = false;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    // 打开物料化学成分窗口
    showelementsOre(rowinfo) {
      // 根据物料编码获取近期检验结果
      this.loading = true;
      this.currOpRow = rowinfo;
      this.eleOreFlag = true;
      
      let tempMateNo = rowinfo.materialNumber.split(':')[0];
      // getMateElemALL('ore', rowinfo.materialNumber).then((resp) => {
      getMateElemALL('ore', tempMateNo).then((resp) => {
        try {
          if (resp.code == 200) {
            // console.log(JSON.stringify(resp));
            this.materiaEle = resp.data.samples;
            this.openEle = true;
            this.dialogTitle = '化验结果 ';
            this.dialogTitle += resp.data.MATERIAL_NAME;
            this.dialogTitle += ' [' + rowinfo.materialNumber + '] ';
            // this.dialogTitle += '堆比重:' + resp.data.DENSITY_VALUE;
            let selectedDen = resp.data["DENSITY_VALUE_"+this.planObj.prod_code];
            selectedDen = this.isEmptyStr(selectedDen)?"0.0":selectedDen;
            this.dialogTitle += ' 堆比重:' + selectedDen;
            this.planEle[0].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[1].MATERIAL_CODE = rowinfo.materialNumber;
            this.planEle[0].DENSITY = resp.data.DENSITY_VALUE;
            this.planEle[1].DENSITY = resp.data.DENSITY_VALUE;
            this.currMateNum = rowinfo.materialNumber;
          }
        } catch (error) {
          this.loading = false;
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    handleSelectionChange(selection) {
      // TFE
      const valuesTFE = selection.map(item => Number(item['TFE_VALUE']));
      let avgTFE = this.getAvg(valuesTFE);
      this.planEle[1].TFE_VALUE = avgTFE;
      // SIO2
      const valuesSIO2 = selection.map(item => Number(item['SIO2_VALUE']));
      let avgSIO2 = this.getAvg(valuesSIO2);
      this.planEle[1].SIO2_VALUE = avgSIO2;
      // CAO
      const valuesCAO = selection.map(item => Number(item['CAO_VALUE']));
      let avgCAO = this.getAvg(valuesCAO);
      this.planEle[1].CAO_VALUE = avgCAO;
      // MGO
      const valuesMGO = selection.map(item => Number(item['MGO_VALUE']));
      let avgMGO = this.getAvg(valuesMGO);
      this.planEle[1].MGO_VALUE = avgMGO;
      // AL2O3
      const valuesAL2O3 = selection.map(item => Number(item['AL2O3_VALUE']));
      let avgAL2O3 = this.getAvg(valuesAL2O3);
      this.planEle[1].AL2O3_VALUE = avgAL2O3;
      // S
      const valuesS = selection.map(item => Number(item['S_VALUE']));
      let avgS = this.getAvg(valuesS);
      this.planEle[1].S_VALUE = avgS;
      // P
      const valuesP = selection.map(item => Number(item['P_VALUE']));
      let avgP = this.getAvg(valuesP);
      this.planEle[1].P_VALUE = avgP;
      // Zn
      const valuesZn = selection.map(item => Number(item['ZN_VALUE']));
      let avgZn = this.getAvg(valuesZn);
      this.planEle[1].ZN_VALUE = avgZn;
      // Ti
      const valuesTi = selection.map(item => Number(item['TI_VALUE']));
      let avgTi = this.getAvg(valuesTi);
      this.planEle[1].TI_VALUE = avgTi;
      // Mn
      const valuesMn = selection.map(item => Number(item['MN_VALUE']));
      let avgMn = this.getAvg(valuesMn);
      this.planEle[1].MN_VALUE = avgMn;
      // K
      const valuesK = selection.map(item => Number(item['K_VALUE']));
      let avgK = this.getAvg(valuesK);
      this.planEle[1].K_VALUE = avgK;
      // Na
      const valuesNa = selection.map(item => Number(item['NA_VALUE']));
      let avgNa = this.getAvg(valuesNa);
      this.planEle[1].NA_VALUE = avgNa;
      // Mad
      const valuesMad = selection.map(item => Number(item['MAD_VALUE']));
      let avgMad = this.getAvg(valuesMad);
      this.planEle[1].MAD_VALUE = avgMad;
      // Mt
      const valuesMt = selection.map(item => Number(item['MT_VALUE']));
      let avgMt = this.getAvg(valuesMt);
      this.planEle[1].MT_VALUE = avgMt;
      // Ad
      const valuesAd = selection.map(item => Number(item['AD_VALUE']));
      let avgAd = this.getAvg(valuesAd);
      this.planEle[1].AD_VALUE = avgAd;
      // Vdaf
      const valuesVdaf = selection.map(item => Number(item['VDAF_VALUE']));
      let avgVdaf = this.getAvg(valuesVdaf);
      this.planEle[1].VDAF_VALUE = avgVdaf;
      // Fcad
      const valuesFcad = selection.map(item => Number(item['FCAD_VALUE']));
      let avgFcad = this.getAvg(valuesFcad);
      this.planEle[1].FCAD_VALUE = avgFcad;
      // Fcad
      const valuesQgad = selection.map(item => Number(item['QGRD_VALUE']));
      let avgQgad = this.getAvg(valuesQgad);
      this.planEle[1].QGRD_VALUE = avgQgad;
    },
    getAvg(values) {
      let sum = 0;
      values.forEach((v, index) => {
        if (!isNaN(v)) {
          sum += v;
        }
      });
      let avg = sum / (values.length);
      if (avg >= 0.001) {
        return avg.toFixed(3);
      }
      else {
        return avg;
      }
    },
    handleCurrentChange(val) {
      this.currentRow = val;
      console.log(this.currentRow);
    },
    selectedDialogELE() {
      // let currMateIndex = null;
      // this.planObj.listOre.forEach((ore, index) => {
      //   if (ore.materialNumber == this.currMateNum) {
      //     currMateIndex = index;
      //   }
      // });
      // console.log('当前index', currMateIndex);

      // 修改当前物料元素占比
      // this.planObj.listOre[currMateIndex].tfeRate = this.planEle[1].TFE_VALUE;
      // this.planObj.listOre[currMateIndex].sio2Rate = this.planEle[1].SIO2_VALUE;
      // this.planObj.listOre[currMateIndex].caoRate = this.planEle[1].CAO_VALUE;
      // this.planObj.listOre[currMateIndex].mgoRate = this.planEle[1].MGO_VALUE;
      // this.planObj.listOre[currMateIndex].al2o3Rate = this.planEle[1].AL2O3_VALUE;
      // this.planObj.listOre[currMateIndex].pRate = this.planEle[1].P_VALUE;
      // this.planObj.listOre[currMateIndex].sRate = this.planEle[1].S_VALUE;
      // this.planObj.listOre[currMateIndex].znRate = this.planEle[1].ZN_VALUE;
      // this.planObj.listOre[currMateIndex].tiRate = this.planEle[1].TI_VALUE;
      // this.planObj.listOre[currMateIndex].mnRate = this.planEle[1].MN_VALUE;
      // this.planObj.listOre[currMateIndex].kRate = this.planEle[1].K_VALUE;
      // this.planObj.listOre[currMateIndex].naRate = this.planEle[1].NA_VALUE;
      // this.planObj.listOre[currMateIndex].density = this.planEle[1].DENSITY;

      this.currOpRow.tfeRate = this.planEle[1].TFE_VALUE;
      this.currOpRow.sio2Rate = this.planEle[1].SIO2_VALUE;
      this.currOpRow.caoRate = this.planEle[1].CAO_VALUE;
      this.currOpRow.mgoRate = this.planEle[1].MGO_VALUE;
      this.currOpRow.al2o3Rate = this.planEle[1].AL2O3_VALUE;
      this.currOpRow.pRate = this.planEle[1].P_VALUE;
      this.currOpRow.sRate = this.planEle[1].S_VALUE;
      this.currOpRow.znRate = this.planEle[1].ZN_VALUE;
      this.currOpRow.tiRate = this.planEle[1].TI_VALUE;
      this.currOpRow.mnRate = this.planEle[1].MN_VALUE;
      this.currOpRow.kRate = this.planEle[1].K_VALUE;
      this.currOpRow.naRate = this.planEle[1].NA_VALUE;
      this.currOpRow.madrate = this.planEle[1].MAD_VALUE;
      this.currOpRow.mtrate = this.planEle[1].MT_VALUE;
      this.currOpRow.adrate = this.planEle[1].AD_VALUE;
      this.currOpRow.vdafrate = this.planEle[1].VDAF_VALUE;
      this.currOpRow.fcadrate = this.planEle[1].FCAD_VALUE;
      this.currOpRow.qgrdrate = this.planEle[1].QGRD_VALUE;

      this.currOpRow.density = this.planEle[1].DENSITY;
      this.openEle = false;
    },
    getSummariesOre(param) {
      return this.getSummaries(param, '矿批-小计');
    },
    getSummariesCoke(param) {
      return this.getSummaries(param, '焦-小计');
    },
    getSummariesCokeNut(param) {
      return this.getSummaries(param, '焦丁-小计');
    },
    getSummariesCoal(param) {
      return this.getSummaries(param, '煤-小计');
    },
    getSummaries(param, suntxt) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = suntxt + '';
          return;
        }
        // console.log(column.property);
        if (this.summaryCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = sums[index].toFixed(3);
        } else if (this.weightAvgCols.indexOf(column.property) >= 0) {
          const values = data.map(item => Number(item[column.property]));
          let sumValue = 0.0;
          let sumWeight = 0.0;
          data.forEach((v, i) => {
            if (!isNaN(v[column.property])) {
              sumValue += v[column.property] * v["allRate"];
              sumWeight += v["allRate"];
            }
          });
          if (sumWeight > 0) {
            sums[index] = sumValue / sumWeight;
            sums[index] = sums[index].toFixed(3);
          } else {
            sums[index] = 0;
          }
        }
      });
      return sums;
    },

    // 矿
    filterOptionOre(val) {
      if (this.isEmptyStr(val)) {
        this.oreoptions = this.oreoptionsAll;
      } else {
        this.oreoptions = this.oreoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // filterOptionOre(val) {
    //   if (this.isEmptyStr(val)) {
    //     this.oreoptions = this.oreoptionsAll;
    //   } else {
    //     const tempAllOptions = JSON.parse(JSON.stringify(this.oreoptionsAll));
    //     const tempOptions = JSON.parse(JSON.stringify(this.oreoptions));
    //     const oreOptions = this.oreoptionsAll[0].options.filter((item1) => {
    //       return this.checkOption(item1, val);
    //     });
    //     const sovFilterOptions = this.oreoptionsAll[1].options.filter((item2) => {
    //       return this.checkOption(item2, val);
    //     });
    //     tempOptions[0].options = oreOptions;
    //     tempOptions[1].options = sovFilterOptions;
    //     this.oreoptions = tempOptions;
    //   }
    // },
    // 下拉框选项显示
    handlerVisibileChangerOre(e) {
      if (e) {
        this.oreoptions = this.oreoptionsAll;
      }
    },
    // 焦
    filterOptionCoke(val) {
      if (this.isEmptyStr(val)) {
        this.cokeoptions = this.cokeoptionsAll;
      } else {
        this.cokeoptions = this.cokeoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoke(e) {
      if (e) {
        this.cokeoptions = this.cokeoptionsAll;
      }
    },
    // 焦丁
    filterOptionCokenut(val) {
      if (this.isEmptyStr(val)) {
        this.cokenutoptions = this.cokenutoptionsAll;
      } else {
        this.cokenutoptions = this.cokenutoptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCokenut(e) {
      if (e) {
        this.cokenutoptions = this.cokenutoptionsAll;
      }
    },
    // 煤
    filterOptionCoal(val) {
      if (this.isEmptyStr(val)) {
        this.coaloptions = this.coaloptionsAll;
      } else {
        this.coaloptions = this.coaloptionsAll.filter((item) => {
          return this.checkOption(item, val);
        });
      }
    },
    // 下拉框选项显示
    handlerVisibileChangerCoal(e) {
      if (e) {
        this.coaloptions = this.coaloptionsAll;
      }
    },
    isEmptyStr(s) {
      if (s == undefined || s == null || s == '') {
        return true
      }
      return false
    },
    // 校验下拉框选项是否符合录入值
    checkOption(item, val) {
      // console.log(`${item.label} ${item.value} ${item.alias}`);        
      return !!~item.label.indexOf(val)
        ||
        !!~item.value.indexOf(val)
        ||
        !!~item.alias.indexOf(val)
        ||
        this.pinyinMatched(item.label, val)
        ||
        this.pinyinMatched(item.alias, val)
    },
    pinyinMatched(src, val) {
      const mRet = match(src, val, { continuous: true });
      if (mRet == null) {
        return false;
      }
      return true;
    },
    tableRowClassName({ row, rowIndex }) {
      // console.log('orebg');
      return 'orebg';
    },
    formatBoolean: function (row, column, cellValue) {
      var ret = ''  //你想在页面展示的值
      // console.log(cellValue);
      if (cellValue) {
        ret = "是"  //根据自己的需求设定
      } else {
        ret = "否"
      }
      return ret;
    },
    scaleform() {
      return {
        transform: `scale(${this.currentRatio},${this.currentRatio})`,
        padding: `5px`
      }
    }
  }
};
</script>

<style scoped>
.dataForm{
  margin: 0px;
  border-left: 1px solid #000000;
  border-bottom: 1px solid #000000;
}
.formItemDuty{
  border-top: 1px solid #000000;
  border-right: 1px solid #000000;
  margin: 0px;
}
.formItemDutyIn{
  border-left: 1px solid #000000;
  border-right: 1px solid #000000;
}

/* 单元格回行设置 */
::deep .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
  overflow: visible;
}

::deep .el-select-dropdown__wrap.el-scrollbar__wrap {
  margin-bottom: 0 !important;
}

/*闪烁动画*/
@keyframes twinkle {
  from {
    opacity: 1.0;
  }

  50% {
    opacity: 0.4;
  }

  to {
    opacity: 1.0;
  }
}

.flash {
  animation: twinkle 1s;
  animation-iteration-count: infinite;
}

.itemfeedinput {
  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    border: none!important;
  }
}

.feedplaninput {
  /* width: 100px; */
  /* 调整elementUI中样式 如果不需要调整请忽略 */
  .el-input__inner {
    padding-left: 2px;
    padding-right: 2px;
    height: 24px !important;
    /* border: none!important; */
  }
}

.el-form>>>.el-form-item__label {
  text-align: justify;
  text-align-last: justify;
}

.el-form-item {
  margin-bottom: 1px;
  height: 30px;
}

.el-form-item__label-wrap {
  margin-left: 0px !important;
}

.el-table>>>.el-select {
  width: 184px;
  height: 23px;

  .el-input__inner {
    height: 23px;
    border: none!important;
  }

  .el-input__prefix,
  .el-input__suffix {
    height: 23px;
  }

  /* 下面设置右侧按钮居中 */
  .el-input__suffix {
    top: 0px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row;
    align-content: flex-start;
  }

  /* 输入框加上上下边是 32px + 2px =34px */
  .el-input__icon {
    line-height: 25px;
  }
}

.el-table /deep/ .el-table__footer-wrapper tbody td {
    height: 25px;
    padding-top: 2px;
    padding-bottom: 2px;
    font-weight: bolder;
    border-top: 1px solid #000000;
    border-bottom: 1px solid #000000;
    border-right: 1px solid #000000;
    
}

.el-table /deep/ .el-table__fixed-footer-wrapper tbody td {
    height: 25px;
    padding-top: 2px;
    padding-bottom: 2px;
    border-top: 1px solid #000000;
    border-bottom: 1px solid #000000;
    border-right: 1px solid #000000;
}

::v-deep .el-table .el-table__header th,
.el-table .el-table__header tr,
.el-table .el-table__header td {
  /* 表头颜色 */
  background: #c7d1c6 !important;
  padding: 0px;
  height: 25px;
  border-bottom: 1px solid #000000;
  border-right: 1px solid #000000;
}

.el-table{
  /* border: 1px solid #000000; */
  border-left: 1px solid #000000;
  border-top: 1px solid #000000;
}

</style>