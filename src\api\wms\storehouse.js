import request from '@/utils/request'

//查询
export function listStorehouse(query) {
    return request({
        url: '/api/wms/storehouse/list',
        method: 'get',
        params: query,
    })
}

export function saveOrUpdate(data) {
    return request({
        url: '/api/wms/storehouse/saveOrUpdate',
        method: 'post',
        data: data,
    })
}

export function deleteStorehouse(storehouseId) {
    return request({
        url: '/api/wms/storehouse/' + storehouseId,
        method: 'delete',
    })
}

export function listProdCenter() {
    return request({
        url: '/api/wms/storehouse/listProdCenter',
        method: 'get',
    })
}

          /*库区图相关接口*/

/****1#料场******** */
//计划和实际查询接口
export function getStorehouseMapData_1() {
    return request({
        url: 'public/wms/stockmap/info1',
        method: 'get',
    })
}
//保存接口
export function updateStorehouseMapData(data) {
    return request({
        url: 'public/wms/stockmap/save',
        method: 'post',
        data: data,
    })
}


/************ 2#料场 ************/
//计划和实际查询接口
export function getStorehouseMapData_2() {
    return request({
        url: 'public/wms/stockmap/info2',
        method: 'get',
    })
}

/************ 出库、入库、盘点接口 ************/

export function stockInOrOut(data) {
    return request({
        url: 'public/wms/stockmap/inOrOut',
        method: 'post',
        data: data,
    })
}

// 删除接口
export function deleteStock(data) {
    return request({
        url: 'public/wms/stockmap/delete',
        method: 'post',
        data: data,
    })
}

/**大机任务相关接口 */

// 查询大机任务接口 - 1#料场
export function getStackerTasks_1() {
    return request({
        url: '/public/wms/stockmap/work1',
        method: 'get',
    })
}
// 查询大机任务接口 - 2#料场
export function getStackerTasks_2() {
    return request({
        url: '/public/wms/stockmap/work2',
        method: 'get',
    })
}
// 修改大机任务状态接口
export function updateStackerTaskStatus(data) {
    return request({
        url: '/public/wms/stockmap/updateWorkStatus',
        method: 'post',
        data: data,
    })
}