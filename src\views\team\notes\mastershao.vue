<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="业务日期" prop="workDate">
        <el-date-picker style="margin-left: 10px;width: 392px;"
                        v-model="createTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="班次" prop="workGroupName">
        <el-select v-model="queryParams.workGroupName" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="班组" prop="workClassName">
        <el-select v-model="queryParams.workClassName" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="烧结工段" prop="prodCenterName">-->
<!--        <el-input-->
<!--          v-model="queryParams.prodCenterName"-->
<!--          placeholder="请输入烧结工段"-->
<!--          clearable-->
<!--          @keyup.enter.native="handleQuery"-->
<!--        />-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_table">
      <vxe-table
        border
        :edit-config="{trigger: 'click', mode: 'row'}"
        @edit-closed="editClosedEvent"
        :data="tableData"
        :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
        @checkbox-change="handleCheckboxChange">
        >
        <vxe-column type="checkbox"  width="60"></vxe-column>
        <vxe-column field="workDate" title="日期" :edit-render="{ name: 'VxeDatePicker', props: { type: 'date' } }" width="150" ></vxe-column>
        <vxe-column field="prodCenterName" title="烧结工段" width="120" :header-align="'center'"></vxe-column>
        <vxe-column field="workGroupName" title="班次" :edit-render="{name: 'input'}" width="80" :header-align="'center'"></vxe-column>
        <vxe-column field="workClassName" title="班组" :edit-render="{name: 'input'}" width="80" :header-align="'center'"></vxe-column>
        <vxe-column field="recordInfo" title="记录信息" :edit-render="{name: 'input'}" width="120" :header-align="'center'"></vxe-column>
        <vxe-column field="remark" title="备注"width="80" :header-align="'center'"></vxe-column>
        <vxe-column field="createBy" title="创建人"width="80" :header-align="'center'"></vxe-column>
        <vxe-column field="createTime" title="创建时间"width="150" :header-align="'center'"></vxe-column>
        <vxe-column field="updateBy" title="更新人"width="80" :header-align="'center'"></vxe-column>
        <vxe-column field="updateTime" title="更新时间"width="150" :header-align="'center'"></vxe-column>
      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTableConfig.pageConfig.pageNum"
        :page-size.sync="mainTableConfig.pageConfig.pageSize"
        :total="mainTableConfig.pageConfig.total"
        @page-change="pageChange">
      </vxe-pager>
    </div>

    <!-- 添加或修改班组记事主对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="日期" prop="workDate">
          <el-date-picker v-model="form.workDate" type="date" placeholder="选择日期时间"></el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark" class="addItemModel">
          <el-input v-model="form.remark" placeholder="请输入备注"/>
        </el-form-item>
        <el-form-item label="班组名称" prop="workGroupName">
          <el-select v-model="form.workGroupName" placeholder="请选择" style="width: 220px;">
            <el-option
              v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="班别名称" prop="workClassName" class="addItemModel">
          <el-select v-model="form.workClassName" placeholder="请选择" style="width: 220px;">
            <el-option
              v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="记录信息" prop="recordInfo">
          <el-input autosize  type="textarea" v-model="form.recordInfo" placeholder="请输入记录信息" style="width: 538px;"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listMaster,
    addMaster,
    editMaster,
    delMaster
  } from "@/api/teamnotes/master";
  import dayjs from "dayjs";

  export default {
    name: "Master",
    dicts: [
      'common_workGroup',
      'common_workClass',
    ],
    data() {
      return {
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 班组记事主表格数据
        masterList: [],
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNums: 1,
          pageSizes: 10,
          prodCenterCode: null,
          prodCenterName: null,
          workGroupName: null,
          workClassName: null,
          chargeHand: null,
          assistantChargeHand: null,
          recordInfo: null,
          remark: null,
          operator: null,
          operatTime: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          role: null,
          createTimeStart:null,
          createTimeEnd:null,
          workDate: null
        },
        // 更换日期 插件
        createTimeArr:[],
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          prodCenterCode: [
            {
              required: true, message: "加工中心编码不能为空", trigger: "blur" }
          ],
          workGroupName: [
            {
              required: true, message: "班组名称不能为空", trigger: "blur" }
          ],
        },
        tableData: [],
        selectVO: '',
        // 放灰时间
        greyOutTimeArr:[],
        startOfDay:null,
        endOfDay:null,

      };
    },
    created() {
      const now = new Date();
      // 获取当天 0点
      this.startOfDay = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        0, 0, 0 // 时、分、秒设为 0
      );

      // 获取当天 23:59:59
      this.endOfDay = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        23, 59, 59 // 时、分、秒设为 23:59:59
      );
      this.createTimeArr.push(this.startOfDay);
      this.createTimeArr.push(this.endOfDay);
      this.getList(null);
    },
    methods: {
      /*  获取加工中心编码  */
      getProdCemterCode() {
        return this.$route.query.prodCenterCode;
      },
      /*  获取角色  */
      getRole() {
        return this.$route.query.role;
      },

      /** 查询班组记事主列表 */
      getList(selectVO) {
        this.loading = true;
        if (selectVO) {
          this.selectVO = selectVO;
        }
        this.queryList();
      },
      queryList() {
        if (this.createTimeArr != null && this.createTimeArr.length == 2) {
          this.queryParams.greyOutStartTime = dayjs(this.createTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.queryParams.greyOutEndTime = dayjs(this.createTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }else{
          this.queryParams.greyOutStartTime = null;
          this.queryParams.greyOutEndTime = null;
        }
        this.queryParams.prodCenterCode = this.getProdCemterCode();
        this.queryParams.role = this.getRole();
        listMaster(this.queryParams, this.selectVO).then(response => {
          this.tableData = response.data
          this.mainTableConfig.pageConfig.total = response.total
          this.loading = false;
        });
      },
      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.queryParams.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.queryParams.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryList()
      },
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
          fid: null,
          prodCenterCode: null,
          prodCenterName: null,
          workGroupName: null,
          workClassName: null,
          chargeHand: null,
          assistantChargeHand: null,
          recordInfo: null,
          remark: null,
          operator: null,
          operatTime: null,
          createBy: null,
          createTime: null,
          updateBy: null,
          updateTime: null,
          role: null,
          gunBlockNum: null,
          ironReduction: null,
          greyOut: null,
          returnAcidSieve: null,
          greyOutStartTime: null,
          greyOutEndTime: null,
        };
        this.greyOutTimeArr = null;
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
        this.createTimeArr = [];
        this.resetForm("queryForm");
        this.handleQuery();
      },

      /** 新增按钮操作 */
      handleAdd() {
        this.reset();
        this.open = true;
        this.title = "添加班组记事";
        const now = new Date();
        // 获取当天
        const startOfDay = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate()
        );
        this.form.workDate = startOfDay;
      },

      /** 提交按钮 */
      submitForm() {
        if(this.greyOutTimeArr != null) {
          if (this.greyOutTimeArr.length == 2) {
            this.form.greyOutStartTime = dayjs(this.greyOutTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.form.greyOutEndTime = dayjs(this.greyOutTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.form.prodCenterCode = this.getProdCemterCode();
        this.form.role = this.getRole();
        this.form.workDate = dayjs(this.form.workDate).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        let tTeamNotesDetail={};
        tTeamNotesDetail.gunBlockNum = this.form.gunBlockNum // 堵枪次数
        tTeamNotesDetail.ironReduction = this.form.ironReduction // 化铁
        tTeamNotesDetail.greyOut = this.form.greyOut // 化铁
        tTeamNotesDetail.returnAcidSieve = this.form.returnAcidSieve // 返酸筛下
        tTeamNotesDetail.greyOutStartTime = this.form.greyOutStartTime
        tTeamNotesDetail.greyOutEndTime = this.form.greyOutEndTime
        let combinedObj = {...this.form, ...tTeamNotesDetail}
        addMaster(combinedObj).then(response=>{
          this.$modal.msgSuccess("添加成功");
          this.open = false;
          this.getList();
          this.reset();
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const id = row.id || this.ids
        this.$modal.confirm('确定删除选中的数据吗？').then(function () {
          console.log("id:",JSON.stringify(id))
          return delMaster(id);
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }).catch(() => { });

      },
      /** 导出按钮操作 */
      handleExport()
      {
        this.download('api/teamNotes/master/export', {
          ...this.queryParams
        }, `master_${new Date().getTime()}.xlsx`)
      },
      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        row.row.workDate = dayjs(row.row.workDate).format("YYYY-MM-DD HH:mm:ss");
        console.log(row.row.workDate)
        editMaster(row.row).then(response=>{
          this.$modal.msgSuccess("编辑成功");
        });
      },
      /*多选框触发事件*/
      handleCheckboxChange({ records, rowIndex, row }) {
        this.ids = records.map(item => item.fid)
        this.single = records.length !== 1
        this.multiple = !records.length
      },

    }
  }
  ;
</script>

<style lang="scss"  scoped>
  /*新增弹框 班别名称*/
  .addItemModel{
    width: 300px;
    z-index: auto;
    position: fixed;
    margin-top: -62px;
    margin-left: 318px;
  }
</style>
