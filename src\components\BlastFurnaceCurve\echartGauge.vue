<template>
  <div class="echartsG_box">
    <div class="title-bar">
      <div class="title-text">铁水温度</div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉'}" @click="chagngeGl('一高炉')">一高炉</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉'}" @click="chagngeGl('二高炉')">二高炉</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div class="echartsData-show">
        <div class="echarts-data">
          <div class="dataMark">安全</div>
          <div class="dataLast">
            <span>1450</span>
            <span style="padding: 0 2px 5px 3px;">-</span>
            <span>1550</span>
            <span>℃</span>
          </div>
        </div>
        <div style="width: 80%;height: 20%;border-bottom: 2px dashed #D2D2D2;margin: 0 0 0 15%;"></div>
        <div class="echarts-data">
          <div class="dataMark">预警</div>
          <div class="dataLast">
            <span>1450</span>
            <span style="padding: 0 2px 4px 3px;">-</span>
            <span>1550</span>
            <span>℃</span>
          </div>
        </div>
      </div>
      <div ref="echartsGauge" class="echartsGauge"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenTemperature } from "@/api/analyse/blastfurnace";

export default {
  name: 'EChartsG',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      activeGL: '一高炉',
      chart: null,
      param: {
        prodCenterCode: '1'
      }
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    initValue() {
      ironScreenTemperature(this.param).then(response => {
        if (response.code === 200) {
          var data = response.data;
          var valueHeat = 0;
          valueHeat = data.highestPhysicalHeat;
          this.$nextTick(() => {
            this.initChart(valueHeat);
            window.addEventListener('resize', this.resizeChart);
          })
        } else {
          this.$message.error(response.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(valueHeat) {
      const chartDom = this.$refs.echartsGauge;
      this.chart = echarts.init(chartDom);
      var colors = ["#E8FCFF", "#A8DD95", "#F71339"];
      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(255, 255, 255, 1)',
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',
          padding: 10
        },
        series: [
          {
            name: "数据",
            type: 'gauge',
            radius: '99%',
            center: ['45%', '52.5%'],
            z: 0,
            min: 0,
            max: 2000,
            axisLine: {
              lineStyle: {
                width: 10,
                color: [
                  [0.1, colors[0]],
                  [0.4, new echarts.graphic.LinearGradient(
                    0, 1, 0, 0,
                    [{
                      offset: 0,
                      color: colors[0]
                    }, {
                      offset: 0.8,
                      color: colors[1]
                    }]
                  )],
                  [0.6, colors[1]],
                  [0.9, new echarts.graphic.LinearGradient(
                    0, 1, 0, 0,
                    [{
                      offset: 0,
                      color: colors[2]
                    }, {
                      offset: 0.6,
                      color: colors[1]
                    }]
                  )],
                  [1, colors[2]]
                ],
              }
            },
            tooltip: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            },
            detail: {
              //说明数字大小
              formatter: function (value) {
                return value + ' ℃' + ' \n' + '正常';
              },
              offsetCenter: ['0%', '80%'],
              fontSize: 12,
              fontWeight: 'bolder',
              width: 160,
              color: '#000',
              padding: [-8, 15, 0, 15]
            },
            inter: {
              show: false,
            },
            pointer: {
              show: false
            },
            data: [{
              value: valueHeat
            }],
          },
          {
            name: '间隔',
            type: 'gauge',
            radius: '99%',
            center: ['45%', '52.5%'],
            splitNumber: 78,
            emphasis: {
              scale: true
            },
            axisTick: {
              show: false
            },
            splitLine: {
              length: 90,
              lineStyle: {
                width: 3,
                color: "#fff",//改变小格子的线颜色
              }
            },
            axisLabel: {
              show: false
            },
            pointer: {
              show: false
            },
            axisLine: {
              lineStyle: {
                opacity: 0
              }
            },
            detail: {
              show: false
            },
            data: [{
              value: 0,
              name: ""
            }]
          },
          {
            name: "数据刻度",
            type: 'gauge',
            radius: '80%',
            center: ['45%', '52.5%'],
            z: 2,
            min: 0,
            max: 2000,
            splitNumber: 5,
            emphasis: {
              scale: true
            },
            axisTick: {
              length: 3,
              lineStyle: {
                color: '#000'
              }
            },
            axisLabel: {
              show: true,
              color: '#000',
              fontSize: 10
            },
            splitLine: {
              length: 3,
              lineStyle: {
                width: 2,
                color: "#000",//改变小格子的线颜色
              }
            },
            axisLine: {
              lineStyle: {
                opacity: 0
              }
            },
            pointer: {
              show: false
            },
            detail: {
              show: false
            },
            data: [{
              value: 0,
              name: ""
            }]
          },
          {
            name: '温度',
            type: 'gauge',
            radius: '81%',
            center: ['45%', '52.5%'],
            min: 0,
            max: 2000,
            z: 10,
            axisLine: {
              show: false,
              lineStyle: {
                width: 0,
                color: [
                  [0.3, '#000'], // 0-30% 颜色
                  [0.7, '#000'], // 30-70% 颜色
                  [1, '#000']    // 70-100% 颜色
                ]
              }
            },
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: false
            },
            splitLabel: {
              show: false
            },
            pointer: {
              show: true,
              length: '100%',
              width: 2,
            },
            detail: {
              show: false,
            },
            title: {
              show: false
            },
            data: [{
              value: valueHeat,
            }]
          },
        ],
      };
      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    chagngeGl(name) {
      if (name == '一高炉') {
        this.activeGL = '一高炉';
        this.param.prodCenterCode = '1';
        this.initValue();
      }
      if (name == '二高炉') {
        this.activeGL = '二高炉';
        this.param.prodCenterCode = '2';
        this.initValue();
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsG_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-text {
  width: 160px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 18px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsData-show {
  width: 30%;
  height: 80%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-evenly;
}
.echarts-data {
  width: 100%;
  height: 15%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0 0 30px;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 16px;
  color: black;
}
.dataMark {
}
.dataLast {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: center;
  align-items: center;
  line-height: inherit;
}
.echartsGauge {
  width: 70%;
  height: 100%;
}
</style>