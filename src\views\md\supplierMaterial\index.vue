<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="供应商" prop="supplierName">
        <el-input v-model="queryParams.supplierName" placeholder="请输入供应商名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="物料" prop="materialName">
        <el-input v-model="queryParams.materialName" placeholder="请输入物料名称" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
         >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_table">
      <dlTable size="small" refName="dlTable" :stripe="true" :border="true" :height="660" :columns="columns"
        :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig" @handleOrder="getList"
        @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
        @page-current-change="numChange">
      </dlTable>
    </div>

    <!-- 添加或修改供应商-物料关系对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="供应商" prop="supplierCode">
          <el-select v-model="form.supplierCode" filterable placeholder="清选择供应商" style="width: 100%;">
            <el-option v-for="dict in supplierList" :key="dict.code" :label="dict.fullName" :value="dict.code">
              <span style="float: left">{{ dict.fullName }}-{{ dict.code }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="物料" prop="materialNumber">
          <el-select v-model="form.materialNumber" filterable placeholder="清选择物料" style="width: 100%;">
            <el-option v-for="dict in mateList" :key="dict.materialNumber" :label="dict.materialName"
              :value="dict.materialNumber">
              <span style="float: left">{{ dict.materialName }}-{{ dict.materialNumber }}</span>
            </el-option>
          </el-select>
        </el-form-item>


        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMaterial,
  getMaterial,
  delMaterial,
  saveOrUpdate
} from "@/api/md/supplierMaterial";
import { queryAllUsed as materialQueryAllUsed } from "@/api/md/material";
import { queryAllUsed } from "@/api/md/supplier"

export default {
  name: "Material",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      mateList: [],
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        supplierName: null,
        materialNumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        supplierCode: [
          {
            required: true, message: "供应商不能为空", trigger: "blur"
          }
        ],
        materialNumber: [
          {
            required: true, message: "物料不能为空", trigger: "blur"
          }
        ],
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: false // 表头排序是否保存在localStorage中
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      columns: [
        {
          label: 'ID', // 表头描述
          fieldIndex: 'id', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '供应商编码', // 表头描述
          fieldIndex: 'supplierCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '供应商名称', // 表头描述
          fieldIndex: 'supplierName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '物料编码', // 表头描述
          fieldIndex: 'materialNumber', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '物料名称', // 表头描述
          fieldIndex: 'materialName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '状态', // 表头描述
          fieldIndex: 'status', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建人', // 表头描述
          fieldIndex: 'createBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建时间', // 表头描述
          fieldIndex: 'createTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '修改人', // 表头描述
          fieldIndex: 'updateBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '修改时间', // 表头描述
          fieldIndex: 'updateTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        }

      ],
      tableData: [],
      selectVO: '',
    };
  },
  created() {
    queryAllUsed().then((response) => {
      this.supplierList = response.data;
    });
    materialQueryAllUsed().then((response) => {
      this.mateList = response.data;
    });
    this.getList(null);
  },
  methods: {
    /** pageNum事件 */
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum;
      this.queryParams.pageNum = pageNum;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize;
      this.queryParams.pageSize = pageSize;
      this.selectVO = selectVO;
      this.getList(selectVO);
    },
    /** 查询供应商-物料关系列表 */
    getList(selectVO) {
      this.loading = true;
      if (selectVO) {
        this.selectVO = selectVO;
      }
      this.queryList();
    },
    queryList() {
      listMaterial(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.rows
        this.pageConfig.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        supplierCode: null,
        materialNumber: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商-物料关系";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMaterial(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改供应商-物料关系";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          })
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {

      const schemeId = row.id || this.ids
      this.$modal .confirm( '确定删除选中的供应关系吗?' )
        .then(function () {
          return delMaterial(schemeId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/api/md/supplierMaterial/export', {
        ...this.queryParams
      }, `供应商物料_${new Date().getTime()}.xlsx`)
    }
  }
}
  ;
</script>
