<template>
  <div class="app-container">
    <child-component :initial-message="parentMessage" @update-message="handleMessageUpdate" ref="childRef" :parentMessage="parentData"></child-component>
    <vxe-table
      class="vxeTable"
      border
      :edit-config="{trigger: 'click', mode: 'row'}"
      @edit-closed="editClosedEvent"
      :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
      @checkbox-change="handleCheckboxChange"
      :data="tableData">
      <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
      <vxe-column v-for="(column, index) in columns"  :field="column.field" :title="column.title" width="auto"></vxe-column>
    </vxe-table>
  </div>
</template>


<script>
  import ChildComponent from './component/selectComponent.vue'; // 引入子组件

    export default {
      name: "beltParticleSummary",
      components: { // 局部注册子组件
        ChildComponent
      },
      data(){
        return{
          tableData:[],
          columns:[
            // { field: 'name', title: 'Name' ,width:'auto'},
            // { field: 'sex', title: 'Sex' ,width:'auto'}
          ],
          parentMessage: 'Message from Parent', // 初始消息传递给子组件
          refreshChild: this.refreshChild,
          parentData: '' // 这是要传递给子组件的数据
        }
      },
      created() {
          this.queryList();
      },
      methods:{

        /* 进入页面刷新数据*/
        refreshChild() {
          this.$refs.childRef.queryChildList(); // 调用子组件的方法
        },

        queryList(){
          this.$refs.childRef.queryChildList(); // 调用子组件方法
          this.handleMessageUpdate(newMessage,clumnData);

        },
        handleMessageUpdate(newMessage,clumnData) { // 处理子组件传递过来的新消息
          this.columns = []
          console.log("newMessage:",newMessage)
          console.log("clumnData:",clumnData)
          this.tableData = newMessage; // 更新父组件的数据以反映子组件的更改
          let tabData = newMessage
          if(this.tableData !=[]){
            for(let i=0;i< clumnData.length;i++){
              let obj={field:'',title:''}
              obj.title = clumnData[i].title
              obj.field = clumnData[i].colum
              this.columns.push(obj)
            }


            // for(let i = 0;i<this.tableData.length;i++){
            //   let obj={field:'',title:''}
            //   obj.title = this.tableData[i].title
            //   obj.field = this.tableData[i].colum
            //   this.columns.push(obj)
            // }
            console.log("this.columns:",JSON.stringify(this.columns))
          }
        },
        /*关闭编辑框触发 失去焦点 进行保存数据*/
        editClosedEvent(row, column) {
          console.log("editClosedEvent row:",JSON.stringify(row))
          console.log("editClosedEvent column:",JSON.stringify(column))
        },
        /*多选框触发事件*/
        handleCheckboxChange({ records, rowIndex, row }) {
          console.log("handleCheckboxChange records:",records)
          console.log("handleCheckboxChange rowIndex:",rowIndex)
          console.log("handleCheckboxChange row:",row)
        },
      },
      mounted() {
        // 父组件加载完成后的操作，比如刷新子组件数据
        this.refreshChild();
      }
    }
</script>

<style scoped>
  .vxeTable{
    margin-top: -8px;
  }

</style>
