<template>
  <div class="app-container" style="padding: 10px;">
    <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="68px" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="时间间隔">
        <el-input-number v-model="queryParams.timeInterge" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="时间间隔">
        <el-select v-model="queryParams.timeType" placeholder="请选择">
          <el-option v-for="item in timeTypes" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" type="primary" size="mini" @click="queryList">查询</el-button>
        <el-button icon="el-icon-search" type="primary" size="mini" @click="handleExport">导出Excel</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="8">
        <el-tabs type="border-card">
          <el-tab-pane label="点位列表">
            <el-form ref="queryPointParams" :inline="true" :model="queryPointParams" label-width="auto" size="small">
              <el-form-item label="" prop="pointProcess">
                <el-select v-model="queryPointParams.pointProcess" allow-create clearable filterable
                           placeholder="请输入点位业务" @change="pointQuery"
                >
                  <el-option v-for="item in processList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="pointOperation">
                <el-select v-model="queryPointParams.pointOperation" allow-create clearable filterable
                           placeholder="请输入点位工序" @change="pointQuery"
                >
                  <el-option v-for="item in operList" :key="item" :label="item" :value="item">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="" prop="pointName">
                <el-input v-model="queryPointParams.pointName" @keyup.enter.native="pointQuery"/>
              </el-form-item>
            </el-form>
            <dlTable :basicConfig="pointTableConfig.basicConfig" :border="true" :columns="pointTableConfig.columns"
                     :height="dltableHeight-36"
                     :pageConfig="pointTableConfig.pageConfig" :stripe="true"
                     :tableData="pointTableConfig.tableData"
                     refName="dlTable" @selection-change="handleSelectionChange"
            >
            </dlTable>
          </el-tab-pane>
        </el-tabs>

      </el-col>
      <el-col :span="16">
        <el-tabs style="margin-left: 10px;" type="border-card">
          <el-tab-pane label="趋势图">
            <div ref="chart4" class="chart4" style="width: 100%; height: 100%; margin-left: auto; margin-right: auto;">
            </div>
          </el-tab-pane>
          <el-tab-pane label="点位详细值">
            <div class="mes_new_table">
              <vxe-grid
                ref="tableMainRef"
                :column-config="{resizable: true}"
                :height="dltableHeight"
                v-bind="mainTable.gridOptions"
              >
              </vxe-grid>
            </div>
          </el-tab-pane>
        </el-tabs>

      </el-col>
    </el-row>

  </div>
</template>
<script>
import dayjs from 'dayjs'
import {
  nopageList,
  getProcess,
  getOperList,
  analysisDelay
} from '@/api/collect/point'
import { list } from '@/api/collect/influxdb'

export default {
  name: 'ZhiJinQiao',
  data() {
    return {
      chart4: null,
      dltableHeight: 660,
      timeTypes: [{
        value: '秒',
        label: '秒'
      }, {
        value: '分',
        label: '分'
      }, {
        value: '时',
        label: '时'
      }],
      processList: [],
      operList: [],
      queryParams: {
        timeInterge: 1,
        timeType: '分',
        points: ''
      },
      queryPointParams: {
        pointProcess: null,
        pointOperation: null
      },
      // 日期范围
      dateRange: [],
      basicConfig: {
        index: false, // 是否启用序号列
        needPage: false, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: false, // 是否启用多选框
        indexWidth: 150, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: false // 表头排序是否保存在localStorage中
      },
      pointTableConfig: {
        ids: [],
        tableData: [],
        basicConfig: {
          index: false, // 是否启用序号列
          needPage: false, // 是否展示分页
          indexName: null, // 序号列名(默认为：序号)
          selectionType: true, // 是否启用多选框
          indexWidth: null, // 序号列宽(默认为：50)
          indexFixed: null, // 序号列定位(默认为：left)
          settingType: true, // 是否展示表格配置按钮
          headerSortSaveType: false // 表头排序是否保存在localStorage中
        },
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        },
        columns: [
          {
            label: '点位名称', // 表头描述
            fieldIndex: 'pointName', // 表格显示内容绑定值
            width: 200,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'POINT_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },
          {
            label: '点位编号', // 表头描述
            fieldIndex: 'pointCode', // 表格显示内容绑定值
            width: 150,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'POINT_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },

          {
            label: '点位工序', // 表头描述
            fieldIndex: 'pointProcess', // 表格显示内容绑定值
            width: 100,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'POINT_PROCESS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },
          {
            label: '点位业务', // 表头描述
            fieldIndex: 'pointOperation', // 表格显示内容绑定值
            width: 100,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'POINT_OPERATION',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },

          {
            label: '存储编号', // 表头描述
            fieldIndex: 'storeCode', // 表格显示内容绑定值
            width: 100,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'STORE_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },
          {
            label: '点位描述', // 表头描述
            fieldIndex: 'pointDesc', // 表格显示内容绑定值
            width: 300,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'POINT_DESC',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },
          {
            label: '最后采集时间', // 表头描述
            fieldIndex: 'lastCollectionTime', // 表格显示内容绑定值
            width: 150,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'LAST_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },
          {
            label: '开始采集时间', // 表头描述
            fieldIndex: 'startCollectionTime', // 表格显示内容绑定值
            width: 150,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'START_COLLECTION_TIME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },

          {
            label: '状态', // 表头描述
            fieldIndex: 'status', // 表格显示内容绑定值
            width: 130,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'STATUS',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          },

          {
            label: '备注', // 表头描述
            fieldIndex: 'remark', // 表格显示内容绑定值
            width: 130,
            sortable: false, // 此属性可以设置排序
            filterable: false, // 是否筛选 默认为false
            searchField: 'REMARK',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
            concise: false // 是否显示筛选icon 和排序 icon
          }
        ]
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 100, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [100, 200, 500, 1000]// 分页器分页待选项
      },

      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: [],
          ratioMasterIds: []
        }
      },
      tableData: [],
      option4: {

        tooltip: {
          trigger: 'axis',
          confine: true,
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
          orient: 'horizontal',
          left: 'left',
          top: 'top',
          type: 'scroll'
        },
        dataZoom: [{ // 这部分是dataZoom组件
          type: 'inside', // 表示内置型数据区域缩放组件
          start: 0, // 数据窗口范围的起始百分比
          end: 80 // 数据窗口范围的结束百分比
        }],
        grid: {
          left: '1%',
          right: '1%',
          bottom: '%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
          }
        ],
        yAxis: [
          {
            type: 'value'
          }
        ],
        series: [
          {
            name: 'Email',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: 'Union Ads',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'Video Ads',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: 'Direct',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: 'Search Engine',
            type: 'line',
            stack: 'Total',
            label: {
              show: true,
              position: 'top'
            },
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          }
        ]
      }

    }
  },
  created() {
    getProcess().then(response => {
      this.processList = response.data
    })
    getOperList().then(response => {
      this.operList = response.data
    })
    this.dateRange.push(dayjs(new Date()).startOf('day'))
    this.dateRange.push(dayjs(new Date()))
    this.pointQuery()
  },
  methods: {
    handleSelectionChange(selection) {
      this.pointTableConfig.ids = selection.map(item => item.pointCode)
    },
    pointQuery() {
      nopageList(this.queryPointParams).then(response => {
        this.pointTableConfig.tableData = response.data
      })
    },
    queryList() {
      if (this.pointTableConfig.ids.length == 0) {
        this.$message.error('请选择点位')
        return
      }
      if (this.dateRange.length == 2) {
        this.queryParams.dtstart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtend = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.points = this.pointTableConfig.ids
      this.mainTable.gridOptions.columns = []
      list(this.queryParams).then(res => {
        this.columns = []
        res.data.table.columns.forEach(element => {
          this.mainTable.gridOptions.columns.push({
            field: element,
            title: element,
            width: '180'
          })
        })
        this.mainTable.gridOptions.data = res.data.table.data

        this.chart4 = this.$echarts.init(this.$refs.chart4)
        this.option4.legend.data.splice(0, this.option4.legend.data.length)
        this.option4.xAxis[0].data.splice(0, this.option4.xAxis[0].data.length)
        this.option4.yAxis.splice(0, this.option4.yAxis.length)
        this.option4.series.splice(0, this.option4.series.length)
        res.data.echars.legend.forEach(element => {
          this.option4.legend.data.push(element)
          var item = {
            type: 'value',
            name: 'SJC_SJ360_T1301',
            axisLine: {
              show: true
            }
          }
          item.name = element
          this.option4.yAxis.push(item)
        })

        res.data.echars.xAxis.forEach(element => {
          if (this.option4.xAxis[0].data.indexOf(element) == -1) {
            this.option4.xAxis[0].data.push(element)
          }
        })

        res.data.echars.series.forEach(element => {

          var optionItem = {
            name: 'Email',
            type: 'line',
            stack: 'Total',
            areaStyle: {},
            emphasis: {
              focus: 'series'
            },
            data: []
          }
          optionItem.name = element.name
          optionItem.data = element.data
          this.option4.series.push(optionItem)

        })
        this.chart4.clear()
        this.chart4.setOption(this.option4)
      })
    },
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '点位清单', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue2 = document.getElementsByClassName('chart4')[0].getBoundingClientRect().top
      var height = (document.body.clientHeight - topValue2 - 15)
      this.dltableHeight = height
      document.getElementsByClassName('chart4')[0].style.height = height + 'px'
    })
  }

}
</script>
<style scoped>
/deep/ .el-form-item--mini.el-form-item,
.el-form-item--small.el-form-item {
  margin-bottom: 10px;
}

/deep/ .el-tabs--border-card > .el-tabs__content {
  padding: 5px;
}
</style>
