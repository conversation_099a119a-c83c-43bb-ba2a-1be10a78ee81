<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="分组">
        <el-select v-model="queryParams.categoryGroup" style="width: 100%;" @change="change">
          <el-option v-for="dict in dict.type.material_category_group" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="物料编码" prop="materialNumber">
        <el-input v-model="queryParams.materialNumber" placeholder="请输入编码" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="名称" prop="materialName">
        <el-input v-model="queryParams.materialName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-col :span="4" style="padding-right: 10px">
        <el-tree class="tree-border" :data="materialCategoryList" ref="categoryTree" default-expand-all node-key="id"
          empty-text="加载中，请稍候" :props="defaultProps" @node-click="categoryTreeNodeClick"></el-tree>
      </el-col>

      <el-col :span="20">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
              @click="handleUpdate">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
              @click="handleDelete">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport">导入</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange" border
          style="width: 100%;">
          <af-table-column type="selection" :fit="false" width="55" align="left" header-align="center" />
          <af-table-column label="分类编码" fixed="left" align="left" header-align="center" prop="categoryCode" />
          <af-table-column label="分类名称" fixed="left" align="left" header-align="center" prop="categoryName" />
          <af-table-column label="分类分组" align="left" header-align="center" prop="categoryGroup" />
          <af-table-column label="物料编码" align="left" header-align="center" prop="materialNumber" />
          <af-table-column label="物料名称" align="left" header-align="center" prop="materialName" />
          <af-table-column label="创建者" align="left" header-align="center" prop="createBy" />
          <af-table-column label="创建时间" align="left" header-align="center" prop="createTime" />
          <af-table-column label="更新者" align="left" header-align="center" prop="updateBy" />
          <af-table-column label="更新时间" align="left" header-align="center" prop="updateTime" />
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />
      </el-col>
    </el-row>

    <!-- 添加或修改物料管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">

        <el-form-item label="分组" prop="categoryGroup">
          <el-select v-model="form.categoryGroup" style="width: 100%;" @change="changeedit">
            <el-option v-for="dict in dict.type.material_category_group" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <treeselect v-model="form.categoryId" :options="editmaterialCategoryList" :show-count="true"
            placeholder="请选择分类" />
        </el-form-item>
        <el-form-item label="物料" prop="materialNumber">
          <el-select v-model="form.materialNumber" filterable placeholder="请选择" style="width: 100%;">
            <el-option v-for="item in materialListEdit" :key="item.materialNumber" :label="item.materialName"
              :value="item.materialNumber">
              <span style="float: left">{{ item.materialName }}-{{ item.materialNumber }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{
      item.tMdMaterialCategory.categoryName }}</span>
            </el-option>
          </el-select>
        </el-form-item>




      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" /> 是否更新已存在的物料
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;"
            @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { queryAllUsed } from "@/api/md/material";
import {
  listMapping,
  getMapping,
  delMapping,
  addMapping,
  updateMapping,
  saveOrUpdate,
  getSaveEntity
} from "@/api/md/mapping";
import { treeselectByGroup } from "@/api/md/materialCategory";
import { queryByUnitGropCode } from "@/api/md/materialUnit";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Material",
  dicts: ["effective_or_not", "material_tags", "material_category_group"],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料管理表格数据
      materialList: [],
      materialListEdit: [],
      materialCategoryList: [],
      materialCategoryCurrent: {},
      editmaterialCategoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialCategoryCode: null,
        materialNumber: null,
        materialNumberDatacollect: null,
        materialName: null,
      },

      defaultProps: {
        children: "children",
        label: "label",
      },
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",

        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/api/md/materialcategorymapping/importData"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryGroup: [
          { required: true, message: "分组不能为空", trigger: "blur" },
        ],
        categoryId: [
          { required: true, message: "分类不能为空", trigger: "blur" },
        ],
        materialNumber: [
          { required: true, message: "编码不能为空", trigger: "blur" },
        ],

      },
    };
  },
  created() {
    this.queryParams.categoryGroup = '苍穹'
    this.change(this.queryParams.categoryGroup);
    queryAllUsed().then((response) => {
      this.materialListEdit = response.data;
    })
  },
  methods: {
    /** 查询物料管理列表 */
    getList() {
      this.loading = true;
      if (this.materialCategoryCurrent != null) {
        this.queryParams.categoryId = this.materialCategoryCurrent.id;
      }

      listMapping(this.queryParams).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        categoryGroup: null,
        categoryId: null,
        materialNumber: null,
      };
      this.resetForm("form");
    },
    change(val) {
      treeselectByGroup(val).then((response) => {
        const result = [{ id: 0, label: '(全部)', children: response.data }];
        this.materialCategoryList = result;
        this.getList();
      });
    },
    changeedit(val) {
      treeselectByGroup(val).then((response) => {
        this.editmaterialCategoryList = response.data;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.mappingId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料归属分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const mappingId = row.mappingId || this.ids;
      console.log(mappingId)
      getSaveEntity(mappingId).then((response) => {
        treeselectByGroup(response.data.categoryGroup).then((response2) => {
          this.editmaterialCategoryList = response2.data;
          console.log(response.data)
          const obj = response.data;
          this.form = obj;
          this.open = true;
          this.title = "编辑物料归属分类";
        });

      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const mappingIds = row.mappingId || this.ids;
      this.$modal
        .confirm('确认删除物料归属分类吗？')
        .then(function () {
          return delMapping(mappingIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/api/md/materialcategorymapping/export",
        {
          ...this.queryParams,
        },
        `物料归属分类${new Date().getTime()}.xlsx`
      );
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "物料导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('/api/md/materialcategorymapping/importTemplate', {
      }, `物料模板_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    categoryTreeNodeClick(data) {
      if (data.id != 0) {
        this.materialCategoryCurrent = data;
      } else {
        this.materialCategoryCurrent = {};
      }

      this.getList();
    },
  },
};
</script>

<style scoped>
/* 单元格回行设置 */
/deep/ .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
}
</style>