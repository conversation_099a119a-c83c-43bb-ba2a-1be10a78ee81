import request from '@/utils/request'

// 查询 子表 列表
export function getByConfigID(configDetailId) {
  return request({
    url: '/api/calculatedCollectConfigDetail/detail/getByConfigID/' + configDetailId,
    method: 'get'
  })
}

// 根据分析方式 显示参数配置 填入项
export function displayDetailListNum(data) {
  return request({
    url: '/api/calculatedCollectConfigDetail/detail/displayDetailListNum',
    method: 'post',
    data: data
  })
}

