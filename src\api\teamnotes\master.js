import request from '@/utils/request'

// 查询班组记事主列表
export function listMaster(query,selectVO) {
  return request({
    url: '/api/teamNotes/master/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}


// 新增班组记事主
export function addMaster(data) {
  return request({
    url: '/api/teamNotes/master/add',
    method: 'post',
    data: data
  })
}

// 修改班组记事
export function editMaster(data) {
  return request({
    url: '/api/teamNotes/master/edit',
    method: 'put',
    data: data
  })
}



// 删除班组记事主
export function delMaster(data) {
  return request({
    url: '/api/teamNotes/master/delete',
    method: 'post',
    data: data
  })
}

///////////////// 日 班组记事 ////////////////////////
export function listMasterDay(query,selectVO) {
  return request({
    url: '/api/teamNotes/master/listMasterDay',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}
