<template>
  <div class="stacker-reclaimer-component">
    <!-- 堆取料机主体 -->
    <div class="stacker-reclaimer" 
         :class="deviceClasses"
         :style="deviceStyle"
         @click="handleClick"
         @mouseenter="handleMouseEnter"
         @mouseleave="handleMouseLeave">
      
      <!-- 设备主体 - 侧视简化图 -->
      <div class="device-body">
        <!-- 轨道基础 -->
        <!-- <div class="track-base"></div> -->
        
        <!-- 下部走行装置 -->
        <div class="travel-mechanism">
          <div class="wheel wheel-front"></div>
          <div class="wheel wheel-rear"></div>
          <div class="bogie"></div>
        </div>
        
        <!-- 上部旋转平台 -->
        <div class="rotating-platform" :style="platformRotationStyle">
          <!-- 机房 -->
          <div class="machine-house">
            <div class="house-body"></div>
            <div class="house-roof"></div>
            <div class="house-windows"></div>
            <!-- 设备编号 -->
            <div class="device-number">{{ deviceNumber }}</div>
          </div>
          
          <!-- 悬臂结构 -->
          <div class="boom-structure" :style="boomRotationStyle">
            <!-- 悬臂主梁 -->
            <div class="boom-beam"></div>
            <!-- 悬臂桁架 -->
            <div class="boom-truss">
              <div class="truss-line" v-for="i in 10" :key="i"></div>
            </div>

            <!-- 斗轮装置 -->
            <div class="bucket-wheel-assembly">
              <div class="bucket-wheel" :class="{ 'spinning': isWorking }">
                <div class="wheel-center"></div>
                <div class="wheel-buckets">
                  <div class="bucket" v-for="i in 8" :key="i"
                       :style="{ transform: `rotate(${i * 45}deg)` }"></div>
                </div>
              </div>
              <!-- 斗轮支撑 -->
              <div class="wheel-support"></div>
              <!-- 斗轮驱动装置 -->
              <div class="wheel-drive"></div>
            </div>
          </div>
          
          <!-- 配重臂 -->
          <div class="counterweight-arm">
            <div class="counterweight"></div>
          </div>
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <!-- <div class="status-indicators">
        <div class="status-dot" :class="statusClass"></div>
        <div class="operation-mode" v-if="data.operation.mode !== 'idle'">
          {{ operationText }}
        </div>
      </div> -->
      
      <!-- 位置标签 -->
      <div class="position-label">
        {{ Math.round(data.position.scale) }}
      </div>
      
      <!-- 工作目标指示 -->
      <!-- <div v-if="data.operation.targetMaterial" 
           class="target-indicator"
           :class="{ 'pulsing': data.equipment.status === 'working' }">
        {{ data.operation.targetMaterial }}
      </div> -->
    </div>
    
    <!-- 详细信息面板（悬停显示） -->
    <div v-if="showDetails" class="details-panel" :style="detailsPanelStyle">
      <div class="panel-header">
        <h4>{{ data.name }}</h4>
        <span class="status-text">{{ statusText }}</span>
      </div>
      <div class="panel-content">
        <div class="info-row">
          <span>位置:</span>
          <span>{{ Math.round(data.position.scale) }}m ({{ data.position.percentage }}%)</span>
        </div>
        <div class="info-row">
          <span>悬臂角度:</span>
          <span>{{ data.equipment.boomAngle }}°</span>
        </div>
        <div class="info-row" v-if="data.operation.mode !== 'idle'">
          <span>作业模式:</span>
          <span>{{ operationText }}</span>
        </div>
        <div class="info-row" v-if="data.operation.targetMaterial">
          <span>目标物料:</span>
          <span>{{ data.operation.targetMaterial }}</span>
        </div>
        <div class="info-row" v-if="data.operation.efficiency > 0">
          <span>作业效率:</span>
          <span>{{ data.operation.efficiency }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StackerReclaimer',
  props: {
    // 设备数据
    data: {
      type: Object,
      required: true,
      default: () => ({
        id: 'sr-001',
        name: '堆取料机#1',
        position: {
          percentage: 50,
          scale: 500
        },
        equipment: {
          boomAngle: 0,
          status: 'idle', // idle, working, moving, maintenance
          bucketWheelSpeed: 0
        },
        operation: {
          mode: 'idle', // idle, stacking, reclaiming
          targetMaterial: '',
          efficiency: 0
        }
      })
    },
    
    // 显示设置
    size: {
      type: String,
      default: 'medium', // small, medium, large
      validator: value => ['small', 'medium', 'large'].includes(value)
    },
    
    // 是否可交互
    interactive: {
      type: Boolean,
      default: true
    },
    
    // 位置样式
    positionStyle: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      showDetails: false,
      detailsPanelPosition: { x: 0, y: 0 }
    };
  },
  
  computed: {
    // 设备编号
    deviceNumber() {
      return this.data.name.match(/\d+/)?.[0] || '1';
    },
    
    // 设备样式类
    deviceClasses() {
      return [
        `size-${this.size}`,
        `status-${this.data.equipment.status}`,
        {
          'interactive': this.interactive,
          'working': this.isWorking,
          'moving': this.data.equipment.status === 'moving'
        }
      ];
    },
    
    // 设备样式
    deviceStyle() {
      return {
        ...this.positionStyle
      };
    },
    
    // 平台旋转样式
    platformRotationStyle() {
      return {
        transform: 'rotate(0deg)', // 整机朝向，暂时固定
        transformOrigin: 'center center'
      };
    },
    
    // 悬臂旋转样式
    boomRotationStyle() {
      return {
        transform: `rotate(${this.data.equipment.boomAngle}deg)`,
        transformOrigin: '0 50%',
        transition: this.data.equipment.status === 'moving' ? 
          'transform 1.5s ease-in-out' : 'transform 0.5s ease'
      };
    },
    
    // 是否正在工作
    isWorking() {
      return this.data.equipment.status === 'working' && 
             this.data.equipment.bucketWheelSpeed > 0;
    },
    
    // 状态样式类
    statusClass() {
      return `status-${this.data.equipment.status}`;
    },
    
    // 状态文本
    statusText() {
      const statusMap = {
        'idle': '待机',
        'working': '作业中',
        'moving': '移动中',
        'maintenance': '维护'
      };
      return statusMap[this.data.equipment.status] || '未知';
    },
    
    // 作业模式文本
    operationText() {
      const modeMap = {
        'stacking': '堆料',
        'reclaiming': '取料',
        'idle': '待机'
      };
      return modeMap[this.data.operation.mode] || '';
    },
    
    // 详情面板样式
    detailsPanelStyle() {
      return {
        left: `${this.detailsPanelPosition.x}px`,
        top: `${this.detailsPanelPosition.y}px`
      };
    }
  },
  
  methods: {
    // 点击事件
    handleClick() {
      if (!this.interactive) return;
      this.$emit('click', this.data);
    },
    
    // 鼠标进入
    handleMouseEnter(event) {
      if (!this.interactive) return;
      this.showDetails = true;
      this.updateDetailsPanelPosition(event);
      this.$emit('hover', this.data);
    },
    
    // 鼠标离开
    handleMouseLeave() {
      this.showDetails = false;
      this.$emit('leave', this.data);
    },
    
    // 更新详情面板位置
    updateDetailsPanelPosition(event) {
      const rect = event.currentTarget.getBoundingClientRect();
      this.detailsPanelPosition = {
        x: rect.right + 10,
        y: rect.top
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.stacker-reclaimer-component {
  position: relative;
  display: inline-block;
}

.stacker-reclaimer {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;

  &.interactive:hover {
    transform: scale(1.05);
  }

  &.working {
    animation: deviceWorking 3s ease-in-out infinite;
  }

  &.moving {
    animation: deviceMoving 2s ease-in-out infinite;
  }
}

@keyframes deviceWorking {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

@keyframes deviceMoving {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-1px); }
}

/* 设备主体 */
.device-body {
  position: relative;
  width: 120px;  /* 增大整体尺寸 */
  height: 80px;
}

/* 轨道基础 */
.track-base {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;  /* 加厚轨道 */
  background: linear-gradient(to bottom, #5a5a5a, #3a3a3a, #2a2a2a);
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
  border: 1px solid #1a1a1a;
}

.track-base::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 5%;
  width: 90%;
  height: 2px;
  background: linear-gradient(to right, #8a8a8a, #aaa, #8a8a8a);
  border-radius: 1px;
}

.track-base::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 8px,
    rgba(255, 255, 255, 0.1) 8px,
    rgba(255, 255, 255, 0.1) 10px
  );
}

/* 走行装置 */
.travel-mechanism {
  position: absolute;
  bottom: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;  /* 加宽走行装置 */
  height: 20px;
}

.wheel {
  position: absolute;
  width: 14px;  /* 加大车轮 */
  height: 14px;
  background: radial-gradient(circle, #2c3e50, #1a252f, #34495e);
  border: 2px solid #0f1419;
  border-radius: 50%;
  bottom: 0;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

.wheel::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, #555, #333);
  border-radius: 50%;
}

.wheel-front {
  left: 5px;
}

.wheel-rear {
  right: 5px;
}

.bogie {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;  /* 加宽转向架 */
  height: 12px;
  background: linear-gradient(to bottom, #34495e, #2c3e50, #1a252f);
  border-radius: 6px;
  border: 1px solid #0f1419;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.bogie::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 10%;
  width: 80%;
  height: 2px;
  background: linear-gradient(to right, #555, #777, #555);
  border-radius: 1px;
}

/* 旋转平台 */
.rotating-platform {
  position: absolute;
  bottom: 28px;  /* 提高位置 */
  left: 50%;
  transform: translateX(-50%);
  width: 90px;   /* 加大平台 */
  height: 50px;
  transform-origin: center center;
}

/* 平台基座 */
.rotating-platform::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 12px;
  background: linear-gradient(to bottom, #34495e, #2c3e50);
  border-radius: 6px;
  border: 1px solid #1a252f;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* 机房 */
.machine-house {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;   /* 加大机房 */
  height: 32px;
}

.house-body {
  width: 100%;
  height: 26px;  /* 加高机房 */
  background: linear-gradient(to bottom, #3498db, #2980b9, #1f4e79);
  border: 2px solid #1a4480;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  position: relative;
}

.house-body::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  height: 4px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
  border-radius: 2px;
}

.house-roof {
  position: absolute;
  top: -6px;
  left: -4px;
  width: 48px;   /* 加大屋顶 */
  height: 10px;
  background: linear-gradient(to bottom, #e74c3c, #c0392b, #922b21);
  border-radius: 5px 5px 0 0;
  border: 2px solid #7d1f1f;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.house-windows {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 8px;
  background: linear-gradient(45deg,
    rgba(255, 255, 255, 0.3) 25%,
    transparent 25%,
    transparent 75%,
    rgba(255, 255, 255, 0.3) 75%);
  background-size: 4px 4px;
}

.device-number {
  position: absolute;
  bottom: 1px;
  left: 50%;
  transform: translateX(-50%);
  color: white;
  font-size: 8px;
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
}

/* 悬臂结构 */
.boom-structure {
  position: absolute;
  bottom: 16px;
  left: 20px;
  width: 120px;   /* 悬臂 */
  height: 28px;
  transform-origin: 0 50%;
}

.boom-beam {
  position: absolute;
  top: 12px;
  left: 0;
  width: 90px;   /* 主梁长度*/
  height: 8px;   /* 主梁 */
  background: linear-gradient(to right, #7f8c8d, #95a5a6, #7f8c8d);
  border: 2px solid #5d6d7e;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.boom-beam::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 2px;
  right: 2px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
  border-radius: 2px;
}

.boom-truss {
  position: absolute;
  top: 2px;
  left: 8px;
  width: 74px;  /* 再次加长桁架 */
  height: 20px;
}

.truss-line {
  position: absolute;
  width: 12px;  /* 加长桁架杆 */
  height: 2px;  /* 加厚桁架杆 */
  background: linear-gradient(to right, #7f8c8d, #95a5a6);
  border-radius: 1px;

  &:nth-child(1) {
    top: 2px;
    left: 2px;
    transform: rotate(25deg);
  }

  &:nth-child(2) {
    top: 10px;
    left: 8px;
    transform: rotate(-25deg);
  }

  &:nth-child(3) {
    top: 2px;
    left: 15px;
    transform: rotate(25deg);
  }

  &:nth-child(4) {
    top: 10px;
    left: 21px;
    transform: rotate(-25deg);
  }

  &:nth-child(5) {
    top: 2px;
    left: 28px;
    transform: rotate(25deg);
  }

  &:nth-child(6) {
    top: 10px;
    left: 34px;
    transform: rotate(-25deg);
  }

  &:nth-child(7) {
    top: 2px;
    left: 41px;
    transform: rotate(25deg);
  }

  &:nth-child(8) {
    top: 10px;
    left: 47px;
    transform: rotate(-25deg);
  }

  &:nth-child(9) {
    top: 2px;
    left: 54px;
    transform: rotate(25deg);
  }

  &:nth-child(10) {
    top: 10px;
    left: 60px;
    transform: rotate(-25deg);
  }
}

/* 斗轮装置 */
.bucket-wheel-assembly {
  position: absolute;
  top: -2px;
  right: -8px;   /* 调整到更长悬臂的末端 */
  width: 28px;   /* 加大斗轮装置 */
  height: 28px;
}

.bucket-wheel {
  position: relative;
  width: 24px;   /* 加大斗轮 */
  height: 24px;
  border-radius: 50%;
  background: radial-gradient(circle, #e67e22, #d35400, #a04000);
  border: 3px solid #7d3000;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);

  &.spinning {
    animation: bucketWheelSpin 2s linear infinite;
  }
}

.bucket-wheel::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3), transparent 50%);
}

@keyframes bucketWheelSpin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  background: radial-gradient(circle, #2c3e50, #1a252f);
  border-radius: 50%;
}

.wheel-buckets {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.bucket {
  position: absolute;
  top: 1px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 3px;
  background: #8b4513;
  border-radius: 1px;
  transform-origin: 50% 6px;
}

.wheel-support {
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 12px;  /* 加大支撑 */
  height: 6px;
  background: linear-gradient(to bottom, #7f8c8d, #5d6d7e);
  border-radius: 3px;
  border: 1px solid #4a5568;
}

.wheel-drive {
  position: absolute;
  bottom: -8px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: radial-gradient(circle, #34495e, #2c3e50);
  border: 1px solid #1a252f;
  border-radius: 2px;
}

/* 配重臂 */
.counterweight-arm {
  position: absolute;
  bottom: 16px;
  right: 20px;
  width: 25px;   /* 加大配重臂 */
  height: 12px;
}

.counterweight {
  width: 100%;
  height: 10px;  /* 加厚配重 */
  background: linear-gradient(to bottom, #34495e, #2c3e50, #1a252f);
  border: 2px solid #0f1419;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.counterweight::before {
  content: '';
  position: absolute;
  top: 1px;
  left: 2px;
  right: 2px;
  height: 2px;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.2), transparent, rgba(255, 255, 255, 0.2));
  border-radius: 2px;
}

/* 状态指示器 */
.status-indicators {
  position: absolute;
  top: -12px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

  &.status-idle {
    background: #95a5a6;
  }

  &.status-working {
    background: #2ecc71;
    animation: statusPulse 1.5s infinite;
  }

  &.status-moving {
    background: #f39c12;
    animation: statusPulse 1s infinite;
  }

  &.status-maintenance {
    background: #e74c3c;
  }
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.2);
  }
}

.operation-mode {
  background: rgba(46, 204, 113, 0.9);
  color: white;
  padding: 1px 4px;
  border-radius: 6px;
  font-size: 8px;
  font-weight: bold;
  white-space: nowrap;
}

/* 位置标签 */
.position-label {
  position: absolute;
  bottom: -18px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(52, 152, 219, 0.9);
  color: white;
  padding: 1px 6px;
  border-radius: 8px;
  font-size: 9px;
  font-weight: bold;
  white-space: nowrap;
}

/* 工作目标指示 */
.target-indicator {
  position: absolute;
  top: -28px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(46, 204, 113, 0.9);
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: bold;
  white-space: nowrap;

  &.pulsing {
    animation: targetPulse 2s infinite;
  }
}

@keyframes targetPulse {
  0%, 100% {
    opacity: 0.9;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.05);
  }
}

/* 详情面板 */
.details-panel {
  position: fixed;
  z-index: 1000;
  background: rgba(44, 62, 80, 0.95);
  color: white;
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  h4 {
    margin: 0;
    font-size: 14px;
    color: #3498db;
  }

  .status-text {
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 4px;
    background: rgba(46, 204, 113, 0.8);
  }
}

.panel-content {
  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 4px;
    font-size: 12px;

    span:first-child {
      color: #bdc3c7;
      margin-right: 8px;
    }

    span:last-child {
      color: white;
      font-weight: 500;
    }
  }
}

/* 尺寸变体 */
.stacker-reclaimer.size-small {
  .device-body {
    width: 90px;   /* 小尺寸 */
    height: 60px;
  }

  .rotating-platform {
    width: 65px;
    height: 38px;
  }

  .machine-house {
    width: 30px;
    height: 24px;
  }

  .boom-structure {
    width: 65px;   /* 小尺寸也加长悬臂 */
    left: 15px;
  }

  .boom-beam {
    width: 65px;
    height: 6px;
  }

  .bucket-wheel-assembly {
    width: 20px;
    height: 20px;
  }

  .bucket-wheel {
    width: 18px;
    height: 18px;
  }
}

.stacker-reclaimer.size-large {
  .device-body {
    width: 150px;  /* 大尺寸 */
    height: 100px;
  }

  .rotating-platform {
    width: 110px;
    height: 62px;
  }

  .machine-house {
    width: 50px;
    height: 40px;
  }

  .house-body {
    height: 32px;
  }

  .boom-structure {
    width: 120px;  /* 大尺寸进一步加长悬臂 */
    left: 25px;
    height: 35px;
  }

  .boom-beam {
    width: 120px;
    height: 10px;
    top: 15px;
  }

  .bucket-wheel-assembly {
    width: 35px;
    height: 35px;
    right: -8px;
  }

  .bucket-wheel {
    width: 30px;
    height: 30px;
  }

  .counterweight-arm {
    width: 32px;
    height: 15px;
    right: 25px;
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .details-panel {
    position: absolute;
    left: 50% !important;
    top: 100% !important;
    transform: translateX(-50%);
    margin-top: 8px;
  }
}
</style>
