<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="daterange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="班次" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
        :height="tableHeight*0.6"
        :row-style="tableRowStyle"
        :span-method="mergeRowsMethod"
        @radio-change="handleRadioChange"
      >
      </vxe-grid>
    </div>

    <el-tabs type="border-card" style="margin-top: 10px">
      <el-tab-pane label="检化验成分详情">
        <el-row>
          <el-col :span="12">
            <vxe-grid
              ref="tableDetailRef"
              :column-config="{resizable: true}"
              v-bind="detialTable.gridOptions"
              :height="tableHeight*0.4"
            >
            </vxe-grid>
          </el-col>

          <el-col :span="12">
            <div id="mainChars" :style="{ width: '100%', height: `${tableHeight*0.4}px` }"></div>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>


  </div>
</template>

<script>
import * as echarts from 'echarts'
import { queryForDetail, queryForSumTable } from '@/api/cpes/productionIntegration'
import dayjs from 'dayjs'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'

export default {
  name: 'productionIntegration',
  components: { BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      dateRange: [],
      tableHeight: 300,
      mainTable: {
        selectedRadioRow: null,
        isQuery: false,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: []
        }
      },
      detialTable: {
        selectedRadioRow: null,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: []
        }
      },
      showSearch: true,

      // 查询参数
      queryParams: {
        operType: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        workModeId: null
      },
      prodCenterCode:null,
    }
  },
  created() {
    this.prodCenterCode = this.$route.query.prodCenterCode;
    this.dateRange.push(dayjs(new Date()).subtract(1, 'day').startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))

    this.queryList()
  },
  methods: {
    queryList() {
      if (!this.prodCenterCode) {
        this.$message({
          message: '地址栏加工中心为空',
          type: 'warning'
        })
        return
      }
      if (this.isQuery) {
        this.$message({
          message: '正在查询中...',
          type: 'warning'
        })
        return
      }
      this.queryParams.prodCenterCode = this.prodCenterCode

      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD 00:00:00'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD 00:00:00'
        )
      }
      this.queryParams.workModeId = 9
      this.mainTable.tableData = []
      this.mainTable.gridOptions.columns = []
      this.detialTable.gridOptions.data = []
      this.detialTable.gridOptions.columns = []
      this.isQuery = true
      queryForSumTable(this.queryParams).then(response => {

        this.mainTable.gridOptions.columns.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })
        for (let i = 0; i < response.data.col.length; i++) {
          var item = response.data.col[i]
          if (item.key.indexOf('-') == -1) {
            this.mainTable.gridOptions.columns.push({
              field: item.key,
              title: item.value,
              width: 'auto',
              headerAlign: 'center'
            })
          } else {
            var suffix = item.key.split('-')[0]
            const isHaveCol = this.mainTable.gridOptions.columns.filter(column =>
              column.title === suffix
            )
            if (isHaveCol == null || isHaveCol.length == 0) {
              var colDetailList = response.data.col.filter(col => {
                return col.key.startsWith(suffix + '-')
              })
              var colItem = { title: suffix, headerAlign: 'center', children: [] }
              colDetailList.forEach((element, index, array) => {
                colItem.children.push({
                  field: element.key,
                  title: element.value.replace(suffix, ''),
                  width: 'auto',
                  headerAlign: 'center'
                })
              })
              this.mainTable.gridOptions.columns.push(colItem)
            }
          }
        }
        this.mainTable.gridOptions.data = response.data.rec
        if (this.mainTable.gridOptions.columns.length > 0) {
          var par = {}
          par.WorkDate = this.mainTable.gridOptions.data[0].WorkDate
          this.handleRadioChange({ row: par })
        }
        this.isQuery = false
      }).catch(error => {
        this.$message({
          message: error,
          type: 'warning'
        })
        this.isQuery = false
      })

    },
    tableRowStyle({ row }) {
      if (row.WorkGroup == '合计') {
        return { backgroundColor: '#EBEEF5' }
      }
    },
    handleRadioChange({ row }) {
      var queryParams = {}
      queryParams.prodCenterCode = this.prodCenterCode
      queryParams.dtStart = dayjs(row.WorkDate).format('YYYY-MM-DD 00:00:00')
      queryParams.dtEnd = dayjs(row.WorkDate).format('YYYY-MM-DD 23:59:59')
      queryParams.workModeId = 9
      this.detialTable.gridOptions.data = []
      this.detialTable.gridOptions.columns = []
      queryForDetail(queryParams).then(response => {
        response.data.col.forEach(item => {
          this.detialTable.gridOptions.columns.push({
            field: item.key,
            title: item.value,
            width: 'auto',
            headerAlign: 'center'
          })
        })
        this.detialTable.gridOptions.data = response.data.rec
      })
      this.refreshChars({ row })

    },
    mergeRowsMethod({ row, rowIndex, column, data }) {
      const fields = ['WorkDate', 'radio', 'PlanCode'] // 需要合并的字段
      if (fields.includes(column.field)) {
        let rowspan = 1
        // 从当前行的下一行开始遍历，统计相同 WorkDate 的行数
        for (let i = rowIndex + 1; i < data.length; i++) {
          if (data[i].WorkDate === row.WorkDate) {
            rowspan++
          } else {
            break
          }
        }
        // 从当前行的上一行开始遍历，跳过已经合并的行
        for (let i = rowIndex - 1; i >= 0; i--) {
          if (data[i].WorkDate === row.WorkDate) {
            return { rowspan: 0, colspan: 0 }
          } else {
            break
          }
        }
        return { rowspan, colspan: 1 }
      }
      return { rowspan: 1, colspan: 1 }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '烧结生产综合', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    refreshChars({ row }) {

      var chartDom = document.getElementById('mainChars')
      var myChart = echarts.init(chartDom)
      myChart.clear()
      var option

      option = {
        title: {
          text: '合格率百分比图'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['R一级品率', 'R合格率', 'FeO合格率', '转鼓合格率']
        },
        series: [
          {
            type: 'bar',
            data: []
          }
        ]
      }
      if (row) {
        var specificDate = row.WorkDate
        const filteredData = this.mainTable.gridOptions.data.filter(item => {
          return item.WorkDate === specificDate && item.WorkGroup === '合计'
        })
        option.title.text = specificDate + '合格率百分比图'
        option.series[0].data = [
          filteredData[0]['RLeave1Rate'],
          filteredData[0]['R-PassRate'],
          filteredData[0]['FeO-PassRate'],
          filteredData[0]['转鼓-PassRate']
        ]
        option && myChart.setOption(option)
      }

    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
