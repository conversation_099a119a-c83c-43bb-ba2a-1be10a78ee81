import request from '@/utils/request'



//查询 
export function stackPlanList(query) {
    return request({
        url: '/api/wms/stackplan/list',
        method: 'get',
        params: query,
    })
}

//新增 修改
export function saveOrUpdate(data) {
    return request({
        url: '/api/wms/stackplan/saveOrUpdate',
        method: 'post',
        data: data,
    })
}

// 删除
export function deletePlan(data) {
    return request({
        url: "/api/wms/stackplan/remove",
        method: "delete",
        data: data,
    });
}
