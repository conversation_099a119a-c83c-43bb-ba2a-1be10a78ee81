<template>
  <div class="app-container" @mousedown="handleMouseInput">
    <el-row>
      <fromSearchBox ref="searchbox" @searchBoxAction="searchBoxAction"></fromSearchBox>
    </el-row>
    <el-row :gutter="10" class="mb8" v-show="flgedit">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" :v-if=false>新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">保存</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" v-if="false"
          @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <template>
      <div>
        <vxe-grid ref="vxegrid" v-bind="gridOptions" @page-change="pageChangeEvent" @edit-closed="editClosedEvent"
          @menu-click=menuClickEvent></vxe-grid>
      </div>
    </template>

    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogForm" :title="title" :visible.sync="formopen" v-if="formopen" width="800px"
      @close='closeDialog' append-to-body :close-on-click-modal="false" :close-on-press-escape="false" >
      <fromBuilder ref="fromBuilder" :propPageKey="pageKey" :propDefFormData="defFormData"
        :propFilterParam="filterParam" :propRowData="subRowData" @call-parent-method="closeDialog">
      </fromBuilder>
    </el-dialog>

    <el-dialog ref="dialogForm" :title="title" :visible.sync="dedigneropen" v-if="dedigneropen" width="1450px"
      @close='closeDialogDesigner' append-to-body :close-on-click-modal="false" :close-on-press-escape="false">
      <fromDesigner ref="fromDesigner" :propPageKey="pageKey">
      </fromDesigner>
    </el-dialog>

  </div>
</template>

<script>
import { VxeUI } from 'vxe-pc-ui';
import { getChart, getcols, gettabledata } from "@/api/formtemplate/formdylist";
import { getformjson, getformdata, saveformdata, querynoterole } from '@/api/formtemplate/details';
import fromSearchBox from './fromSearchBox';
import fromBuilder from './fromBuilder';
import fromDesigner from './fromDesigner';
import { watch } from "vue";

export default {
  name: "Unit",
  dicts: ["OPER_SOURCE", "SUMMARY_TYPE", "record_status", "form_template_details_status_config"],
  components: { fromSearchBox, fromBuilder, fromDesigner },
  data() {
    return {
      // 表格配置
      gridOptions: {"pagerConfig":{"currentPage":1,"pageSize":10}},
      // 页面编码
      pageKey: '',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 选择所有的数据
      selectionData: [],
      // 非单个禁用
      single: false,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      formopen: false,
      dedigneropen: false,
      flgedit: false,
      // 表单页类型
      formtype: '1',
      //加工中心菜单
      editProductCenterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      columns: [],
      selectVO: '',
      defFormData: {},
      filterParam: {},
      pkfieldkey: '',
      subRowData: {},
      // 监控序列
      secretSequence: [0, 1, 0, 1, 0, 1],
      inputSequence: [],
    };
  },
  watch: {

  },
  created() {
    this.defFormData = this.$route.query;
  },
  mounted() {
    this.pageKey = this.$route.query.pageKey;
    this.getcols();
  },
  methods: {
    /** 查询报表单元列表 */
    getList() {
      this.getcols();
      this.getTableData();
    },
    getcols() {
      this.loading = true;
      getcols(this.pageKey).then((resp) => {
        this.gridOptions = resp;
        this.formtype = resp.formtype;
        this.flgedit = resp.allowedit === '1';
        this.pkfieldkey = resp.pkfieldkey;
        this.loading = false;
        // console.log(this.gridOptions);
      })
    },
    getTableData() {
      this.searchBoxAction();
    },
    pageChangeEvent({ pageSize, currentPage }) {
      this.gridOptions.pagerConfig.currentPage = currentPage
      this.gridOptions.pagerConfig.pageSize = pageSize
      this.getTableData();
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      if (this.formtype === '1')
        this.formopen = false;
    },
    // this.reset();
    // 表单重置
    reset() {
      // this.$refs.fromBuilder.clearFormData();
    },
    // 初始化表单布局
    initForm() {

    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.selectionData = selection;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.filterParam = {};
      if (this.formtype === '1') {
        this.formopen = true;
      }
      this.open = true;
      this.title = "增加数据";
      this.initForm()
      this.reset();
    },
    /** 修改按钮操作 */
    handleUpdate() {
      const $table = this.$refs.vxegrid
      if ($table) {
        const updateRecords = $table.getUpdateRecords()
        console.log("修改行数:",updateRecords.length);
        if (updateRecords.length > 0) {
          for(var i=0;i<updateRecords.length;i++){
            console.log("修改:",i,updateRecords[i]);
            this.modifyFunc(updateRecords[i]);
          }
          VxeUI.modal.message({
            content: '保存成功',
            status: 'success'
          })
        } else {
          VxeUI.modal.message({
            content: '数据未改动',
            status: 'warning'
          })
        }
      }
    },
    // 右键事件
    async menuClickEvent({ menu, row }) {
      // 编辑
      if (menu.code == 'row_edit') {
        this.handleUpdateMenu(row);
      } else if (menu.code == 'row_copy') {
        // confirm
        let isConfirmed = false;
        await this.$confirm('是否要复制行数据', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          isConfirmed = true;
        }).catch(() => {
        });
        if (!isConfirmed) {
          return;
        }

        // 复制
        // 清空主键ID
        row[this.pkfieldkey] = '';
        //保存数据
        saveformdata(this.pageKey, {}, row).then(response => {
          if (response == undefined) {
            this.loading = false;
            this.$message.error("修改失败");
          }
          if (response.code == 200) {
            this.$message.success("修改成功");
            this.saveflag = true;
          } else {
            this.$message.error("修改失败");
          }
          this.searchBoxAction();
          // console.log(response.code);
          this.loading = false;
        }).catch(error => {
          this.loading = false;
        });
      }
    },
    // 右键修改按钮
    handleUpdateMenu(row) {
      this.subRowData = row;
      this.filterParam = { "con1": row[this.pkfieldkey] };
      if (this.formtype === '1') {
        this.formopen = true;
      }
      try {
        this.reset();
      } catch (error) {

      }
      this.open = true;
      this.title = "修改数据";
      this.initForm()
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          if (obj.operSource != null) {
            obj.operSource = obj.operSource.join();
          }

          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reportUnitIds = row.reportUnitId || this.ids;
      this.$modal
        .confirm("确认删除选中的报表单元？")
        .then(function () {
          return delUnit(reportUnitIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        this.download(
          "/formtemp/common/dltable/export",
          {
            "pageKey": this.pageKey,
            "filter": JSON.stringify(data),
          },
          `${this.pageKey}_${new Date().getTime()}.xlsx`
        ).then(ret => {
          this.loading = false;
        }).error(ret => {
          this.loading = true;
        });
      })
    },
    searchBoxAction() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        let tbconfig = {
          pagenum: this.gridOptions.pagerConfig.currentPage,
          pagesize: this.gridOptions.pagerConfig.pageSize
        };
        gettabledata(this.pageKey, JSON.stringify(data), JSON.stringify(tbconfig)).then((resp) => {
          this.gridOptions.data = resp.tbdata;
          this.gridOptions.pagerConfig.total = resp.total;
          this.loading = false;
          this.$refs.vxegrid.loadData(this.gridOptions.data);
          // console.log(this.gridOptions);
        })
      })
    },
    //关闭弹框的事件
    closeDialog() {
      // saveflag
      if (this.$refs.fromBuilder.saveflag) {
        this.searchBoxAction();
      }
      this.$refs.fromBuilder.saveflag = false;
      this.formopen = false;
      this.open = false;
      this.reset();
    },
    // 关闭设计器
    closeDialogDesigner(){

    },

    // 修改逻辑
    async modifyFunc(row){
      const $table = this.$refs.vxegrid
      // 校验
      const errMap = await $table.validate(true)
      if(errMap){
          VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
          return;
      }
      // 提交
      if ($table) {
        const field = ''
        const cellValue = ''
        const title = ''
        this.filterParam = { "con1": row[this.pkfieldkey] };
        // 判断单元格值是否被修改
        if ($table.isUpdateByRow(row)) {
          setTimeout(() => {
            //保存数据
            saveformdata(this.pageKey, this.filterParam, row).then(response => {
              if (response == undefined) {
                VxeUI.modal.message({
                  content: `保存失败！ ${title}${cellValue}`,
                  status: 'error'
                })
              }
              if (response.code == 200) {
                VxeUI.modal.message({
                  content: `保存成功！ ${title}${cellValue}`,
                  status: 'success'
                })
              } else {
                VxeUI.modal.message({
                  content: `保存失败！ ${title}${cellValue}`,
                  status: 'error'
                })
              }
              this.loading = false;
            }).catch(error => {
              this.loading = false;
              VxeUI.modal.message({
                content: `保存失败！ ${title}${cellValue}`,
                status: 'error'
              })
            });
            // 局部更新单元格为已保存状态
            $table.reloadRow(row, null, field)
          }, 300)
        }
      }
    },

    // 表格修改
    editClosedEvent: async function ({ row }) {
      const $table = this.$refs.vxegrid
      // 校验
      const errMap = await $table.validate(true)
      if(errMap){
          VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
          return;
      }
      // confirm
      // let isConfirmed = false;
      // await this.$confirm('是否要执行修改', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   type: 'warning'
      // }).then(() => {
      //   isConfirmed = true;
      // }).catch(() => {
      // });
      // if (!isConfirmed) {
      //   return;
      // }
      // 提交
      // this.modifyFunc(row);
    },

    handleMouseInput(e) {
      // 记录鼠标按键（左键0，右键2，中键1）
      this.inputSequence.push(e.button);

      // 保持序列长度与密码一致
      if(this.inputSequence.length > this.secretSequence.length) {
        this.inputSequence.shift();
      }

      console.log(e.button,'序列值',this.inputSequence);

      // 检测序列匹配
      if(this.checkSequence()) {
        this.dedigneropen = true;
        this.inputSequence = []; // 重置输入
      }
    },
    checkSequence() {
      return this.inputSequence.join(',') === this.secretSequence.join(',');
    },
    // 数据校验
    async fullValidEvent () {
      const $grid = this.$refs.gridRef
      if ($grid) {
        const errMap = await $grid.validate(true)
        if (errMap) {
          VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
          return false;
        } else {
          VxeUI.modal.message({ status: 'success', content: '校验成功！' })
          return true;
        }
      }
    }
  },
};
</script>
