<template>
    <div>
        <div>
            <vxe-grid ref="grid1Ref" v-bind="grid1Options">
                <template #currUser_edit="{ row }">
                    <vxe-table-select v-model="row.currUser" :options="currUserOptions" :columns="currUserColumns">
                    </vxe-table-select>
                </template>

                <template #currUser_default="{ row }">
                    <span>{{ formatCurrUserLabel(row.currUser) }}</span>
                </template>
            </vxe-grid>
        </div>
    </div>
</template>

<script>
export default {
    name: 'StockOut2',
    data() {
        const currUserColumns = [
            { field: 'label', title: 'Name' },
            { field: 'role', title: 'Role' },
            { field: 'sex', title: 'Sex' },
            { field: 'address', title: 'Address' }
        ]
        const currUserOptions = [
            { value: 10001, label: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
            { value: 10002, label: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
            { value: 10003, label: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
            { value: 10004, label: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' }
        ]


        // grid1数据
        const grid1Options = {
            border: true,
            stripe: true,
            align: 'center',
            height: 800,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            mouseConfig: {
                selected: true
            },
            editConfig: {
                trigger: 'dblclick',
                mode: 'cell',
            },
            columns: [],
            data: [],
        }

        return {
            grid1Options,
            currUserOptions,
            currUserColumns,
        }
    },

    methods: {
        initGridData() {
            this.initGrid1Data()
        },

        // 初始化grid1数据
        initGrid1Data() {
            this.grid1Options.data = [
                {
                    id: 10001, pdc: 'G8A', start: 'A-2', end: '圆盘', mateCode: '', mateName: '', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10002, pdc: 'G8B', start: 'A-2', end: '圆盘', mateCode: '', mateName: '', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10003, pdc: 'G4', start: 'A-2', end: '供烧结', mateCode: '', mateName: '', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10004, pdc: 'S2', start: 'B-2', end: '供1#高炉烧结矿', mateCode: '', mateName: '烧结矿', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10005, pdc: 'K1B', start: 'A-2', end: '供1#高炉块矿', mateCode: '', mateName: '块矿', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10006, pdc: 'J7', start: 'A-2', end: '供1#2#高炉焦炭', mateCode: '', mateName: '焦炭', v1: '', v2: '', stoker: '取料机'
                },
                {
                    id: 10007, pdc: 'J1B', start: 'A-2', end: '天焦供高炉焦炭', mateCode: '', mateName: '焦炭', v1: '', v2: '', stoker: '取料机'
                },
            ]

            this.grid1Options.columns = [
                { type: 'seq', width: 50 },
                { field: 'pdc', title: '皮带秤', },
                { field: 'start', title: '起点', editRender: { name: 'VxeInput' } },
                { field: 'end', title: '终点', editRender: { name: 'VxeInput' } },
                // { field: 'mateName', title: '物料', },
                { field: 'mateName', title: '物料', editRender: {}, slots: { edit: 'currUser_edit', default: 'currUser_default' } },
                { field: 'v1', title: '瞬时值', },
                { field: 'v2', title: '累计值', },
                { field: 'stoker', title: '取料机', },
                // { title: '操作', width: 100, fixed: 'right', slots: { default: 'action' } }
            ]
        },

        formatCurrUserLabel(val) {
            const item = this.currUserOptions.find(item => item.value === val)
            return item ? item.label : val
        },
    },

    mounted() {
        this.initGridData();
    },

}
</script>