import request from "@/utils/request";

// 查询物料-单位列表
export function listUnit(query) {
  return request({
    url: "/api/md/materialunit/list",
    method: "get",
    params: query,
  });
}

// 查询物料-单位详细
export function getUnit(unitId) {
  return request({
    url: "/api/md/materialunit/" + unitId,
    method: "get",
  });
}

// 新增物料-单位
export function addUnit(data) {
  return request({
    url: "/api/md/materialunit",
    method: "post",
    data: data,
  });
}

// 修改物料-单位
export function updateUnit(data) {
  return request({
    url: "/api/md/materialunit",
    method: "put",
    data: data,
  });
}

// 删除物料-单位
export function delUnit(unitId) {
  return request({
    url: "/api/md/materialunit/" + unitId,
    method: "delete",
  });
}

export function queryByUnitGropCode(unitGropCode) {
  const data = { unitGropCode: unitGropCode };
  return request({
    url: "/api/md/materialunit/queryByUnitGropCode/",
    method: "get",
    params: data,
  });
}
