<template>
  <div class="material-detail">
    <el-form :model="form" :rules="rules" ref="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="物料名称" prop="materialName">
            <el-input v-model="form.materialName" placeholder="请输入物料名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="重量(T)" prop="weight">
            <el-input-number 
              v-model="form.weight" 
              :min="0" 
              :max="9999" 
              :precision="1"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="密度" prop="density">
            <el-input-number 
              v-model="form.density" 
              :min="0" 
              :max="10" 
              :precision="2"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="位置(px)" prop="x">
            <el-input-number 
              v-model="form.x" 
              :min="0" 
              :max="2000" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="宽度(px)" prop="w">
            <el-input-number 
              v-model="form.w" 
              :min="20" 
              :max="500" 
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商" prop="supplier">
            <el-select v-model="form.supplier" placeholder="请选择供应商" style="width: 100%">
              <el-option label="澳洲矿业" value="澳洲矿业" />
              <el-option label="巴西矿业" value="巴西矿业" />
              <el-option label="焦化厂A" value="焦化厂A" />
              <el-option label="石灰石厂B" value="石灰石厂B" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="到货时间" prop="arrivalTime">
            <el-date-picker
              v-model="form.arrivalTime"
              type="datetime"
              placeholder="选择到货时间"
              style="width: 100%"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input 
          v-model="form.remark" 
          type="textarea" 
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>

      <!-- 统计信息 -->
      <div class="statistics">
        <h4>统计信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">体积</div>
              <div class="stat-value">{{ calculatedVolume }} m³</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">占用面积</div>
              <div class="stat-value">{{ calculatedArea }} m²</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-label">存储天数</div>
              <div class="stat-value">{{ storageDays }} 天</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave">保 存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MaterialDetail',
  props: {
    material: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      form: {
        i: '',
        materialName: '',
        materialCode: '',
        weight: 0,
        density: 1.5,
        x: 0,
        w: 100,
        supplier: '',
        arrivalTime: '',
        remark: ''
      },
      rules: {
        materialName: [
          { required: true, message: '请输入物料名称', trigger: 'blur' }
        ],
        materialCode: [
          { required: true, message: '请输入物料编码', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' }
        ],
        density: [
          { required: true, message: '请输入密度', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    calculatedVolume() {
      // 简单的体积计算：重量 / 密度
      if (this.form.weight && this.form.density) {
        return (this.form.weight / this.form.density).toFixed(2);
      }
      return '0.00';
    },
    
    calculatedArea() {
      // 简单的面积计算：宽度 * 假设长度(80px)
      if (this.form.w) {
        return ((this.form.w * 80) / 10000).toFixed(2); // 转换为平方米
      }
      return '0.00';
    },
    
    storageDays() {
      if (this.form.arrivalTime) {
        const arrivalDate = new Date(this.form.arrivalTime);
        const currentDate = new Date();
        const diffTime = Math.abs(currentDate - arrivalDate);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
      }
      return 0;
    }
  },
  watch: {
    material: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.form = {
            i: newVal.i,
            materialName: newVal.materialName || '',
            materialCode: newVal.materialCode || '',
            weight: newVal.weight || 0,
            density: newVal.density || 1.5,
            x: newVal.x || 0,
            w: newVal.w || 100,
            supplier: newVal.supplier || '',
            arrivalTime: newVal.arrivalTime || '',
            remark: newVal.remark || ''
          };
        }
      }
    }
  },
  methods: {
    handleSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 合并原始数据和表单数据
          const updatedMaterial = {
            ...this.material,
            ...this.form
          };
          this.$emit('save', updatedMaterial);
        } else {
          this.$message.error('请检查表单数据');
        }
      });
    },
    
    handleCancel() {
      this.$emit('cancel');
    }
  }
}
</script>

<style lang="scss" scoped>
.material-detail {
  .statistics {
    margin-top: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    
    h4 {
      margin: 0 0 16px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
    }
    
    .stat-item {
      text-align: center;
      
      .stat-label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }
      
      .stat-value {
        font-size: 16px;
        font-weight: 600;
        color: #409eff;
      }
    }
  }
  
  .dialog-footer {
    margin-top: 20px;
    text-align: right;
    
    .el-button {
      margin-left: 10px;
    }
  }
}

::v-deep .el-form-item__label {
  font-weight: 500;
  color: #333;
}

::v-deep .el-input__inner,
::v-deep .el-textarea__inner {
  border-radius: 4px;
}

::v-deep .el-input-number {
  width: 100%;
}
</style>
