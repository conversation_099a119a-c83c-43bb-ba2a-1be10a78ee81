import request from '@/utils/request'
// 查询操作日志记录列表
export function listIronSelect(query) {
  return request({
    url: '/api/blastFurnace/iron/listIronSelect',
    method: 'get',
    params: query
  })
}
// 混罐
export function mixedCanMethod(data) {
  return request({
    url: '/api/blastFurnace/iron/mixedCanMethod',
    method: 'post',
    data: data
  })
}

//数据录入
export function ironCanWeightInputEdit(data) {
  return request({
    url: '/api/blastFurnace/iron/ironCanWeightInputEdit',
    method: 'post',
    data: data
  })
}
