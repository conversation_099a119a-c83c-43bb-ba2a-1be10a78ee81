<template>
  <div class="app-container" style="padding: 10px;"  ref="box">
    <el-form :model="queryParams" ref="queryForm" :inline="true" size="small" label-width="68px">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="时间间隔">
        <el-input-number v-model="queryParams.timeInterge" :min="1"></el-input-number>
      </el-form-item>
      <el-form-item label="时间间隔">
        <el-select v-model="queryParams.timeType" placeholder="请选择">
          <el-option v-for="item in timeTypes" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="瞬时数据">
        <el-select v-model="queryParams.instantValue" placeholder="请选择">
          <el-option
            v-for="item in instantOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="瞬时间隔">
        <el-select v-model="queryParams.timeValue" placeholder="请选择">
          <el-option
            v-for="item in timeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="queryList" icon="el-icon-search">查询</el-button>
      </el-form-item>
    </el-form>

    <el-radio-group v-model="isCollapse" style="margin-bottom: 1px;" >
      <el-radio-button :label="false" >展开</el-radio-button>
      <el-radio-button :label="true" >收起</el-radio-button>
    </el-radio-group>
    <el-row>
      <div class="left">
        <el-col :span="8" :class="{'content-expandedss': isCollapse}">
          <el-menu  class="el-menu-vertical-demo" :collapse="isCollapse">
            <el-tabs type="border-card">
              <el-tab-pane label="点位值">
                <div class="mes_new_table" >
                  <vxe-table
                    border
                    show-overflow
                    :cell-config="{height: 35}"
                    ref="tableRef"
                    :height="dltableHeight"
                    @checkbox-change="handleCheckboxChange"
                    :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
                    :data="pointTableConfig.tableData "
                  >
                    <vxe-column type="checkbox"  align="center" width="60"></vxe-column>
                    <vxe-column field="pointName" title="点位名称" ></vxe-column>
                    <vxe-column field="pointCode" title="点位编号"></vxe-column>
                  </vxe-table>
                </div>
              </el-tab-pane>
            </el-tabs>
          </el-menu>
        </el-col>
      </div>
      <div class="mid" :class="{'content-expanded': isCollapse}">
        <el-col :span="16" style="width: 107%;">
          <el-tabs type="border-card" style="margin-left: 10px;" >
            <el-tab-pane label="趋势图" >
              <div ref="chart4" class="chart4" style="width: 100%; height: 100%; margin-left: auto; margin-right: auto;">
              </div>
            </el-tab-pane>
            <el-tab-pane label="点位详细值">
              <div class="mes_new_table">
                <dlTable size="small" :height="dltableHeight" refName="dlTable" :stripe="true" :border="true"
                         :columns="columns" :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig"
                >
                </dlTable>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </div>
    </el-row>

  </div>
</template>

<script>
  import dayjs from "dayjs";
  import { list } from "@/api/vuexinfluxdbdemo/influxdbdemo";
  import { influxdbTableComm } from '@/api/vuexinfluxdbdemo/influxdbEchartTemplate.js';

  export default {
    name: 'ZhiJinQiao',
    data() {
      return {
        isChecked: false,
        isCollapse:false, // tab页面展示与隐藏   false：tab页打开
        timer: null, //定时任务时间
        chart4: null,
        dltableHeight: 660,
        timeTypes: [{
          value: '秒',
          label: '秒'
        }, {
          value: '分',
          label: '分'
        }, {
          value: '时',
          label: '时'
        }],
        queryParams: {
          timeInterge: 1,
          timeType: '分',
          points: '',
          instantValue:'',
          timeValue:'',
        },
        timeOptions:[{
          value: '1小时',
          label: '1小时'
        },{
          value: '2小时',
          label: '2小时'
        }],
        instantOptions:[{
          value: '开启',
          label: '开启'
        },{
          value: '关闭',
          label: '关闭'
        }],
        // 日期范围
        dateRange: [],
        basicConfig: {
          index: false, // 是否启用序号列
          needPage: false, // 是否展示分页
          indexName: null, // 序号列名(默认为：序号)
          selectionType: false, // 是否启用多选框
          indexWidth: 150, // 序号列宽(默认为：50)
          indexFixed: null, // 序号列定位(默认为：left)
          settingType: true, // 是否展示表格配置按钮
          headerSortSaveType: false // 表头排序是否保存在localStorage中
        },
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 100, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [100, 200, 500, 1000]// 分页器分页待选项
        },
        columns: [
          {
            label: '时间',
            fieldIndex: '时间',
            width: 150,
            sortable: true,
            filterable: true,
            searchField: null,
            concise: true,
          },
        ],
        tableData: [],
        option4: {

          tooltip: {
            trigger: 'axis',
            confine: true,
            axisPointer: {
              type: 'cross',
              label: {
                backgroundColor: '#6a7985'
              }
            }
          },
          legend: {
            data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
            orient: 'horizontal',
            left: 'left',
            top: 'top',
            type: 'scroll',
          },
          dataZoom: [{ // 这部分是dataZoom组件
            type: 'inside', // 表示内置型数据区域缩放组件
            start: 0, // 数据窗口范围的起始百分比
            end: 80, // 数据窗口范围的结束百分比

          }],
          grid: {
            left: '1%',
            right: '1%',
            bottom: '%',
            containLabel: true
          },
          xAxis: [
            {
              type: 'category',
              boundaryGap: false,
              data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
            }
          ],
          yAxis: [
            {
              type: 'value',
            }
          ],
          series: [
            {
              name: 'Email',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [120, 132, 101, 134, 90, 230, 210]
            },
            {
              name: 'Union Ads',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [220, 182, 191, 234, 290, 330, 310]
            },
            {
              name: 'Video Ads',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [150, 232, 201, 154, 190, 330, 410]
            },
            {
              name: 'Direct',
              type: 'line',
              stack: 'Total',
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [320, 332, 301, 334, 390, 330, 320]
            },
            {
              name: 'Search Engine',
              type: 'line',
              stack: 'Total',
              label: {
                show: true,
                position: 'top'
              },
              areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: [820, 932, 901, 934, 1290, 1330, 1320]
            }
          ]
        },
        pointTableConfig: {
          ids: [],
          tableData: [],
          basicConfig: {
            index: false, // 是否启用序号列
            needPage: false, // 是否展示分页
            indexName: null, // 序号列名(默认为：序号)
            selectionType: true, // 是否启用多选框
            indexWidth: null, // 序号列宽(默认为：50)
            indexFixed: null, // 序号列定位(默认为：left)
            settingType: true, // 是否展示表格配置按钮
            headerSortSaveType: false // 表头排序是否保存在localStorage中
          },
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 20, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          },
          pointColumns: [
            {
              label: '点位名称', // 表头描述
              fieldIndex: 'pointName', // 表格显示内容绑定值
              width: 250,
              sortable: false, // 此属性可以设置排序
              filterable: false, // 是否筛选 默认为false
              searchField: 'POINT_NAME',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
              concise: false // 是否显示筛选icon 和排序 icon
            },
            {
              label: '点位编号', // 表头描述
              fieldIndex: 'pointCode', // 表格显示内容绑定值
              width: 200,
              sortable: false, // 此属性可以设置排序
              filterable: false, // 是否筛选 默认为false
              searchField: 'POINT_CODE',// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
              concise: false // 是否显示筛选icon 和排序 icon
            },
          ]
        },
        queryPointParams: {
          pointProcess: null
        },
        optionItemTimer:'',
        echartsData:{}, // 曲线图数据
      }
    },
    created() {
      this.dateRange.push(dayjs(new Date()).startOf("day"))
      this.dateRange.push(dayjs(new Date()));
      this.pointQuery();
    },
    methods: {
      getPointProcess() {
        return this.$route.query.pointProcess;
      },
      /** 图形名称 */
      getFileName() {
        return this.$route.query.FileName;
      },
      /*点位列表*/
      pointQuery() {
        let config = null;
        let tableConfig =  influxdbTableComm().filter(x=>x.businessCode == this.getPointProcess());
        if(tableConfig == null || tableConfig.length == 0){
          this.$message.error('配置项暂无数据')
          return
        }
        if(tableConfig[0].tableData.length>0){
          this.pointTableConfig.tableData = tableConfig[0].tableData;
        }
      },
      handleCheckboxChange({ checked, record }) {
        const table = this.$refs.tableRef;
        const selectedRows = table.getCheckboxRecords();
        this.pointTableConfig.ids =  selectedRows.map(item => item.pointCode)
      },

      queryList() {
        if (this.pointTableConfig.ids.length == 0) {
          this.$message.error('请选择点位')
          return
        }
        // 是否开启定时任务
        if(this.queryParams.instantValue == '开启'){
          if(this.queryParams.timeValue.length == 0){
            this.$message.error('请选择瞬时间隔')
            return
          }
          console.log("开启定时任务")

          this.startTimer();
        }else{
          console.log("关闭定时任务")
          this.stopTimer();
        }
        const obj = JSON.parse(JSON.stringify(this.queryParams))
        if (this.dateRange.length == 2) {
          obj.dtstart = dayjs(this.dateRange[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          obj.dtend = dayjs(this.dateRange[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }
        obj.points = this.pointTableConfig.ids
        obj.timeStr = this.queryParams.timeValue
        obj.instantValue = this.queryParams.instantValue
        list(obj).then(res => {
          this.columns = [];
          res.data.table.columns.forEach(element => {
            this.columns.push({
              label: element,
              fieldIndex: element,
              width: 150,
              sortable: true,
              filterable: true,
              searchField: null,
              concise: true,
            });
          });

          this.tableData = res.data.table.data;
          this.chart4 = this.$echarts.init(this.$refs.chart4);
          this.option4.legend.data.splice(0, this.option4.legend.data.length);
          this.option4.xAxis[0].data.splice(0, this.option4.xAxis[0].data.length);
          this.option4.yAxis.splice(0, this.option4.yAxis.length);
          this.option4.series.splice(0, this.option4.series.length);

          res.data.echars.legend.forEach(element => {
            this.option4.legend.data.push(element);
            var item = {
              type: 'value',
              name: 'SJC_SJ360_T1301',
              axisLine: {
                show: true,
              },
            };
            item.name = element;
            this.option4.yAxis.push(item);
          });

          res.data.echars.xAxis.forEach(element => {
            if (this.option4.xAxis[0].data.indexOf(element) == -1) {
              this.option4.xAxis[0].data.push(element)
            }
          });

          res.data.echars.series.forEach(element => {

            var optionItem = {
              name: 'Email',
              type: 'line',
              stack: 'Total',
              // areaStyle: {},
              emphasis: {
                focus: 'series'
              },
              data: []
            };
            optionItem.name = element.name;
            optionItem.data.shift(); // 把 数据删除掉
            optionItem.data = element.data; // 查询的新数据存入
            this.option4.series.push(optionItem);

          });
            console.log(JSON.stringify(this.option4));
          // this.chart4.clear();
          this.chart4.setOption(this.option4);

        })
      },

      startTimer() {
        this.timer = setInterval(() => {
          console.log('This runs every second.');
          this.queryList();
        }, 60000);
      },
      stopTimer() {
        if (this.timer !== null) {
          clearInterval(this.timer);
          this.timer = null;
        }
      },

    },
    mounted() {
      this.$nextTick(() => {
        let topValue2 = document.getElementsByClassName('chart4')[0].getBoundingClientRect().top;
        var height = (document.body.clientHeight - topValue2 - 15)
        this.dltableHeight = height;
        console.log("this.dltableHeight:",this.dltableHeight)
        document.getElementsByClassName('chart4')[0].style.height = height + 'px';
      })
    },
    beforeDestroy() {
      this.stopTimer();
    }
  }
</script>
<style  scoped>
  /deep/ .el-form-item--mini.el-form-item,
  .el-form-item--small.el-form-item {
    margin-bottom: 10px;
  }

  /deep/ .el-tabs--border-card>.el-tabs__content {
    padding: 5px;
  }


  /* 拖拽相关样式 */
  /*包围div样式*/
  .app-container {
    width: 100%;
    height: 100%;
    margin: 1% 0px;
    overflow: hidden;
    box-shadow: -1px 9px 10px 3px rgba(0, 0, 0, 0.11);
  }
  /*左侧div样式*/
  .left {
    width: calc(80% - 10px);  /*左侧初始化宽度*/
    height: 100%;
    background: #FFFFFF;
    /*float: left;*/
  }

  /*右侧div'样式*/
  .mid {
    float: left;
    width: 68%;   /*右侧初始化宽度*/
    height: 100%;
    background: #fff;
    box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);

    width: calc(100% - 500px); /* 默认宽度 */
    transition: width 0.3s; /* 平滑过渡效果 */
  }
/* 右边tab页面 展开后的自适应*/
  .content-expanded {
    width: calc(100% - 150px); /* 展开后的宽度 */
    float: inside;
    height: 100%;
    background: #fff;
    box-shadow: -1px 4px 5px 3px rgba(0, 0, 0, 0.11);
  }
  /* 左边tab页面 折叠后的自适应*/
  .content-expandedss{
    width:5%;
  }
</style>
