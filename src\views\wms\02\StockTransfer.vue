<template>
  <div style="margin: 10px;">
    <vxe-grid ref="tableMainRef" v-bind="gridOptions" style="margin: 10px" @page-change="pageChangeEvent"
      @checkbox-change="selectChangeEvent" @checkbox-all="selectChangeEvent">
      <template #form>
        <vxe-form ref="formRef" v-bind="formOptions">
          <template #action>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="searchEvent">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetEvent">重置</el-button>
          </template>
        </vxe-form>
      </template>
      <template #toolbarButtons>
        <el-button type="primary" plain size="mini" @click="showForm('add')">调拨计划</el-button>
        <el-button type="primary" plain size="mini" @click="showDirectTransferForm">直接调拨</el-button>
        <el-button type="primary" plain icon="el-icon-edit" size="mini" @click="showForm('edit')">编辑</el-button>
        <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleDelete">删除</el-button>
        <!-- <vxe-button status="primary" @click="handleExport">导出</vxe-button> -->
      </template>
    </vxe-grid>
    <!-- 数据操作弹窗 -->
    <vxe-modal v-model="formVisible" :title="formTitle" width="800" destroy-on-close @close="handleModalClose">
      <vxe-form ref="formDialogRef" v-bind="formDialogOptions">
        <template #action>
          <div style="text-align: center; margin-top: 20px">
            <vxe-button status="primary" @click="submitForm">提交</vxe-button>
            <vxe-button @click="cancel">取消</vxe-button>
          </div>
        </template>
      </vxe-form>
    </vxe-modal>
    <vxe-modal v-model="directTransferVisible" title="直接调拨" width="800" destroy-on-close @close="cancelDirectTransfer">
      <vxe-form ref="directTransferFormRef" v-bind="directTransferFormOptions">
        <template #action>
          <div style="text-align: center; margin-top: 20px">
            <vxe-button status="primary" @click="submitDirectTransferForm">提交</vxe-button>
            <vxe-button @click="cancelDirectTransfer">取消</vxe-button>
          </div>
        </template>
      </vxe-form>
    </vxe-modal>
  </div>
</template>

<script>
import {
  selectTransferData,
  insertTransferData,
  updateTransferData,
  deleteTransferData,
  listStorehouse,
  listStorehouseFilter,
  initMaterialList
} from "@/api/wms/stocktransfer";

export default {
  name: "TransferData",
  data() {
    const stripRender = {
        name: 'VxeSelect',
        props: {
            clearable: true,
        },
        options: [
            { value: '1', label: '1' },
            { value: '2', label: '2' },
            { value: '3', label: '3' },
            { value: '4', label: '4' },
            { value: '5', label: '5' },
            { value: '6', label: '6' },
        ]
    }

    const stripEditRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
      },
      options: [
        { value: 'A', label: 'A' },
        { value: 'B', label: 'B' },
        { value: 'C', label: 'C' },
        { value: 'D', label: 'D' },
        { value: 'E', label: 'E' },
      ]
    }
    const materialEditRender = {
      name: 'VxeSelect',
      props: {
        filterable: true,
        clearable: true,
      },
      options: [],
      events: {
        change: this.changeMate
      }
    }
    const outStorehouseRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,
      },
      options: [],
      events: {
        change: this.updateOutDependentOptions
      }
    }

    const outStripRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,
      },
      options: [],
    }

    const outStackRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,
      },
      options: [],
    }

    const inStripRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,
      },
      options: [],
    }

    const inStackRender = {
      name: 'VxeSelect',
      props: {
        clearable: true,
        filterable: false,
      },
      options: [],
    }

    const directTransferFormOptions = {
      titleWidth: 120,
      titleAlign: "right",
      data: {
        transferSource: "",
        // status: "0",
        mateCode: "",
        mateName: '',
        outStorehouseName: "",
        planOutQuantity: 0,
        realOutQuantity: 0,
        inStorehouseName: "",
        inStrip: "",
        inStack: "",
      },
      rules: {
        transferSource: [{ required: true, message: "调拨计划单必填" }],
        mateName: [{ required: true, message: "物料名称必填" }],
        outStorehouseName: [{ required: true, message: "出库仓库必填" }],
        planOutQuantity: [
          { required: true, message: "计划出库量必填" },
          { type: "number", min: 1, message: "数量必须大于0" },
        ],
        realOutQuantity: [
          { required: true, message: "实际出库量必填" },
          { type: "number", min: 0, message: "数量必须大于等于0" },
        ]
      },
      items: [
        {
          field: "transferSource",
          title: "调拨计划单号",
          span: 12,
          itemRender: { name: "VxeInput" },
        },
        {
          field: "mateCode",
          title: "物料名称",
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              filterable: true,
              clearable: true,
            },
            options: [],
            events: {
              change: this.handleDirectTransferMaterialChange
            }
          },
        },
        {
          field: "outStorehouseName",
          title: "出库仓库",
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              clearable: true,
            },
            options: []
          },
          events: {
            change: this.updateDirectTransferOutDependentOptions
          }
        },
        {
          field: "inStorehouseName",
          title: "入库仓库",
          span: 12,
          itemRender: { name: "VxeInput" },
          events: {
            change: this.updateDirectTransferInDependentOptions
          }
        },
        {
          field: "planOutQuantity",
          title: "计划出库量",
          span: 12,
          itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
        },
        {
          field: "realOutQuantity",
          title: "实际出库量",
          span: 12,
          itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
        },
        {
          field: "inStrip",
          title: "入库料条",
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              clearable: true,
              filterable: false,
            },
            options: []
          },
        },
        {
          field: "inStack",
          title: "入库位置",
          span: 12,
          itemRender: {
            name: 'VxeSelect',
            props: {
              clearable: true,
              filterable: false,
            },
            options: []
          },
        },
        { slots: { default: "action" }, span: 24 },
      ],
    };


    const formDialogOptions = {
      titleWidth: 120,
      titleAlign: "right",
      data: {
        transferSource: "",
        status: "0",
        mateCode: "",
        mateName: '',
        outStorehouseName: "",
        outTransferPlan: "",
        outStrip: "",
        outStack: "",
        planOutQuantity: 0,
        realOutQuantity: 0,
        inStorehouseName: "",
        inTransferPlan: "",
        inStrip: "",
        inStack: "",
      },
      rules: {
        transferSource: [{ required: true, message: "调拨计划单必填" }],
        mateName: [{ required: true, message: "物料名称必填" }],
        planOutQuantity: [
          { required: true, message: "计划出库量必填" },
          { type: "number", min: 1, message: "数量必须大于0" },
        ],
        realOutQuantity: [
          { required: true, message: "实际出库量必填" },
          { type: "number", min: 0, message: "数量必须大于等于0" },
        ],
      },
      items: [
        {
          field: "transferSource",
          title: "调拨计划单号",
          span: 12,
          itemRender: { name: "VxeInput" },
        },
        {
          field: "mateCode",
          title: "物料名称",
          span: 12,
          itemRender: materialEditRender,
        },
        {
          field: "outStorehouseName",
          title: "出库仓库",
          span: 12,
          itemRender: outStorehouseRender,
          events: {
            change: this.updateOutDependentOptions
          }
        },
        {
          field: "inStorehouseName",
          title: "入库仓库",
          span: 12,
          itemRender: { name: "VxeInput" },
          events: {
            change: this.updateInDependentOptions
          }
        },
        {
          field: "outStrip",
          title: "出库料条",
          span: 12,
          itemRender: outStripRender,
        },
        {
          field: "inStrip",
          title: "入库料条",
          span: 12,
          itemRender: inStripRender,
        },
        {
          field: "outStack",
          title: "出库垛位",
          span: 12,
          itemRender: outStackRender,
        },
        {
          field: "inStack",
          title: "入库位置",
          span: 12,
          itemRender: inStackRender,
        },
        {
          field: "planOutQuantity",
          title: "计划出库量",
          span: 12,
          itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
        },
        {
          field: "realOutQuantity",
          title: "实际出库量",
          span: 12,
          itemRender: { name: "VxeInput", props: { type: "number", min: 0 } },
        },
        {
          field: "status",
          title: "状态",
          span: 12,
          itemRender: {
            name: "VxeSelect",
            options: [
              { value: "0", label: "计划提交" },
              { value: "1", label: "计划执行" },
              { value: "2", label: "计划完成" },
              { value: "3", label: "计划取消" }
            ],
          },
        },
        { slots: { default: "action" }, span: 24 },
      ],
    };

    return {
      stripEditRender,
      materialEditRender,
      outStorehouseRender,
      outStripRender,
      outStackRender,
      inStripRender,
      inStackRender,
      directTransferFormOptions,
      allMaterialInfoList: [],

      // 表格配置
      gridOptions: {
        columns: [],
        data: [],
        height: 800,
        border: true,
        stripe: true,
        align: "center",
        columnConfig: { resizable: true },
        rowConfig: {
          isHover: true,
          isCurrent: true,
        },

        checkboxConfig: {
          checkField: "checked",
          highlight: true,
          range: true,
          trigger: "click",
        },

        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },

        toolbarConfig: {
          custom: true,
          zoom: true,
          slots: { buttons: "toolbarButtons" },
        },

        customConfig: { immediate: true },
        loading: false,
      },

      // 搜索表单
      formOptions: {
        data: {
          beginTime: new Date().toISOString().split('T')[0] + ' 00:00:00',
          endTime: new Date().toISOString().split('T')[0] + ' 23:59:59',
          crossRegion: "",
          mateCode: "",
          outStorehouseName: "",
          outStrip: "",
        },
        items: [
          {
            field: "beginTime",
            title: "开始时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "endTime",
            title: "结束时间",
            itemRender: { name: "VxeDatePicker", props: { type: "datetime" } },
          },
          {
            field: "outStorehouseName",
            title: "原料库",
            itemRender: {
              name: 'VxeSelect',
              props: {
                clearable: true,
                filterable: true,
              },
              options: []
            },
          },
          {
            field: "outStrip",
            title: "料条",
            itemRender: stripRender,
          },
          { slots: { default: "action" }, align: "center" },
        ],
      },


      formVisible: false,
      formTitle: "新增数据",
      formMode: "add",
      formDialogOptions,


      selectedRows: [],
      directTransferVisible: false,
      directTransferStorehouseOptions: [],
      isSubmitting: false,
      stripRender,
    };
  },

  mounted() {
    this.initGridData();
    this.initData();
  },

  methods: {
    // 初始化表格
    initGridData() {
      this.gridOptions.columns = [
        { type: "checkbox", width: 60 },
        { type: "seq", title: "序号", width: 60 },
        { field: "transferSource", title: "调拨计划单号" },
        {
          field: "status",
          title: "状态",
          formatter: ({ cellValue }) => {
            const statusMap = {
              0: "计划提交",
              1: "计划执行",
              2: "计划完成",
              3: "计划取消"
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        { field: "mateName", title: "物料名称" },
        { field: "outStorehouseName", title: "出库仓库" },
        // { field: "outTransferPlan", title: "出库堆料计划" },
        { field: "outStrip", title: "出库料条" },
        { field: "outStack", title: "垛位" },
        { field: "planOutQuantity", title: "计划出库量" },
        { field: "realOutQuantity", title: "实际出库量" },
        { field: "inStorehouseName", title: "入库仓库" },
        // { field: "inTransferPlan", title: "入库堆料计划" },
        { field: "inStrip", title: "入库料条" },
        { field: "inStack", title: "入库位置" },
      ];
      this.handlePageData();
    },

    getInStorehouseByPath() {
      const path = window.location.hash;
      console.log(path)
      if (path.includes('/zhihuicangchu/stock1/stocktransfer')) {
        return "原料库1#料场";
      }
      if (path.includes('/zhihuicangchu/stock2/stocktransfer')) {
        return "原料库2#料场";
      }
      return "";
    },

    showForm(mode) {
      if (mode === "edit" && this.selectedRows.length !== 1) {
        this.$message({
          message: `请选择一条数据`,
          type: "error",
        });
        return;
      }
      this.formMode = mode;
      this.formTitle = mode === "add" ? "新增数据" : "修改数据";
      this.formVisible = true;
      this.$nextTick(() => {
        if (mode === "edit") {
          this.formDialogOptions.data = { ...this.selectedRows[0] };
          this.clearSelectRow();
          this.updateMaterialDependentOptions(true);
        } else {
          this.$refs.formDialogRef.reset();
          this.formDialogOptions.data = {
            id: null,
            transferSource: this.generateTransferNumber(),
            status: "0",
            mateCode: "",
            mateName: "",
            outStorehouseName: "",
            outTransferPlan: "",
            outStrip: "",
            outStack: "",
            planOutQuantity: 0,
            realOutQuantity: 0,
            inStorehouseName: this.getInStorehouseByPath(),
            inTransferPlan: "",
            inStrip: "",
            inStack: "",
          };
          this.clearMaterialDependentOptions();
        }
      });
    },

    clearMaterialDependentOptions() {
      this.outStorehouseRender.options = [];
      this.outStripRender.options = [];
      this.outStackRender.options = [];
      this.inStripRender.options = [];
      this.inStackRender.options = [];
      this.formDialogOptions.data.outStorehouseName = "";
      this.formDialogOptions.data.outStrip = "";
      this.formDialogOptions.data.outStack = "";
      this.formDialogOptions.data.inStrip = "";
      this.formDialogOptions.data.inStack = "";
    },

    updateMaterialDependentOptions(isEdit = false) {
      const mateCode = this.formDialogOptions.data.mateCode;
      if (mateCode) {
        // 使用完整的物料信息列表
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode);
        if (materialInfoList.length > 0) {
          const storehouses = [...new Set(materialInfoList.map(item => item.name))];
          this.outStorehouseRender.options = storehouses.map(name => ({
            value: name,
            label: name
          }));
          this.formDialogOptions.data.outStorehouseName = storehouses.length === 1 ? storehouses[0] : "";
          this.updateOutDependentOptions();
          this.updateInDependentOptions();
        }
      }
    },

    updateOutDependentOptions() {
      const mateCode = this.formDialogOptions.data.mateCode;
      const outStorehouseName = this.formDialogOptions.data.outStorehouseName;

      if (mateCode && outStorehouseName) {
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode && option.name === outStorehouseName);

        if (materialInfoList.length > 0) {
          const strips = [...new Set(materialInfoList.map(item => item.crossRegion))];
          this.outStripRender.options = strips.map(strip => ({
            value: strip,
            label: strip
          }));

          const stacks = [...new Set(materialInfoList.map(item => item.stackingPosition))];
          this.outStackRender.options = stacks.map(stack => ({
            value: stack,
            label: stack
          }));

          // 根据数据条数自动填充或清空
          this.formDialogOptions.data.outStrip = strips.length === 1 ? strips[0] : "";
          this.formDialogOptions.data.outStack = stacks.length === 1 ? stacks[0] : "";
        }
      } else {
        this.outStripRender.options = [];
        this.outStackRender.options = [];
        this.formDialogOptions.data.outStrip = "";
        this.formDialogOptions.data.outStack = "";
      }
    },

    updateInDependentOptions() {
      const mateCode = this.formDialogOptions.data.mateCode;
      const inStorehouseName = this.formDialogOptions.data.inStorehouseName;

      if (mateCode && inStorehouseName) {
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode && option.name === inStorehouseName);

        if (materialInfoList.length > 0) {
          const strips = [...new Set(materialInfoList.map(item => item.crossRegion))];
          this.inStripRender.options = strips.map(strip => ({
            value: strip,
            label: strip
          }));

          const stacks = [...new Set(materialInfoList.map(item => item.stackingPosition))];
          this.inStackRender.options = stacks.map(stack => ({
            value: stack,
            label: stack
          }));

          // 根据数据条数自动填充或清空
          this.formDialogOptions.data.inStrip = strips.length === 1 ? strips[0] : "";
          this.formDialogOptions.data.inStack = stacks.length === 1 ? stacks[0] : "";
        } else {
          this.inStripRender.options = [];
          this.inStackRender.options = [];
          this.formDialogOptions.data.inStrip = "";
          this.formDialogOptions.data.inStack = "";
        }
      } else {
        this.inStripRender.options = [];
        this.inStackRender.options = [];
        this.formDialogOptions.data.inStrip = "";
        this.formDialogOptions.data.inStack = "";
      }
    },

    //表单弹窗关闭
    handleModalClose() {
      this.formVisible = false;
      this.clearSelectRow();
      this.handlePageData();
    },

    // 提交表单
    async submitForm() {
      // 防抖处理
      if (this.isSubmitting) {
        return;
      }

      try {
        this.isSubmitting = true;

        if (this.formMode === "add") {
          await this.submitAddForm();
        } else {
          await this.submitEditForm();
        }
      } finally {
        // 延迟重置提交状态，防止快速重复点击
        setTimeout(() => {
          this.isSubmitting = false;
        }, 1000);
      }
    },

    clearSelectRow() {
      this.selectedRows = [];
      this.$refs.tableMainRef.setCheckboxRow([]);
    },
    cancel() {
      this.formVisible = false;
      this.formDialogOptions.data.inStorehouseName = this.getInStorehouseByPath();
      this.clearSelectRow();
      this.handlePageData();
    },

    generateTransferNumber() {
      const now = new Date();
      const year = now.getFullYear().toString();
      const month = (now.getMonth() + 1).toString().padStart(2, '0');
      const day = now.getDate().toString().padStart(2, '0');
      const today = `${year}-${month}-${day}`;
      const storedDate = localStorage.getItem('transferNumberDate');
      let counter = parseInt(localStorage.getItem('transferNumberCounter')) || 1;
      if (storedDate !== today) {
        counter = 1;
        localStorage.setItem('transferNumberDate', today);
      }
      const serialNumber = counter.toString().padStart(4, '0');
      counter++;
      if (counter > 9999) {
        counter = 1;
      }
      localStorage.setItem('transferNumberCounter', counter.toString());
      return `DB${today}-${serialNumber}`;
    },

    async submitAddForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formDialogRef.validate((valid) => {
          if (!valid) {
            if (!this.formDialogOptions.data.inStorehouseName) {
              this.formDialogOptions.data.inStorehouseName = this.getInStorehouseByPath();
            }
            this.formDialogOptions.data.transferSource = this.generateTransferNumber();
            insertTransferData(this.formDialogOptions.data)
              .then(() => {
                this.formVisible = false;
                this.handlePageData();
                this.$modal.msgSuccess("新增成功");
                resolve();
              })
              .catch(error => {
                this.$modal.msgError("新增失败");
                reject(error);
              });
          } else {
            this.$modal.msgError("请填写必须字段");
            reject(new Error("表单验证失败"));
          }
        });
      });
    },

    //修改数据
    async submitEditForm() {
      return new Promise((resolve, reject) => {
        this.$refs.formDialogRef.validate((valid) => {
          if (!valid) {
            updateTransferData(this.formDialogOptions.data)
              .then(() => {
                this.formVisible = false;
                this.clearSelectRow();
                this.handlePageData();
                this.$modal.msgSuccess("修改成功");
                resolve();
              })
              .catch(error => {
                // this.$modal.msgError("修改失败");
                reject(error);
              });
          } else {
            this.$modal.msgError("请填写必须字段");
            reject(new Error("表单验证失败"));
          }
        });
      });
    },

    // 删除处理
    handleDelete() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords();
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: "请选择要删除的数据",
          type: "warning",
        });
        return;
      }

      const invalidRows = selectedRows.filter(row =>
        row.status === 2 || row.status === 1
      );
      if (invalidRows.length > 0) {
        const invalidIds = invalidRows.map(row => row.transferSource).join(", ");
        this.$message({
          message: `调拨单号为 ${invalidIds} 的数据状态不为提交，不可删除`,
          type: "warning",
        });
        return;
      }
      this.$modal
        .confirm("是否确认删除？")
        .then(() => {
          const ids = selectedRows.map((row) => row.id);
          return deleteTransferData(ids);
        })
        .then(() => {
          this.handlePageData();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
          this.$modal.msgError("删除失败");
        });
    },

    selectChangeEvent({ records }) {
      this.selectedRows = records;
    },

    searchEvent() {
      this.gridOptions.pagerConfig.currentPage = 1;
      this.handlePageData();
    },

    resetEvent() {
      this.$refs.formRef.reset();

      this.formOptions.data = {
        beginTime: new Date().toISOString().split('T')[0] + ' 00:00:00',
        endTime: new Date().toISOString().split('T')[0] + ' 23:59:59',
        crossRegion: "",
        mateCode: "",
        outStorehouseName: "",
        outStrip: "",
      };

      this.searchEvent();
    },

    handlePageData() {
      this.gridOptions.loading = true;
      const searchParams = { ...this.formOptions.data };
      selectTransferData(searchParams).then((response) => {
        let data = response.data;
        const { pageSize, currentPage } = this.gridOptions.pagerConfig;
        this.gridOptions.pagerConfig.total = data.length;
        this.gridOptions.data = data.slice(
          (currentPage - 1) * pageSize,
          currentPage * pageSize
        );
        this.gridOptions.loading = false;
      });
    },

    pageChangeEvent({ pageSize, currentPage }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.handlePageData();
    },

    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv'
        })
      }
    },

    async initData() {
      try {
        const materialResponse = await initMaterialList();
        console.log('物料数据响应:', materialResponse);
        let materialData = materialResponse.data;
        console.log('物料数据:', materialData);
        this.allMaterialInfoList = materialData.map(item => ({
          value: `${item.materialNumber}`,
          label: `${item.materialName}`,
          name: `${item.storehouseName}`,
          crossRegion: `${item.crossRegion}`,
          stackingPosition: `${item.stackingPosition}`
        }));

        const uniqueMaterialNames = [...new Set(materialData.map(item => item.materialName))];
        const uniqueMaterialOptions = uniqueMaterialNames.map(name => {
          const firstItem = materialData.find(item => item.materialName === name);
          return {
            value: `${firstItem.materialNumber}`,
            label: `${name}`
          };
        });

        this.materialEditRender.options = uniqueMaterialOptions;
        this.directTransferFormOptions.items.find(
          item => item.field === 'mateCode'
        ).itemRender.options = uniqueMaterialOptions;

        const storehouseResponse = await listStorehouse();
        const storehouseOptions = storehouseResponse.data.map(item => ({
          value: item.storehouseName,
          label: item.storehouseName
        }));
        this.formOptions.items.find(
          item => item.field === 'outStorehouseName'
        ).itemRender.options = storehouseOptions;
      } catch (error) {
        this.$modal.msgError("初始化数据失败");
        console.error("初始化数据失败:", error);
      }
    },

    changeMate() {
      this.updateMaterialDependentOptions(false);

      if (this.materialEditRender.options.length > 0) {
        this.updateMaterialName();
      } else {
        this.$watch(
          () => this.materialEditRender.options.length,
          (newLength) => {
            if (newLength > 0) {
              this.updateMaterialName();
              this.updateMaterialDependentOptions(false);
            }
          }
        );
      }
    },

    updateMaterialName() {
      console.log('更新物料编码:', this.formDialogOptions.data.mateCode);
      const materialOption = this.materialEditRender.options.find(
        option => option.value === this.formDialogOptions.data.mateCode
      );
      if (materialOption) {
        this.formDialogOptions.data.mateName = materialOption.label;
      }
    },

    handleDirectTransferMaterialChange({ value }) {
      const materialOption = this.materialEditRender.options.find(
        option => option.value === value
      );
      if (materialOption) {
        this.directTransferFormOptions.data.mateName = materialOption.label;
      }
      this.updateDirectTransferDependentOptions();
    },


    updateDirectTransferDependentOptions() {
      const mateCode = this.directTransferFormOptions.data.mateCode;
      if (mateCode) {
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode);
        if (materialInfoList.length > 0) {
          const strips = [...new Set(materialInfoList.map(item => item.crossRegion))];
          const stacks = [...new Set(materialInfoList.map(item => item.stackingPosition))];
          const inStripItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStrip'
          );
          if (inStripItem) {
            inStripItem.itemRender.options = strips.map(strip => ({
              value: strip,
              label: strip
            }));
            this.directTransferFormOptions.data.inStrip = strips.length === 1 ? strips[0] : '';
          }
          const inStackItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStack'
          );
          if (inStackItem) {
            inStackItem.itemRender.options = stacks.map(stack => ({
              value: stack,
              label: stack
            }));
            this.directTransferFormOptions.data.inStack = stacks.length === 1 ? stacks[0] : '';
          }
        }
      } else {
        const inStripItem = this.directTransferFormOptions.items.find(
          item => item.field === 'inStrip'
        );
        const inStackItem = this.directTransferFormOptions.items.find(
          item => item.field === 'inStack'
        );

        if (inStripItem) {
          inStripItem.itemRender.options = [];
          this.directTransferFormOptions.data.inStrip = '';
        }
        if (inStackItem) {
          inStackItem.itemRender.options = [];
          this.directTransferFormOptions.data.inStack = '';
        }
      }
    },

    updateDirectTransferOutDependentOptions() {
      const mateCode = this.directTransferFormOptions.data.mateCode;
      const outStorehouseName = this.directTransferFormOptions.data.outStorehouseName;

      if (mateCode && outStorehouseName) {
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode && option.name === outStorehouseName);

        if (materialInfoList.length > 0) {
          const strips = [...new Set(materialInfoList.map(item => item.crossRegion))];
          const stacks = [...new Set(materialInfoList.map(item => item.stackingPosition))];

          // 更新直接调拨表单中的出库料条
          const outStripItem = this.directTransferFormOptions.items.find(
            item => item.field === 'outStrip'
          );
          if (outStripItem) {
            outStripItem.itemRender.options = strips.map(strip => ({
              value: strip,
              label: strip
            }));
            this.directTransferFormOptions.data.outStrip = strips.length === 1 ? strips[0] : '';
          }

          // 更新直接调拨表单中的出库垛位
          const outStackItem = this.directTransferFormOptions.items.find(
            item => item.field === 'outStack'
          );
          if (outStackItem) {
            outStackItem.itemRender.options = stacks.map(stack => ({
              value: stack,
              label: stack
            }));
            this.directTransferFormOptions.data.outStack = stacks.length === 1 ? stacks[0] : '';
          }
        }
      } else {
        // 清空直接调拨表单中的出库料条和出库垛位
        const outStripItem = this.directTransferFormOptions.items.find(
          item => item.field === 'outStrip'
        );
        const outStackItem = this.directTransferFormOptions.items.find(
          item => item.field === 'outStack'
        );

        if (outStripItem) {
          outStripItem.itemRender.options = [];
          this.directTransferFormOptions.data.outStrip = '';
        }
        if (outStackItem) {
          outStackItem.itemRender.options = [];
          this.directTransferFormOptions.data.outStack = '';
        }
      }
    },

    updateDirectTransferInDependentOptions() {
      const mateCode = this.directTransferFormOptions.data.mateCode;
      const inStorehouseName = this.directTransferFormOptions.data.inStorehouseName;

      if (mateCode && inStorehouseName) {
        const materialInfoList = this.allMaterialInfoList
          .filter(option => option.value === mateCode && option.name === inStorehouseName);

        if (materialInfoList.length > 0) {
          const strips = [...new Set(materialInfoList.map(item => item.crossRegion))];
          const stacks = [...new Set(materialInfoList.map(item => item.stackingPosition))];

          // 更新直接调拨表单中的入库料条
          const inStripItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStrip'
          );
          if (inStripItem) {
            inStripItem.itemRender.options = strips.map(strip => ({
              value: strip,
              label: strip
            }));
            this.directTransferFormOptions.data.inStrip = strips.length === 1 ? strips[0] : '';
          }

          // 更新直接调拨表单中的入库垛位
          const inStackItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStack'
          );
          if (inStackItem) {
            inStackItem.itemRender.options = stacks.map(stack => ({
              value: stack,
              label: stack
            }));
            this.directTransferFormOptions.data.inStack = stacks.length === 1 ? stacks[0] : '';
          }
        } else {
          // 清空直接调拨表单中的入库料条和入库垛位
          const inStripItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStrip'
          );
          const inStackItem = this.directTransferFormOptions.items.find(
            item => item.field === 'inStack'
          );

          if (inStripItem) {
            inStripItem.itemRender.options = [];
            this.directTransferFormOptions.data.inStrip = '';
          }
          if (inStackItem) {
            inStackItem.itemRender.options = [];
            this.directTransferFormOptions.data.inStack = '';
          }
        }
      } else {
        // 清空直接调拨表单中的入库料条和入库垛位
        const inStripItem = this.directTransferFormOptions.items.find(
          item => item.field === 'inStrip'
        );
        const inStackItem = this.directTransferFormOptions.items.find(
          item => item.field === 'inStack'
        );

        if (inStripItem) {
          inStripItem.itemRender.options = [];
          this.directTransferFormOptions.data.inStrip = '';
        }
        if (inStackItem) {
          inStackItem.itemRender.options = [];
          this.directTransferFormOptions.data.inStack = '';
        }
      }
    },

    async showDirectTransferForm() {
      this.directTransferVisible = true;
      this.formTitle = "直接调拨";
      try {
        const response = await listStorehouseFilter();
        const storehouseOptions = response.data.map(item => ({
          value: item.storehouseName,
          label: item.storehouseName
        }));
        this.directTransferFormOptions.items.find(
          item => item.field === 'outStorehouseName'
        ).itemRender.options = storehouseOptions;
        this.$nextTick(() => {
          this.$refs.directTransferFormRef?.reset();
          this.directTransferFormOptions.data = {
            transferSource: this.generateTransferNumber(),
            // status: "0",
            mateCode: "",
            mateName: "",
            outStorehouseName: "",
            planOutQuantity: 0,
            realOutQuantity: 0,
            inStorehouseName: this.getInStorehouseByPath(),
            inStrip: "",
            inStack: "",
          };
          this.updateDirectTransferDependentOptions();
        });
      } catch (error) {
        this.$modal.msgError("获取仓库列表失败");
        return;
      }
    },

    async submitDirectTransferForm() {
      if (this.isSubmitting) {
        return;
      }

      try {
        this.isSubmitting = true;
        return new Promise((resolve, reject) => {
          this.$refs.directTransferFormRef.validate((valid) => {
            if (!valid) {
              if (!this.directTransferFormOptions.data.inStorehouseName) {
                this.directTransferFormOptions.data.inStorehouseName = this.getInStorehouseByPath();
              }
              this.directTransferFormOptions.data.transferSource = this.generateTransferNumber();
              insertTransferData(this.directTransferFormOptions.data)
                .then(() => {
                  this.directTransferVisible = false;
                  this.handlePageData();
                  this.$modal.msgSuccess("新增成功");
                  resolve();
                })
                .catch(error => {
                  this.$modal.msgError("新增失败");
                  reject(error);
                });
            } else {
              this.$modal.msgError("请填写必须字段");
              reject(new Error("表单验证失败"));
            }
          });
        });
      } finally {
        setTimeout(() => {
          this.isSubmitting = false;
        }, 1000);
      }
    },

    cancelDirectTransfer() {
      this.directTransferVisible = false;
      this.directTransferFormOptions.data.inStorehouseName = this.getInStorehouseByPath();
      this.handlePageData();
    },
  },
}
</script>
