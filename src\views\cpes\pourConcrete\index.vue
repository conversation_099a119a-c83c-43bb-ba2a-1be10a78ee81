<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="mainTable.single"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="mainTable.multiple"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <el-col :span="1.5" style="float: right;">
        <el-radio-group size="mini" v-model="currentOperType" @change="radioGroupChange">
          <el-radio-button size="mini" label="含碳除尘灰"></el-radio-button>
          <el-radio-button size="mini" label="外购白灰"></el-radio-button>
          <el-radio-button size="mini" label="本厂"></el-radio-button>
        </el-radio-group>
      </el-col>

    </el-row>

    <div class="tableInfo">
      <vxe-table
        ref="tableMainRef"
        :data="mainTable.tableData"
        :row-config="{isHover: true}"
        :column-config="{resizable: true}"
        border
        @checkbox-change="mainTableCheckboxChange"
        @checkbox-all="mainTableCheckboxChangeAll"
        header-align="center"
        :height="tableHeight-55"
        stripe
      >
        <vxe-column align="center" fixed="left" type="checkbox" width="60"></vxe-column>
        <!-- <vxe-column field="pourConcreteId" title="主键" width="auto"></vxe-column> -->
        <vxe-column field="workDate" title="记账日期" width="auto"></vxe-column>
        <vxe-column field="operType" title="业务类型" width="auto"></vxe-column>
       <!-- <vxe-column field="prodCenterCode" title="加工中心编码" width="auto"></vxe-column> -->
        <vxe-column field="prodCenterName" title="加工中心名称" width="auto"></vxe-column>
        <vxe-column field="factoryName" title="厂家" width="auto"></vxe-column>
        <vxe-column field="carCode" title="车牌照" width="auto"></vxe-column>
        <vxe-column field="storageNumber" title="仓号" width="auto"></vxe-column>
        <vxe-column field="beginTime" title="开始时间" width="auto"></vxe-column>
        <vxe-column field="endTime" title="结束时间" width="auto"></vxe-column>
        <vxe-column field="beginPostion" title="开始仓位" width="auto"></vxe-column>
        <vxe-column field="endPostion" title="结束仓位" width="auto"></vxe-column>
        <vxe-column field="recordStaff" title="记录人" width="auto"></vxe-column>
        <vxe-column field="receiveStaff" title="接收人" width="auto"></vxe-column>
        <vxe-column field="remark" title="备注" width="auto"></vxe-column>
        <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
        <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
        <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
        <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>

      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTable.pageConfig.pageNum"
        :page-size.sync="mainTable.pageConfig.pageSize"
        :total="mainTable.pageConfig.total"
        @page-change="mainTablePageChange"
      >
      </vxe-pager>
    </div>

    <!-- 添加或修改烧结打灰记录对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" width="800px" append-to-body>

      <el-form ref="form" :model="form" :rules="rules" label-width="120px">

        <el-row>
          <el-col :span="12">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker clearable style="width: 100%"
                              v-model="form.workDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="请选择记账日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓号" prop="storageNumber">
              <el-select v-model="form.storageNumber" style="width: 100%" filterable allow-create clearable
                         placeholder="请选择"
              >
                <el-option
                  v-for="item in storageNumberList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="车牌照" prop="carCode">
              <el-input v-model="form.carCode" style="width: 100%" placeholder="请输入车牌照"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂家" prop="factoryName">
              <el-select v-model="form.factoryName" filterable style="width: 100%" allow-create clearable
                         placeholder="请选择"
              >
                <el-option
                  v-for="item in factoryNameList"
                  :key="item"
                  :label="item"
                  :value="item"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker clearable style="width: 100%"
                              v-model="form.beginTime"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择开始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始仓位" prop="beginPostion">
              <el-input-number v-model="form.beginPostion" style="width: 100%" :precision="2"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker clearable style="width: 100%"
                              v-model="form.endTime"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="请选择结束时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束仓位" prop="endPostion">
              <el-input-number v-model="form.endPostion" style="width: 100%" :precision="2"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="记录人" prop="recordStaff">
              <el-input v-model="form.recordStaff" style="width: 100%" placeholder="请输入记录人"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接收人" prop="receiveStaff">
              <el-input v-model="form.receiveStaff" style="width: 100%" placeholder="请输入接收人"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" style="width: 100%" placeholder="请输入备注"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listConcrete,
  getConcrete,
  delConcrete,
  getFactoryName,
  getStorageNumber,
  saveOrUpdate
} from '@/api/cpes/pourConcrete'
import dayjs from 'dayjs'

export default {
  name: 'Concrete',
  data() {
    return {
      factoryNameList: [],
      storageNumberList: [],
      dateRange: [],
      tableHeight: 300,
      currentOperType: '含碳除尘灰',
      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        tableData: [],
        selectId: [],
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        operType: null,
        prodCenterCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        workDate: [
          {
            required: true, message: '记账日期 不能为空', trigger: 'blur'
          }
        ],
        storageNumber: [
          {
            required: true, message: '业务类型 不能为空', trigger: 'change'
          }
        ],
        carCode: [
          {
            required: true, message: '车牌照 不能为空', trigger: 'change'
          }
        ],
        factoryName: [
          {
            required: true, message: '厂家 不能为空', trigger: 'change'
          }
        ],
        beginTime: [
          {
            required: true, message: '开始时间 不能为空', trigger: 'change'
          }
        ],
        beginPostion: [
          {
            required: true, message: '开始仓位 不能为空', trigger: 'change'
          }
        ],
        endTime: [
          {
            required: true, message: '结束时间 不能为空', trigger: 'change'
          }
        ],
        endPostion: [
          {
            required: true, message: '结束仓位 不能为空', trigger: 'change'
          }
        ]
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
    this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
    getStorageNumber(this.getProdCenterCode()).then(response => {
      this.storageNumberList = response.data
    })
    getFactoryName(this.getProdCenterCode()).then(response => {
      this.factoryNameList = response.data
    })
    this.queryList()
  },
  methods: {
    queryList() {
      this.queryParams.operType = this.currentOperType
      this.queryParams.prodCenterCode = this.getProdCenterCode()

      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      this.mainTable.tableData = []
      listConcrete(this.queryParams).then(response => {
        this.mainTable.tableData = response.rows
        this.mainTable.pageConfig.total = response.total
      })
    },
    // 取消按钮
    cancel() {
      this.editDialog.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        pourConcreteId: null,
        workDate: null,
        operType: null,
        prodCenterCode: null,
        prodCenterName: null,
        factoryName: null,
        carCode: null,
        storageNumber: null,
        beginTime: null,
        endTime: null,
        beginPostion: null,
        endPostion: null,
        recordStaff: null,
        receiveStaff: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.mainTable.pageConfig.pageNum = 1
      this.queryList()
    },
    mainTableCheckboxChange() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    },
    mainTableCheckboxChangeAll() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    },
    mainTablePageChange({ pageSize, currentPage }) {
      this.mainTable.pageConfig.pageNum = currentPage
      this.mainTable.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      this.queryList()
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.workDate = dayjs(new Date()).format('YYYY-MM-DD')
      this.editDialog.open = true
      this.editDialog.title = '添加烧结打灰记录'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }
      if (selectedRows.length != 1) {
        this.$message.warning('同时只能对一掉数据进行修改')
        return
      }
      const pourConcreteId = selectedRows[0].pourConcreteId
      getConcrete(pourConcreteId).then(response => {
        this.form = response.data
        this.editDialog.open = true
        this.editDialog.title = '修改烧结打灰记录'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          var reqPar = JSON.parse(JSON.stringify(this.form))
          reqPar.prodCenterCode = this.getProdCenterCode()
          reqPar.operType = this.currentOperType
          saveOrUpdate(reqPar).then(response => {
            this.$modal.msgSuccess('保存成功')
            this.editDialog.open = false
            this.handleQuery()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      const configIds = selectedRows.map(item => item.pourConcreteId)
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delConcrete(configIds)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '打灰记录', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    radioGroupChange(value) {
      this.handleQuery()
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
