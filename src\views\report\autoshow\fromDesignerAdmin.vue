<template>
	<div>
		<v-form-designer ref="vfd" :designer-config="designerConfig" :banned-widgets="bannedWidgets">
			<template #customToolButtons>
				<!-- <el-button type="text" @click="saveForm<PERSON>son"><i class="el-icon-upload" />保存</el-button> -->
				<el-button type="text" @click="saveFormJson">保存页面</el-button>
				<el-button type="text" @click="showFieldInfo">设置设置字段信息</el-button>
				<el-button type="text" @click="getFieldWid">获取所有字段</el-button>
			</template>
		</v-form-designer>
	</div>
</template>

<script>
import { getformjsonDesigner, saveformjsonadmin } from '@/api/formtemplate/details';

export default {
	data() {
		return {
			designerConfig: {
				languageMenu: false,
				externalLink: false,
				formTemplates: false,
				importJsonButton: false,
				exportJsonButton: false,
				exportCodeButton: false,
				generateSFCButton: false,
				//eventCollapse: false,
				clearDesignerButton: false,
				previewFormButton: true,
				widgetNameReadonly: false,
				//presetCssCode: '.abc { font-size: 16px; }',
			},
			//bannedWidgets: ['input', 'textarea', 'number', 'radio', 'checkbox', 'select', 'time', 'time-range', 'date', 'date-range', 'switch', 'rate', 'color', 'slider', 'static-text', 'html-text', 'button', 'divider', 'picture-upload', 'file-upload', 'rich-editor', 'cascader'],
			bannedWidgets:[],
			formjson: {},
			fieldListApi: {}
		}
	},
	created() {
		this.$nextTick(() => {
			//清除缓存
			this.$refs.vfd.clearDesigner();
			this.getformjson();
		})
	},
	methods: {
		saveFormJson() {
			alert(JSON.stringify(this.$refs.vfd.getFormJson()));
			saveformjsonadmin('local_yhlc', this.$refs.vfd.getFormJson()).then((response) => {
				console.log("返回数据:" + response);
				if (response == 1) {
					this.$message.success("保存成功");
				}
				else {
					this.$message.success("保存失败");
				}
			});
		},
		showFieldInfo(){
			console.log(this.$refs.vfd.getSelectedWidgetRef());
			var selWid = this.$refs.vfd.getSelectedWidgetRef();
			//selWid.setLabel('哈哈哈');
			//console.log(selWid.getWidgetOption());
			console.log(selWid.field);
		},
		getFieldWid() {
			console.log(JSON.stringify(this.$refs.vfd.getFieldWidgets()));
		},
		getformjson() {
			console.log('local_yhlc');
			getformjsonDesigner('local_yhlc','1').then((response) => {
				console.log(response);
				this.formjson = response;
				this.$refs.vfd.setFormJson(this.formjson);
			});
		}
	}
}
</script>

<style lang="scss">
body {
	margin: 0;
}
</style>