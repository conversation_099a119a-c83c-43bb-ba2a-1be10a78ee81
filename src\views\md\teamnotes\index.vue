<template>
  <div class="app-container">
    <!-- 筛选项 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-row>
        <el-form-item label="日期" prop="dataTime">
          <el-date-picker clearable v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="班制" prop="workMode">
          <el-input v-model="queryParams.workMode" placeholder="请输入班制" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="班别" prop="workClass">
          <el-input v-model="queryParams.workClass" placeholder="请输入班别" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="班次" prop="workShift">
          <el-input v-model="queryParams.workShift" placeholder="请输入班次" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
      </el-row>

      <el-row>
        <el-form-item label="工长" prop="foreman">
          <el-input v-model="queryParams.foreman" placeholder="请输入工长" clearable @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-row>

    </el-form>

    <!-- 按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 列表 -->
    <div class="mes_new_table">
      <el-table size="small" v-loading="loading" :data="notesList" @selection-change="handleSelectionChange">
        <af-table-column type="expand">
          <template slot-scope="props">
            <el-descriptions class="margin-top" :column="1" :size="size" border>
              <el-descriptions-item>
                <template slot="label">
                  记录信息
                </template>
                <div style="white-space: pre-line; line-height: 20px;">
                  {{ props.row.notesDetail }}
                </div>
              </el-descriptions-item>

              <el-descriptions-item v-if="props.row.remark!=null">
                <template slot="label">
                  备注
                </template>
                <div style="white-space: pre-line; line-height: 20px;">
                  {{ props.row.remark }}
                </div>
              </el-descriptions-item>

            </el-descriptions>

          </template>
        </af-table-column>
        <af-table-column type="selection" width="55" align="center" />

        <af-table-column label="日期" align="center" prop="dataTime" width="100px" >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.dataTime, '{y}-{m}-{d}') }}</span>
          </template>
        </af-table-column>
        <af-table-column label="加工中心编码" align="left" header-align="center" prop="prodCenterCode"  />
        <af-table-column label="加工中心名称" align="left" header-align="center" prop="prodCenterName" />
        <af-table-column label="班制" align="center" prop="workMode" v-if="workModeFlag"/>
        <af-table-column label="班别" align="center" prop="workClass" />
        <af-table-column label="班次" align="center" prop="workShift"  />
        <af-table-column label="角色" align="center" prop="roleName"  />
        <af-table-column label="工长" align="center" prop="foreman"  />
        <af-table-column label="记录信息" align="center" prop="notesDetail" el-tooltip__popper :show-overflow-tooltip="true"  />
        <af-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == '生效'" type="success">{{
              scope.row.status
              }}</el-tag>
            <el-tag v-if="scope.row.status == '失效'" type="danger">{{
              scope.row.status
              }}</el-tag>
          </template>
        </af-table-column>
        <af-table-column label="备注" align="center" prop="remark" />
        <af-table-column label="创建者" align="center" prop="createBy" />
        <af-table-column label="创建时间" align="center" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </af-table-column>
        <af-table-column label="更新者" align="center" prop="updateBy" />
        <af-table-column label="更新时间" align="center" prop="updateTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}</span>
          </template>
        </af-table-column>
        <af-table-column label="操作" align="center" fixed="right" width="120" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </af-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="getList" />
    </div>


    <!-- 添加或修改班组记事对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="日期" prop="dataTime">
              <el-date-picker clearable v-model="form.dataTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工长" prop="foreman">
              <el-input v-model="form.foreman" placeholder="请输入工长" style="width: 220px;" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="班制" prop="workModeId" v-show="workModeFlag">
              <el-select v-model="form.workModeId" @change="workModeChange" placeholder="请选择班制">
                <el-option v-for="item in workModeOptions" :key="item.id" :label="item.workModeName" :value="item.id"
                  :disabled="item.status == 1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" >
            <el-form-item label="班别" prop="workClass">
              <el-select v-model="form.workClass" placeholder="请选择班别">
                <el-option v-for="item in workClassOptions" :key="item.id" :label="item.workClassName"
                  :value="item.workClassName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="班次" prop="workShift">
              <el-select v-model="form.workShift" placeholder="请选择班次">
                <el-option v-for="item in workClassNameOptions" :key="item.id" :label="item.workShiftName"
                  :value="item.workShiftName"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.effective_or_not" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="角色" prop="status">
              <el-select v-model="form.roleName" @change = "roleChange" placeholder="请选择角色">
                <el-option v-for="dict in roleOptions" :key="dict.value" :label="dict.label"
                  :value="{ value: dict.value, label: dict.label }"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="记录信息" prop="notesDetail">
          <el-input v-model="form.notesDetail" type="textarea" :autosize="{ minRows: 6 }" placeholder="请输入记录信息" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" :autosize="{ minRows: 6 }" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style>
/* 处理文本内容过多展示形式 */
.el-tooltip__popper {
  max-width: 20%
}

/* 解决输入框(text)与文本域(textarea)字体不一致问题 */
.el-textarea__inner {
  font-family: Arial, Helvetica, sans-serif !important;
}
</style>
<script>
import { listNotes, getNotes, delNotes, saveOrUpdate, getWorkNoteRole } from "@/api/md/teamnotes";
import { workMode_queryByProdCenterCode } from "@/api/system/work";
import { listWorkClassByWorkModeId, listWorkShiftByWorkModeId, queryListByWorkMode } from "@/api/system/work";
import dayjs from "dayjs";

export default {
  name: "Notes",
  dicts: ["effective_or_not"],
  props: {
    prodCenterCode: String
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 班组记事表格数据
      notesList: [],
      // 日期范围
      dateRange: [],
      // 班制选项
      workModeOptions: [],
      //  班别选项
      workClassOptions: [],
      //  班次选项
      workClassNameOptions: [],
      // 角色选项
      roleOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //获取班制
      editWorkModelst: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        // dataTime: null,
        workMode: null,
        workClass: null,
        workShift: null,
        foreman: null,
        prodCenterCode: this.prodCenterCode
      },
      workModeFlag: false, // 班制 false 隐藏 , true 显示
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dataTime: [
          { required: true, message: "日期不能为空", trigger: "change" }
        ],
        workModeId: [
          { required: true, message: "班制不能为空", trigger: "blur" }
        ],
        workClass: [
          { required: true, message: "班别不能为空", trigger: "blur" }
        ],
        workShift: [
          { required: true, message: "班次不能为空", trigger: "blur" }
        ],
        foreman: [
          { required: true, message: "工长不能为空", trigger: "blur" }
        ],
        // status: [
        //   { required: true, message: "状态不能为空", trigger: "blur" },
        // ],
      }
    };
  },


  created() {
    this.dateRange.push(dayjs(new Date()).add(-7, "day").startOf('date'));
    this.dateRange.push(dayjs(new Date()).endOf('date'));
    this.getList();
  },
  methods: {
    /** 根据班制查询班别、班次 */
    workModeChange() {
      listWorkClassByWorkModeId(this.form.workModeId).then(res => {
        this.workClassOptions = res.data;
        listWorkShiftByWorkModeId(this.form.workModeId).then(resName => {
          this.workClassNameOptions = resName.data;
        });
      });
    },
    roleChange(data) {
      const { value, label } = data;
      console.log(data);
	    this.form.roleCode = value;
	    this.form.roleName = label;
    },

    /** 查询班组记事列表 */
    getList() {
      this.loading = true;
      if (this.dateRange == null) {
        this.queryParams.dtStart = null;
        this.queryParams.dtEnd = null;
      } else {
        if (this.dateRange.length == 2) {
          this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
            "YYYY-MM-DD"
          );
          this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
            "YYYY-MM-DD"
          );
        }
      }
      this.prodCenterCode = this.$route.query.prodCenterCode;
      this.queryParams.prodCenterCode = this.$route.query.prodCenterCode; //this.prodCenterCode;
      listNotes(this.queryParams).then(response => {
        this.notesList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        notesId: null,
        dataTime: null,
        workModeId: null,
        workMode: null,
        workClass: null,
        workShift: null,
        foreman: null,
        notesDetail: null,
        status: "生效",
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        roleCode: null,
        roleName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.notesId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.workModeFlag = false; // 班制 false 隐藏 , true 显示
      this.reset();
      console.log("调用方法")
      workMode_queryByProdCenterCode(this.prodCenterCode).then(response => {
        this.workModeOptions = response.data;
        this.form.workModeId = this.workModeOptions[0].id;
        this.workModeChange();
        queryListByWorkMode(this.form.workModeId).then(resWork => {
          var result = resWork.data;
          this.form.workClass = result.workClass;
          this.form.workShift = result.workShift;
        });
        // 绑定角色
        getWorkNoteRole(this.prodCenterCode).then(response=>{
          this.roleOptions = response;
        });
        this.open = true;
        this.form.dataTime = new Date();
        this.title = "添加班组记事";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const notesId = row.notesId || this.ids
      getNotes(notesId).then(response => {
        this.form = response.data;
        workMode_queryByProdCenterCode(this.prodCenterCode).then(resWork => {
          this.workModeOptions = resWork.data;
          this.workModeChange();
          this.open = true;
          this.title = "修改班组记事";
        });
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.form.prodCenterCode = this.prodCenterCode;
          const obj = JSON.parse(JSON.stringify(this.form));
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const notesIds = row.notesId || this.ids;
      this.$modal.confirm('是否确认删除班组记事编号为"' + notesIds + '"的数据项？').then(function () {
        return delNotes(notesIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('/api/md/teamnotes/export', {
        ...this.queryParams
      }, `notes_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
