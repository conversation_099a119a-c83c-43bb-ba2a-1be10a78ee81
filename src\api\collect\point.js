import request from "@/utils/request";

// 查询点位配置列表
export function listPoint(query, selectVO) {
  return request({
    url: "/api/collect/point/list",
    method: "get",
    params: query,
    selectVO: selectVO,
  });
}

export function nopageList(data) {
  return request({
    url: "/api/collect/point/nopageList",
    method: "post",
    data: data,
  });
}

// 查询点位配置详细
export function getPoint(pointId) {
  return request({
    url: "/api/collect/point/" + pointId,
    method: "get",
  });
}

// 新增点位配置
export function addPoint(data) {
  return request({
    url: "/api/collect/point",
    method: "post",
    data: data,
  });
}

// 修改点位配置
export function updatePoint(data) {
  return request({
    url: "/api/collect/point",
    method: "put",
    data: data,
  });
}

// 删除点位配置
export function delPoint(pointId) {
  return request({
    url: "/api/collect/point/" + pointId,
    method: "delete",
  });
}

export function saveOrUpdate(data) {
  return request({
    url: "/api/collect/point/saveOrUpdate",
    method: "post",
    data: data,
  });
}

export function getProcess() {
  return request({
    url: "/api/collect/point/getProcess",
    method: "get",
  });
}

export function getOperList() {
  return request({
    url: "/api/collect/point/getOperList",
    method: "get",
  });
}

export function analysisDelay(data) {
  return request({
    url: "/api/collect/point/analysisDelay",
    method: "post",
    data: data,
  });
}

export function selectByIds(data) {
  return request({
    url: "/api/collect/point/selectByIds",
    method: "post",
    data: data,
  });
}
