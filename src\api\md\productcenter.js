import request from "@/utils/request";

// 查询加工中心列表
export function listProductcenter(query) {
  return request({
    url: "/api/md/productcenter/list",
    method: "get",
    params: query,
  });
}

// 查询加工中心详细
export function getProductcenter(prodCenterId) {
  return request({
    url: "/api/md/productcenter/" + prodCenterId,
    method: "get",
  });
}

// 修改加工中心
export function saveOrUpdate(data) {
  return request({
    url: "/api/md/productcenter/saveOrUpdate",
    method: "post",
    data: data,
  });
}

// 删除加工中心
export function delProductcenter(prodCenterId) {
  return request({
    url: "/api/md/productcenter/" + prodCenterId,
    method: "delete",
  });
}

// 查询加工中心详细
export function treeselect() {
  return request({
    url: "/api/md/productcenter/treeselect",
    method: "get",
  });
}

export function treeselectCode() {
  return request({
    url: "/api/md/productcenter/treeselectCode",
    method: "get",
  });
}

//  上移
export function moveUp(prodCenterId) {
  return request({
    url: "/api/md/productcenter/moveUp/" + prodCenterId,
    method: "get",
  });
}

//  下移
export function moveDown(prodCenterId) {
  return request({
    url: "/api/md/productcenter/moveDown/" + prodCenterId,
    method: "get",
  });
}

export function queryByCode(prodCode) {
  return request({
    url: "/api/md/productcenter/queryByCode/" + prodCode,
    method: "get",
  });
}

export function queryByStatus(status) {
  return request({
    url: "/api/md/productcenter/queryByStatus/" + status,
    method: "get",
  });
}

export function querysuffix() {
  return request({
    url: "/api/md/productcenter/querysuffix",
    method: "get",
  });
}



