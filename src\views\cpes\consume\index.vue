<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="daterange"
        >
        </el-date-picker>
      </el-form-item>

      <el-form-item label="班次" prop="workClsass">
        <BWorkShiftSelect v-model="queryParams.workClsass" model-name="烧结排班"/>
      </el-form-item>
      <el-form-item label="班组" prop="workGroup">
        <BWorkClassSelect v-model="queryParams.workGroup" model-name="烧结排班"/>
      </el-form-item>

      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-download"
          plain
          size="mini"
          type="warning"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
        @radio-change="handleRadioChange"
      >
      </vxe-grid>


    </div>

    <!-- 添加或修改烧结物料消耗对话框 -->
    <el-dialog :title="editDialog.title" :visible.sync="editDialog.open" append-to-body width="600">

      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="记账日期" prop="workDate">
              <el-date-picker v-model="form.workDate"
                              clearable
                              placeholder="请选择记账日期"
                              type="date"
                              value-format="yyyy-MM-dd"
                              style="width: 100%;"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划编号" prop="planCode">
              <el-select v-model="form.planCode" clearable style="width: 100%;">
                <el-option v-for="dict in feedinPlanList" :key="dict.planNo"
                           :label="dict.endWorkdate ? `${dict.planNo} (${dict.beginWorkdate} - ${dict.endWorkdate})` : `${dict.planNo} (${dict.beginWorkdate} -至今)`"
                           :value="dict.planNo"

                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="班组" prop="workGroup">
              <BWorkClassSelect v-model="form.workGroup" model-name="烧结排班"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="班次" prop="workClsass">
              <BWorkShiftSelect v-model="form.workClsass" model-name="烧结排班"/>
            </el-form-item>
          </el-col>


        </el-row>

        <el-tabs type="border-card">
          <el-tab-pane label="物料消耗清单">
            <el-row>
              <el-col :span="24">
                <el-button-group style="float: right; margin-right: 10px;">
                  <el-button icon="el-icon-plus" size="small" type="success" @click="editAddHandler">新增</el-button>
                  <el-button icon="el-icon-delete" size="small" type="danger" @click="editDeleteHandler">删除
                  </el-button>
                </el-button-group>
              </el-col>
            </el-row>

            <el-row style="margin-top: 10px">
              <el-col :span="24">
                <vxe-table
                  ref="editTableef"
                  :column-config="{resizable: true}"
                  :data="form.formTableData"
                  :height="500"
                  :row-config="{isHover: true}"
                  border
                  header-align="center"
                  stripe
                >
                  <vxe-column align="center" type="checkbox" width="30"></vxe-column>
                  <vxe-column field="mateCode" title="物料名称" width="45%">
                    <template #default="{ row }">
                      <BMateSelect v-model="row.mateCode"/>
                    </template>
                  </vxe-column>
                  <vxe-column field="binName" title="料仓名称" width="15%">
                    <template #default="{ row }">
                      <el-select v-model="row.binName" clearable placeholder="请选择">
                        <el-option
                          v-for="item in binNameList"
                          :key="item"
                          :label="item"
                          :value="item"
                        >
                        </el-option>
                      </el-select>
                    </template>
                  </vxe-column>
                  <vxe-column field="collWeight" title="采集重量" width="15%"></vxe-column>
                  <vxe-column field="inputWeight" title="录入重量" width="20%">
                    <template #default="{ row }">
                      <el-input-number v-model="row.inputWeight" :precision="3" style="width: 100%"></el-input-number>
                    </template>
                  </vxe-column>
                </vxe-table>
              </el-col>
            </el-row>


          </el-tab-pane>

        </el-tabs>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  delConsume,
  getBinNameList,
  getEditList,
  getFeedinPlan,
  queryforManger,
  saveOrUpdate
} from '@/api/cpes/consume'
import dayjs from 'dayjs'
import XEUtils from 'xe-utils'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import BMateSelect from '@/components/BMateSelect/index.vue'

export default {
  name: 'ConsumeManger',
  components: { BMateSelect, BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      tableHeight: 300,
      dateRange: [],
      binNameList: [],
      feedinPlanList: [],
      mainTable: {
        selectedRadioRow: null,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: []
        }
      },
      showSearch: true,
      editDialog: {
        title: '',
        // 是否显示弹出层
        open: false
      },
      // 查询参数
      queryParams: {
        dtStart: null,
        dtEnd: null,
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null
      },
      // 表单参数
      form: {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        planCode: null,
        formTableData: []
      },
      // 表单校验
      rules: {
        workDate: [
          {
            required: true, message: '记账日期不能为空', trigger: 'blur'
          }
        ],
        workClsass: [
          {
            required: true, message: '时间班组不能为空', trigger: 'blur'
          }
        ],
        workGroup: [
          {
            required: true, message: '管理班组不能为空', trigger: 'blur'
          }
        ],
        planCode: [
          {
            required: true, message: '计划编号 不能为空', trigger: 'blur'
          }
        ]

      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryList()
    getBinNameList(this.getProdCenterCode()).then(res => {
      this.binNameList = res.data
    })
    getFeedinPlan(this.getProdCenterCode()).then(res => {
      this.feedinPlanList = res.data
    })
  },
  methods: {
    queryList() {
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).startOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).endOf('date').format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.prodCenterCode = this.getProdCenterCode()
      this.mainTable.gridOptions.columns = []
      this.mainTable.gridOptions.data = []
      this.mainTable.selectedRadioRow = null
      queryforManger(this.queryParams).then(response => {
        this.mainTable.gridOptions.columns.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })

        response.data.col.forEach((item) => {
          if (item.value.includes('采集的重量') == false) {
            console.log(JSON.stringify(item.value))
            if (item.value === '记账日期') {
              this.mainTable.gridOptions.columns.push({
                field: item.key,
                title: item.value,
                width: 'auto',
                formatter({ cellValue }) {
                  return XEUtils.toDateString(cellValue, 'yyyy-MM-dd')
                }
              })

            } else if (item.value === '加工中心编码') {
              this.mainTable.gridOptions.columns.push({
                field: item.key,
                title: item.value.replace('物料消耗-', ''),
                width: 'auto',
                visible: false
              })
            } else {
              this.mainTable.gridOptions.columns.push({
                field: item.key,
                title: item.value.replace('物料消耗-', ''),
                width: 'auto'
              })
            }
          }
        })
        this.mainTable.gridOptions.data = response.data.rec
      })
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    },
    // 取消按钮
    cancel() {
      this.editDialog.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        workDate: null,
        prodCenterCode: null,
        workClsass: null,
        workGroup: null,
        planCode: null,
        formTableData: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.workDate = dayjs().format('YYYY-MM-DD')
      this.editDialog.open = true
      this.editDialog.title = '添加烧结物料消耗'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      var queryEntity = {}
      queryEntity.prodCenterCode = selectedRows.PROD_CENTER_CODE
      queryEntity.workDate = selectedRows.WORK_DATE
      queryEntity.workClsass = selectedRows.WORK_CLSASS
      queryEntity.workGroup = selectedRows.WORK_GROUP
      queryEntity.planCode = selectedRows.PLAN_CODE

      this.editDialog.open = true
      this.editDialog.title = '修改烧结物料消耗'
      getEditList(queryEntity).then(response => {
        if (response.data.length == 0) {
          this.$message.warning('获取数据库数据失败')
          return
        }
        this.form.workDate = response.data[0].workDate
        this.form.workGroup = response.data[0].workGroup
        this.form.workClsass = response.data[0].workClsass
        this.form.planCode = response.data[0].planCode
        this.form.formTableData = response.data
      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (this.form.formTableData.length == 0) {
          this.$message({
            message: '请填写物料消耗清单',
            type: 'warning'
          })
          return
        }
        const from = this.form
        const saveList = []
        for (let item of this.form.formTableData) {
          var saveObject = {
            consumeId: item.consumeId < 0 ? null : item.consumeId,
            mateCode: item.mateCode,
            collWeight: item.collWeight,
            inputWeight: item.inputWeight,
            binName: item.binName,
            workDate: from.workDate,
            workClsass: from.workClsass,
            workGroup: from.workGroup,
            prodCenterCode: this.getProdCenterCode(),
            planCode: from.planCode
          }
          saveList.push(saveObject)
        }
        if (valid) {
          saveOrUpdate(saveList).then(response => {
            this.$message({
              message: '操作成功',
              type: 'success'
            })
            this.editDialog.open = false
            this.handleQuery()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.mainTable.selectedRadioRow
      if (selectedRows == null) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
      }
      const deletePar = {
        prodCenterCode: selectedRows.PROD_CENTER_CODE,
        workDate: selectedRows.WORK_DATE,
        workClsass: selectedRows.WORK_CLSASS,
        planCode: selectedRows.PLAN_CODE
      }

      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delConsume(deletePar)
        })
        .then(() => {
          this.handleQuery()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    handleRadioChange({ row }) {
      this.mainTable.selectedRadioRow = row
    },
    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '烧结下料量', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    editAddHandler() {
      this.form.formTableData.push({
        consumeId: -1 * new Date().getTime() / 1000,
        mateCode: null,
        collWeight: 0,
        inputWeight: 0
      })
    },
    editDeleteHandler() {
      const selectedRows = this.$refs.editTableef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      selectedRows.forEach(item => {
        this.form.formTableData.splice(this.form.formTableData.indexOf(item), 1)
      })
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10
      this.mainTable.gridOptions.height = this.tableHeight - 5
    })
  }
}
</script>
