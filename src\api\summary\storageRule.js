import request from '@/utils/request'

// 查询存储规则列表
export function listRule(query,selectVO) {
  return request({
    url: '/api/summary/storageRule/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询存储规则详细
export function getRule(rulesId) {
  return request({
    url: '/api/summary/storageRule/' + rulesId,
    method: 'get'
  })
}

// 新增存储规则
export function addRule(data) {
  return request({
    url: '/api/summary/storageRule',
    method: 'post',
    data: data
  })
}

// 修改存储规则
export function updateRule(data) {
  return request({
    url: '/api/summary/storageRule',
    method: 'put',
    data: data
  })
}

// 删除存储规则
export function delRule(rulesId) {
  return request({
    url: '/api/summary/storageRule/' + rulesId,
    method: 'delete'
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/summary/storageRule/saveOrUpdate',
    method: 'post',
    data: data
  })
}

export function getRuleType() {
  return request({
    url: '/api/summary/storageRule/getRuleType',
    method: 'get',
  })
}

export function getRuleValue() {
  return request({
    url: '/api/summary/storageRule/getRuleValue',
    method: 'get',
  })
}

export function getWorkClass() {
  return request({
    url: '/api/summary/storageRule/getWorkClass',
    method: 'get',
  })
}
