import request from '@/utils/request'

// 提交按钮
export function tuyereInsert(data) {
  return request({
    url: '/api/blastFurnaceTuyere/tuyereInsert',
    method: 'post',
    data: data,
  })
}

// 搜索按钮
export function queryList(query) {
  return request({
    url: '/api/blastFurnaceTuyere/list',
    method: 'get',
    params: query
  })
}
// 修改数据
export function tuyereEdit(data) {
  return request({
    url: '/api/blastFurnaceTuyere/tuyereEdit',
    method: 'put',
    data: data,
  })
}

// 删除数据
export function tuyereDelete(data) {
  return request({
    url: '/api/blastFurnaceTuyere/tuyereDelete',
    method: 'delete',
    data: data,
  })
}


