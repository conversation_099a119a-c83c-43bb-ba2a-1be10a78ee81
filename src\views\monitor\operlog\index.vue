<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模块标题" prop="module">
        <el-input
          v-model="queryParams.module"
          placeholder="请输入模块标题"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作内容" prop="description">
        <el-select v-model="queryParams.description" placeholder="请选择操作内容" clearable size="small">
          <el-option
            v-for="dict in operatorTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictLabel"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="操作类别" prop="operatorType">
        <el-select v-model="queryParams.operatorType" placeholder="请选择操作类别" clearable size="small">
          <el-option
            v-for="dict in operatorTypeOptions"
            :key="dict.dictValue"
            :label="dict.dictLabel"
            :value="dict.dictValue"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="操作人员" prop="username">
        <el-input
          v-model="queryParams.username"
          placeholder="请输入操作人员"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="用户id" prop="userId">
        <el-input
          v-model="queryParams.userId"
          placeholder="请输入用户id"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="主机地址" prop="ip">
        <el-input
          v-model="queryParams.ip"
          placeholder="请输入主机地址"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          size="mini"
          @click="clearLog"
          v-hasPermi="['monitor:operlog:clear']"
        >清空日志</el-button>
      </el-col>
	  <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="logList" @selection-change="handleSelectionChange" border>
      <el-table-column label="模块标题" align="center" prop="module" />
      <el-table-column label="操作类型" align="center" prop="description" />
      <el-table-column label="操作详细" align="center" prop="content" :show-overflow-tooltip="true"/>
      <el-table-column label="返回参数" align="center" prop="returns" :show-overflow-tooltip="true"/>
      <el-table-column label="操作人员" align="center" prop="username" />

      <el-table-column label="用户id" align="center" prop="userId" />
      <el-table-column label="主机地址" align="center" prop="ip" />
      <el-table-column label="操作时间" align="center" prop="createTime" />
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改操作日志记录对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模块标题" prop="module">
          <el-input v-model="form.module" placeholder="请输入模块标题" />
        </el-form-item>
        <el-form-item label="操作详细" prop="content">
          <el-input v-model="form.content" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="操作内容" prop="description">
          <el-input v-model="form.description" placeholder="请输入操作内容" />
        </el-form-item>
        <el-form-item label="操作类别" prop="operatorType">
          <el-select v-model="form.operatorType" placeholder="请选择操作类别">
            <el-option label="请选择字典生成" value="" />
          </el-select>
        </el-form-item>
        <el-form-item label="操作人员" prop="username">
          <el-input v-model="form.username" placeholder="请输入操作人员" />
        </el-form-item>
        <el-form-item label="用户id" prop="userId">
          <el-input v-model="form.userId" placeholder="请输入用户id" />
        </el-form-item>
        <el-form-item label="主机地址" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入主机地址" />
        </el-form-item>
        <el-form-item label="返回参数" prop="returns">
          <el-input v-model="form.returns" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLog, getLog, delLog, addLog, updateLog, exportLog, clearLog } from "@/api/log/log";
export default {
  name: "Log",
  data() {
    return {
      operatorTypeOptions:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 操作日志记录表格数据
      logList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        module: null,
        content: null,
        description: null,
        operatorType: null,
        username: null,
        userId: null,
        ip: null,
        returns: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
    this.getDicts("sys_log_operatorType").then(response => {
      this.operatorTypeOptions = response.data;
    });
  },
  methods: {
    /** 查询操作日志记录列表 */
    getList() {
      this.loading = true;
      listLog(this.queryParams).then(response => {
        this.logList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        module: null,
        content: null,
        description: null,
        operatorType: null,
        username: null,
        userId: null,
        ip: null,
        returns: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加操作日志记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getLog(id).then(response => {
        this.form = response.object;
        this.open = true;
        this.title = "修改操作日志记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateLog(this.form).then(response => {
                this.msgSuccess(response.message_description);
                this.open = false;
                this.getList();
            });
          } else {
            addLog(this.form).then(response => {
                this.msgSuccess(response.message_description);
                this.open = false;
                this.getList();
            });
          }
        }
      });
    },
    /** 清空按钮操作 */
    clearLog() {
      this.$confirm('是否确认清空操作日志?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return clearLog();
        }).then(() => {
          this.getList();
          this.msgSuccess("清空成功");
        }).catch(function() {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$confirm('是否确认删除?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delLog(ids);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有操作日志记录数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
           exportLog(queryParams).then((res) => {
              //将文件流转成blob形式
              const blob = new Blob([res],{type: 'application/vnd.ms-excel'});
              let filename ='操作日志记录.xls';
              //创建一个超链接，将文件流赋进去，然后实现这个超链接的单击事件
              const eLink = document.createElement('a');
              eLink.download = filename;
              eLink.style.display = 'none';
              eLink.href = URL.createObjectURL(blob);
              document.body.appendChild(eLink);
              eLink.click();
              URL.revokeObjectURL(eLink.href); // 释放URL 对象
              document.body.removeChild(eLink);
          });
        }).then(() => {
          this.msgSuccess("文件下载中，请稍后");
        }).catch(function() {});
    }
  }
};
</script>
