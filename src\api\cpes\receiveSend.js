import request from '@/utils/request'

// 查询烧结物料收发主列表
export function listSend(query, selectVO) {
  return request({
    url: '/api/cpes/receiveSend/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询烧结物料收发主详细
export function getSend(receiveSendId) {
  return request({
    url: '/api/cpes/receiveSend/' + receiveSendId,
    method: 'get'
  })
}

// 新增烧结物料收发主
export function addSend(data) {
  return request({
    url: '/api/cpes/receiveSend',
    method: 'post',
    data: data
  })
}

// 修改烧结物料收发主
export function updateSend(data) {
  return request({
    url: '/api/cpes/receiveSend',
    method: 'put',
    data: data
  })
}

// 删除烧结物料收发主
export function delSend(receiveSendId) {
  return request({
    url: '/api/cpes/receiveSend/' + receiveSendId,
    method: 'delete'
  })
}

export function queryforManger(query) {
  return request({
    url: '/api/cpes/receiveSend/queryforManger',
    method: 'get',
    params: query
  })
}

export function getEdit(receiveSendId) {
  return request({
    url: '/api/cpes/receiveSend/getEdit/' + receiveSendId,
    method: 'get'
  })
}
export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/receiveSend/saveOrUpdate',
    method: 'post',
    data: data
  })
}
