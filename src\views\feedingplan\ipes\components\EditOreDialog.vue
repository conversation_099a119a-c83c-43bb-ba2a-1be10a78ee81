<template>
  <!-- <el-dialog title="编辑矿" :visible.sync="dialogVisible" width="80%" append-to-body @close="handleClose"> -->
    <el-dialog title="编辑矿" :visible.sync="dialogVisible" width="80%" append-to-body>
    <div class="edit-ore-container">
      <!-- 数据面板部分 -->
      <div class="data-panel">
        <el-card>
          <!-- <div slot="header">
            <span>数据面板</span>
          </div> -->
          <div class="panel-content">
            <el-table :data="panelTableData" border style="width: 100%;" size="small" class="panel-table">
              <el-table-column prop="type" label="" width="120" align="center" fixed="left">
                <template slot-scope="scope">
                  <span class="type-label">{{ scope.row.type }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-for="(material, index) in headerInfo"
                :key="`header-${index}-${Object.keys(material)[0]}`"
                :label="Object.values(material)[0]"
                align="center"
                min-width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.type === '物料总量'" class="amount-value total-amount">
                    {{ formatNumber(getStatValue(index, 0)) }}
                  </span>
                  <span v-else-if="scope.row.type === '物料剩余量'" class="amount-value remain-amount">
                    {{ formatNumber(getStatValue(index, 1)) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>

      <!-- 列表部分 -->
      <div class="ore-list">
        <!-- 主数据表格 -->
        <div class="main-data-container">
          <el-table :data="tableData" border style="width: 100%; margin-top: 16px;" v-loading="loading" class="main-table" :height="mainTableHeight">
          <el-table-column prop="slotname" label="槽名" width="100"  align="center" fixed="left" border :resizable=false >
            <template slot-scope="scope">
              <span class="slot-text">{{ scope.row.slotname }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="siloname" label="槽号" width="80" align="center" fixed="left" border :resizable=false >
            <template slot-scope="scope">
              <span class="silo-text">{{ scope.row.silocode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="物料选择" width="280" align="center" fixed="left" border :resizable=false >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.materialnumber"
                clearable
                placeholder="请选择物料"
                filterable
                @clear="handleClear(scope.row)"
                @change="handleOreChange(scope.row)"
                class="material-select">
                <el-option
                  v-for="(item, index) in oreOptions"
                  :key="`${item.materialnumber}-${index}`"
                  :label="`${item.materialname}`"
                  :value="item.materialnumber">
                  <span class="option-name">{{ item.materialname }}</span>
                  <span class="option-code">{{ item.materialnumber }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            v-for="channel in 6"
            :key="`CH${channel}`"
            :label="`CH${channel}`"
            width="120"
            align="center">
            <template slot-scope="scope">
              <el-input-number
                v-model="scope.row[`CH${channel}`]"
                :min="0"
                :max="getMaterialMaxValue(scope.row, `CH${channel}`)"
                :precision="2"
                size="small"
                :controls="false"
                @change="handleValueChange(scope.row, `CH${channel}`)"
                class="value-input">
              </el-input-number>
            </template>
          </el-table-column>
        </el-table>
        </div>

        <!-- 合计表格 -->
        <div class="summary-fixed-container">
          <el-table :data="summaryTableData" border style="width: 100%;" class="summary-table" :show-header="false" :span-method="summarySpanMethod">
            <el-table-column prop="slotname" label="槽名" width="100" align="center" fixed="left">
              <template slot-scope="scope">
                <span class="summary-text">{{ scope.row.slotname }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="siloname" label="槽号" width="80" align="center" fixed="left">
              <template slot-scope="scope">
                <span class="summary-text">{{ scope.row.silocode }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物料选择" width="280" align="center" fixed="left">
              <template slot-scope="scope">
                <span class="summary-text">{{ scope.row.materialname }}</span>
              </template>
            </el-table-column>
            <el-table-column
              v-for="channel in 6"
              :key="`summary-ch${channel}`"
              :label="`CH${channel}`"
              width="120"
              align="center">
              <template slot-scope="scope">
                <span class="summary-value">{{ formatSummaryValue(scope.row[`CH${channel}`]) }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getOreSelect, getOreMapping, saveOreMapping } from "@/api/feedingplan/feedingpage";

export default {
  name: "EditOreDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    planId: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      saving: false,
      oreOptions: [],
      tableData: [],
      headerInfo: [],
      statData: [],
      mainTableHeight: 280
    };
  },
  computed: {
    panelTableData() {
      return [
        { type: '物料总量' },
        { type: '物料剩余量' }
      ];
    },
    summaryTableData() {
      // 合计行数据
      return this.calculateSummaryRows();
    }
  },
  watch: {
    visible(val) {
      // console.log("变化:", val);
      this.dialogVisible = val;
      if (val) {
      
        this.loadData();
      }
    },
    dialogVisible(val) {

      this.$emit('update:visible', val);
    }
  },
  mounted() {
    this.calculateTableHeight();
  },
  methods: {
    /** 加载数据 */
    async loadData() {
      if (!this.planId) {
        this.$message.warning("计划ID不能为空");
        return;
      }

      this.loading = true;

      try {
      
        const mappingResponse = await getOreMapping(this.planId);

        if (mappingResponse.code === 200) {
          const data = mappingResponse.data;
          this.headerInfo = data.headerinfo || [];
          this.statData = data.stat || [];
          this.tableData = this.processMainData(data.maindata || []);

          // 获取下拉框选项数据
          await this.loadOreOptions();

          // 初始化计算物料剩余量
          this.$nextTick(() => {
            this.calculateMaterialRemaining();
          });

        } else {
          throw new Error(`获取数据失败: ${mappingResponse.msg || '未知错误'}`);
        }

      } catch (error) {
        console.error("加载数据失败:", error);
        this.$message.error("请求失败");
        // 初始化空数据
        this.headerInfo = [];
        this.statData = [];
        this.tableData = [];
        this.oreOptions = [];
      } finally {
        this.loading = false;
      }
    
    },



    /** 加载下拉框选项数据 */
    async loadOreOptions() {
      try {
        const selectResponse = await getOreSelect(this.planId);

        if (selectResponse.code === 200) {
          this.oreOptions = selectResponse.data || [];
        } else {
          throw new Error(`获取下拉框选项失败: ${selectResponse.msg || '未知错误'}`);
        }
      } catch (error) {
        console.error("获取下拉框选项失败:", error);
        this.oreOptions = [];
        this.$message.warning("获取物料选项失败");
      }
    },



    /** 处理主数据 */
    processMainData(maindata) {
      if (!maindata || maindata.length === 0) {
        return [];
      }

      return maindata.map(item => ({
        planid: item.planid || '',
        materialnumber: item.materialnumber || '',
        materialname: item.materialname || '',
        CH1: Number(item.CH1) || 0,
        CH2: Number(item.CH2) || 0,
        CH3: Number(item.CH3) || 0,
        CH4: Number(item.CH4) || 0,
        CH5: Number(item.CH5) || 0,
        CH6: Number(item.CH6) || 0,
        silocode: item.silocode || '',
        siloname: item.siloname || item.silocode || '',
        slotname: item.slotname || item.siloname || item.silocode || '',
        prodcentercode: item.prodcentercode || '',
        prodcentername: item.prodcentername || ''
      }));
    },

    /** 获取统计数据值 */
    getStatValue(materialIndex, statIndex) {
      if (!this.statData || !this.statData[statIndex]) {
        return 0;
      }

      const statItem = this.statData[statIndex];
      const keys = Object.keys(statItem);

      if (materialIndex < keys.length) {
        return statItem[keys[materialIndex]] || 0;
      }

      return 0;
    },

    /** 获取物料的最大可输入值（基于剩余量） */
    getMaterialMaxValue(row, channel) {
      if (!row.materialnumber) {
        return 9999; // 如果没有选择物料，返回一个较大的默认值
      }

      // 获取当前物料的剩余量
      const remainingAmount = this.getMaterialRemaining(row.materialnumber);

      // 当前输入框的值
      const currentValue = parseFloat(row[channel] || 0);

      // 最大值 = 剩余量 + 当前值（因为剩余量已经扣除了当前值）
      const maxValue = remainingAmount + currentValue;

      return Math.max(0, maxValue);
    },

    /** 获取指定物料的剩余量 */
    getMaterialRemaining(materialNumber) {
      if (!materialNumber || !this.headerInfo || !this.statData || this.statData.length < 2) {
        return 0;
      }

      // 去掉NM_前缀进行匹配
      const normalizedMaterialNumber = materialNumber.replace(/^NM_/, '');

      // 在headerInfo中查找对应的物料索引
      let materialIndex = -1;
      this.headerInfo.forEach((materialInfo, index) => {
        const headerMaterialNumber = Object.keys(materialInfo)[0];
        const normalizedHeaderNumber = headerMaterialNumber.replace(/^NM_/, '');
        if (normalizedHeaderNumber === normalizedMaterialNumber) {
          materialIndex = index;
        }
      });

      if (materialIndex === -1) {
        return 0;
      }

      // 获取剩余量数据
      const remainingData = this.statData[1] || {};
      const keys = Object.keys(remainingData);

      if (materialIndex < keys.length) {
        return parseFloat(remainingData[keys[materialIndex]] || 0);
      }

      return 0;
    },

    /** 处理后端返回的数据 */
    processBackendData(backendData) {
      const processedData = backendData.map(item => ({
        planid: item.planid || '',
        materialnumber: item.materialnumber || '',
        materialname: item.materialname || '',
        CH1: Number(item.CH1) || 0,
        CH2: Number(item.CH2) || 0,
        CH3: Number(item.CH3) || 0,
        CH4: Number(item.CH4) || 0,
        CH5: Number(item.CH5) || 0,
        CH6: Number(item.CH6) || 0,
        silocode: item.silocode || '',
        siloname: item.siloname || item.silocode || '',
        prodcentercode: item.prodcentercode || '',
        prodcentername: item.prodcentername || ''
      }));
      
      return processedData.sort((a, b) => {
        if (a.silocode && b.silocode) {
          return a.silocode.localeCompare(b.silocode);
        }
        return 0;
      });
    },

    /** 处理物料选择变化 */
    handleOreChange(row) {
     if(!row.materialnumber){
row.materialname = ""
     }
      const selectedMaterial = this.oreOptions.find(item => item.materialnumber === row.materialnumber);
      if (selectedMaterial) {
        row.materialname = selectedMaterial.materialname;
       
      }
      // 重新计算物料剩余量
      
      this.calculateMaterialRemaining();
      
      // console.log("物料变化:", row);
      this.$emit('ore-change', row);
    },

    /** 处理数值变化 */
    handleValueChange(row, channel) {
      // 重新计算物料剩余量
      this.calculateMaterialRemaining();
      
      this.$emit('value-change', { row, channel, value: row[channel] });
    },

    /** 计算合计行数据 */
    calculateSummaryRows() {
      // 烧结矿合计
      const sinterSum = this.calculateSinterSum();
      // 总重量合计
      const totalSum = this.calculateTotalSum();

      return [sinterSum, totalSum];
    },

    /** 计算烧结矿合计 */
    calculateSinterSum() {
      const sinterRows = this.tableData.filter(row =>
        row.materialname && row.materialname.includes('烧结矿')
      );

      const sum = {
        slotname: '烧结矿合计',
        siloname: '',
        materialname: '',
        CH1: 0,
        CH2: 0,
        CH3: 0,
        CH4: 0,
        CH5: 0,
        CH6: 0,
        isSummary: true
      };

      sinterRows.forEach(row => {
        for (let i = 1; i <= 6; i++) {
          sum[`CH${i}`] += parseFloat(row[`CH${i}`] || 0);
        }
      });

      return sum;
    },

    /** 计算总重量合计 */
    calculateTotalSum() {
      const sum = {
        slotname: '总重量合计',
        siloname: '',
        materialname: '',
        CH1: 0,
        CH2: 0,
        CH3: 0,
        CH4: 0,
        CH5: 0,
        CH6: 0,
        isSummary: true
      };

      this.tableData.forEach(row => {
        for (let i = 1; i <= 6; i++) {
          sum[`CH${i}`] += parseFloat(row[`CH${i}`] || 0);
        }
      });

      return sum;
    },

    /** 格式化合计值显示 */
    formatSummaryValue(value) {
      if (value === 0) return '0';
      return parseFloat(value).toFixed(2);
    },

    /** 格式化数字显示 */
    formatNumber(value) {
      if (!value) return '0';
      const num = parseFloat(value);
      if (isNaN(num)) return '0';
      return num.toLocaleString();
    },

    /** 合计表格单元格合并 */
    summarySpanMethod({ columnIndex }) {
      // 合并前三列
      if (columnIndex === 0) {
        return {
          rowspan: 1,
          colspan: 3
        };
      } else if (columnIndex === 1 || columnIndex === 2) {
        return {
          rowspan: 0,
          colspan: 0
        };
      }
      return {
        rowspan: 1,
        colspan: 1
      };
    },

    /**处理清除按钮 */
    handleClear(){
      console.log("111")
    },
    /** 处理取消 */
    handleCancel() {
      this.dialogVisible = false;
    },

    /** 处理关闭 */
    // handleClose() {
    //   if (this.hasUnsavedChanges()) {
    //     this.$confirm('有未保存的修改，确定要关闭吗？', '提示', {
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'warning'
    //     }).then(() => {
    //       this.resetData();
    //       this.$emit('close');
    //     }).catch(() => {
    //       // 用户取消关闭
    //     });
    //   } else {
    //     this.resetData();
    //     this.$emit('close');
    //   }
    // },
    
    /** 检查是否有未保存的修改 */
    // hasUnsavedChanges() {
    //   return this.tableData.some(row => row.materialnumber);
    // },

    /** 保存 */
    async handleSave() {
      if (!this.planId) {
        this.$message.warning("计划ID不能为空");
        return;
      }

      this.saving = true;
      try {
        // 保存数据格式与获取接口相同
        const saveData = {
          headerinfo: this.headerInfo,
          stat: this.statData,
          maindata: this.buildSaveMainData()
        };

        const response = await saveOreMapping(this.planId, saveData);

        if (response.code === 200) {
          this.$message.success("保存成功");
          this.$emit('save-success', this.tableData);
          this.dialogVisible = false;
        } else {
          this.$message.error(response.msg || "保存失败");
        }

      } catch (error) {
        console.error("保存失败:", error);
        this.$message.error("保存失败");
      } finally {
        this.saving = false;
      }
    },

    /** 构建保存数据 */
    buildSaveMainData() {
      // 过滤掉合计行
      const dataRows = this.tableData.filter(row => !row.isSummary);

      return dataRows.map(row => ({
        planid: row.planid || this.planId,
        materialnumber: row.materialnumber || '',
        materialname: row.materialname || '',
        CH1: String(row.CH1 || 0),
        CH2: String(row.CH2 || 0),
        CH3: String(row.CH3 || 0),
        CH4: String(row.CH4 || 0),
        CH5: String(row.CH5 || 0),
        CH6: String(row.CH6 || 0),
        silocode: row.silocode || '',
        siloname: row.siloname || '',
        slotname: row.slotname || '',
        prodcentercode: row.prodcentercode || '',
        prodcentername: row.prodcentername || ''
      }));
    },

    /** 计算物料剩余量 */
    calculateMaterialRemaining() {
    
      if (!this.headerInfo || !this.statData || this.statData.length < 2) {
        return;
      }

      const originalTotalData = this.statData[0] || {};

      // 按物料编号分组
      const materialUsage = {};
      this.tableData.forEach(row => {
        if (row.materialnumber) {
          if (!materialUsage[row.materialnumber]) {
            materialUsage[row.materialnumber] = 0;
          }
          for (let i = 1; i <= 6; i++) {
            materialUsage[row.materialnumber] += parseFloat(row[`CH${i}`] || 0);
          }
        }
      });

      // 更新剩余量数据
      const remainingData = { ...this.statData[1] };
      this.headerInfo.forEach((materialInfo, index) => {
        const materialNumber = Object.keys(materialInfo)[0];
        const materialKey = Object.keys(originalTotalData)[index];

        if (materialKey && originalTotalData[materialKey] !== undefined) {
          const totalAmount = parseFloat(originalTotalData[materialKey] || 0);
          const usedAmount = materialUsage[materialNumber] || 0;
          const remaining = Math.max(0, totalAmount - usedAmount);

          // 更新剩余量
          remainingData[materialKey] = remaining;
        }
      });

      // 更新statData
      this.$set(this.statData, 1, remainingData);
    },

    /** 计算表格高度 */
    calculateTableHeight() {
      this.$nextTick(() => {
        const containerHeight = window.innerHeight * 0.75; // 75vh
        const panelHeight = 120; // 数据面板高度
        const summaryHeight = 120; // 合计表格高度
        const padding = 80; // 内边距

        this.mainTableHeight = containerHeight - panelHeight - summaryHeight - padding;

        // 最小高度
        if (this.mainTableHeight < 200) {
          this.mainTableHeight = 200;
        }
      });
    },

    /** 计算物料剩余量 */
    calculateMaterialRemaining() {
     

      if (!this.headerInfo || !this.statData || this.statData.length < 2) {
        return;
      }

      // 原始总量数据
      const originalTotalData = this.statData[0] || {};
   

      // 按物料编号分组计算用量
      const materialUsage = {};

      this.tableData.forEach(row => {
       
        if (row.materialnumber) {
          if (!materialUsage[row.materialnumber]) {
            materialUsage[row.materialnumber] = 0;
          }

          // 累加用量
          for (let i = 1; i <= 6; i++) {
            materialUsage[row.materialnumber] += parseFloat(row[`CH${i}`] || 0);
          }

        }
      });

    

      // 更新剩余量数据
      const remainingData = { ...this.statData[1] };
      
      this.headerInfo.forEach((materialInfo, index) => {

        let materialNumber = Object.keys(materialInfo)[0];
        //去掉NM_前缀
        const normalizedMaterialNumber = materialNumber.replace(/^NM_/, '');

        const materialKey = Object.keys(originalTotalData)[index];

       

        if (materialKey && originalTotalData[materialKey] !== undefined) {
          const totalAmount = parseFloat(originalTotalData[materialKey] || 0);
          // 查找用量
          const usedAmount = materialUsage[normalizedMaterialNumber] || 0;
          const remaining = Math.max(0, totalAmount - usedAmount);

          remainingData[materialKey] = remaining;
        }
      });

      
      // 更新statData
      this.$set(this.statData, 1, remainingData);
     
    },

    /** 重置数据 */
    resetData() {
      this.tableData = [];
      this.oreOptions = [];
      this.headerInfo = [];
      this.statData = [];
      this.loading = false;
      this.saving = false;
    }
  }
};
</script>

<style scoped>

/* 去掉弹窗的margin */
.el-dialog {
  margin: 0 !important;
}

.edit-ore-container {
  height: 75vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

.data-panel {
  flex: 0 0 auto;
  margin-bottom: 24px;
}

.data-panel .el-card__header {
  font-size: 16px;
  font-weight: 600;
  padding: 18px 20px;
}

.panel-content {
  padding: 0;
}

.panel-table .el-table th {
  font-size: 14px;
  font-weight: 600;
  padding: 16px 0;
}

.panel-table .el-table td {
  padding: 14px 0;
  font-size: 14px;
}

.type-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.amount-value {
  font-weight: 600;
  font-size: 15px;
}

.total-amount {
  color: #67c23a;
}

.remain-amount {
  color: #e6a23c;
}

.no-data-panel {
  padding: 20px;
  text-align: center;
  color: #909399;
  font-size: 14px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

.ore-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.main-data-container {
  flex: 1;
  overflow: hidden;
  margin-bottom: 8px;
}

.summary-fixed-container {
  flex: 0 0 auto;
  border-top: 2px solid #409eff;
  background-color: #f0f9ff;
}

.main-table .el-table th {
  font-size: 15px;
  font-weight: 600;
  padding: 12px 0;
}

.main-table .el-table td {
  padding: 8px 0;
  font-size: 14px;
}

.silo-text {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.material-select .el-input__inner {
  font-size: 14px;
  padding: 6px 12px;
  height: 32px;
}

.option-code {
  float: left;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.option-name {
  float: right;
  color: #8492a6;
  font-size: 13px;
}

.value-input {
  width: 100%;
}

.value-input .el-input__inner {
  font-size: 14px;
  padding: 8px 12px;
  height: 32px;
  text-align: center;
  border-radius: 4px;
}

.value-input .el-input__inner:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 槽名列样式 */
.slot-text {
  font-weight: 500;
  color: #303133;
}

/* 合计行样式 */
.summary-row {
  font-weight: bold;
  color: #409eff;
  background-color: #f0f9ff;
  font-size: 14px;
}

.summary-text {
  font-weight: bold;
  color: #409eff;
}

.summary-value {
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
}

/* 合计表格样式 */
.summary-table .el-table td {
  background-color: #f0f9ff !important;
  border-bottom: 1px solid #e6f7ff;
  padding: 12px 0;
}



.dialog-footer {
  padding: 20px 0;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background: #fafafa;
  margin: 0 -20px -20px -20px;
}

.dialog-footer .el-button {
  padding: 12px 28px;
  border-radius: 6px;
  font-weight: 500;
  margin: 0 8px;
  font-size: 14px;
}
</style>
