<template>
  <div class="app-container" style="padding: 0px;">
    <hr style="border: 5px solid #409EFF;"/>
    <el-tabs type="border-card" style="margin-top:0px;">
      <el-tab-pane>
        <span slot="label"><i class="el-icon-edit"></i> 风口信息录入</span>
        <div class="mes_table">
          <span style="font-weight: bold;margin-left: 34px;">风口号:</span><el-select v-model="parameter.tuyereNumber" filterable clearable   placeholder="请选择" style="margin-left: 10px;width: 193px;">
            <el-option
              v-for="dict in dict.type.blastFurnace_tuyereNumber" :key="dict.value" :label="dict.label" :value="dict.value">
            </el-option>
          </el-select>
          <span style="font-weight: bold;margin-left: 44px;">直径:</span>
          <el-input v-model="parameter.diameter" placeholder="请输入内容" type="number"
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
          <span style="font-weight: bold;margin-left: 30px;">长度:</span>
          <el-input v-model="parameter.length" placeholder="请输入内容" type="number"
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
          <span style="font-weight: bold;margin-left: 16px;">面积:</span>
          <el-input v-model="parameter.area" placeholder="请输入内容" type="number"
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
        </div>

        <div class="mes_table_second" >
          <span style="font-weight: bold;margin-left: 48px;">厂家:</span>
          <el-input v-model="parameter.manufacturer" placeholder="请输入内容" clearable
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
          <span style="font-weight: bold;margin-left: 16px;">更换时间:</span>
          <el-date-picker style="width: 222px;margin-left: 10px;width: 193px;"
                          v-model="parameter.changeTime" type="datetime"  format="yyyy-MM-dd HH:mm:ss"
                          value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间">
          </el-date-picker>
          <span style="font-weight: bold;margin-left: 16px;">更换人:</span>
          <el-input v-model="parameter.changePeople" placeholder="请输入内容"
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
          <span style="font-weight: bold;margin-left: 16px;">备注:</span>
          <el-input v-model="parameter.remark" placeholder="请输入内容"
                    style="width: 222px;margin-left: 10px;width: 193px;" ></el-input>
          <el-button size="mini" type="primary" icon="el-icon-check" @click="submitData" style="margin-left: 10px;">提交</el-button>
          <el-button size="mini" type="primary" icon="el-icon-refresh" @click="resetData" style="margin-left: 10px;">重置</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>
    <hr style="border: 5px solid #409EFF;"/>
    <el-tabs type="border-card" style="margin-top:0px;">
      <div class="block" style="margin-top:-10px;">
        <span >更换日期:</span>
        <el-date-picker style="margin-left: 10px;width: 392px;"
          v-model="changeTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
        <span style=" margin-left: 10px;">风口号:</span>
        <el-select v-model="selectParam.tuyereNumber" filterable clearable   placeholder="请选择" style="margin-left: 10px;width: 193px;">
          <el-option
            v-for="dict in dict.type.blastFurnace_tuyereNumber" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
        <span style="margin-left: 10px;">厂家:</span>
        <el-input v-model="selectParam.manufacturer" placeholder="请输入内容" clearable
                  style="margin-left: 10px;width: 193px;" ></el-input>
        <el-button size="mini" type="primary" icon="el-icon-search" style="margin-left: 34px;" @click="handleQuery">搜索</el-button>
        <el-button size="mini" type="primary" icon="el-icon-delete" style="margin-left: 34px;" @click="handleDelete">删除</el-button>
        <el-button size="mini" type="primary" icon="el-icon-document-checked" style="margin-left: 34px;" @click="handleUpdate">保存</el-button>
      </div>
      <el-tab-pane>
        <span slot="label"><i class="el-icon-s-order"></i> 风口信息查询</span>
        <div style="margin-top: 7px;">
          <vxe-table
            border
            ref="tableRef"
            :edit-config="editConfig"
            @edit-closed="editClosedEvent"
            :checkbox-config="{checkField: 'isChecked', indeterminateField: 'isIndeterminate'}"
            @checkbox-change="handleCheckboxChange"
            @checkbox-all="selectAllChangeEvent"
            :data="tableData">
            <vxe-column type="checkbox" width="60" fixed="left"></vxe-column>
            <vxe-column field="tuyereNumber" title="风口号" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="diameter" title="直径" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="length" title="长度" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="area" title="面积" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="manufacturer" title="厂家" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="startTime" title="开始时间" :edit-render="{name: 'input'}"></vxe-column>
            <vxe-column field="endTime" title="结束时间"></vxe-column>
            <vxe-column field="serviceLife" title="使用寿命/天"></vxe-column>
            <vxe-column field="changePeople" title="更换人"></vxe-column>
            <vxe-column field="remark" title="备注" :edit-render="{name: 'input'}"></vxe-column>
          </vxe-table>
          <vxe-pager
            :current-page.sync="mainTableConfig.pageConfig.pageNum"
            :page-size.sync="mainTableConfig.pageConfig.pageSize"
            :total="mainTableConfig.pageConfig.total"
            @page-change="pageChange">
          </vxe-pager>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import {
    tuyereInsert,
    queryList,
    tuyereEdit,
    tuyereDelete,
  } from "@/api/report/preview/blastFurnace/tuyere";
  import dayjs from "dayjs";
  import { VxeUI } from 'vxe-table'
  export default {
    name: "tuyere",
    dicts: ['blastFurnace_tuyereNumber'],
    data() {
      return {
        editConfig : {
          trigger: 'click',
          mode: 'cell'
        },
        mainTableConfig: {
          tableData: [],
          selectVO: '',
          pageConfig: {
            pageNum: 1, // 页码
            pageSize: 10, // 每页显示条目个数
            total: 0, // 总数
            background: true, // 是否展示分页器背景色
            pageSizes: [10, 20, 50, 100]// 分页器分页待选项
          }
        },
        tableData:[],
        // 更换日期 插件
        changeTimeArr:[],
        // tanble查询
        selectParam:{
          // 风口号
          tuyereNumber:'',
          // 厂家
          manufacturer:'',
          changeTimeStart:'',
          changeTimeEnd:'',
          pageNum: 1,
          pageSize: 10,
          blastFurnaceNumber:'',
        },
        parameter:{
          // 风口号
          tuyereNumber:'',
          // 直径
          diameter:'',
          // 长度
          length:'',
          // 面积
          area:'',
          // 厂家
          manufacturer:'',
          // 更换时间
          changeTime:'',
          // 更换人
          changePeople:'',
          // 备注
          remark:'',
          // 高炉号
          blastFurnaceNumber:'',
        },
        // 提交按钮标记
        submitDataFlag:false,
        // 接收编辑的数据
        editDataList:[],
        // 多选框数组
        checkboxArr:[],
        // 保存数据
        updateDataArr:[],
      }
    },
    created(){
      this.changeTimeArr.push(dayjs(new Date()).add(-1, "year"));
      this.changeTimeArr.push(dayjs(new Date()));
      this.queryLists();

    },
    methods:{
    /*  获取高炉号*/
      getBlastFurnaceNumber() {
        return this.$route.query.blastFurnaceNumber;
      },
      /* 提交按钮 */
      submitData(){
        this.parameter.blastFurnaceNumber = this.getBlastFurnaceNumber();
        tuyereInsert(this.parameter).then(response=>{
          this.$modal.msgSuccess("提交成功");
          this.submitDataFlag = true;
          this.queryLists();
          this.clear();
        });
      },

      /*搜索按钮*/
      handleQuery(){
        if (this.changeTimeArr != null && this.changeTimeArr.length == 2) {
          this.selectParam.changeTimeStart = dayjs(this.changeTimeArr[0]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
          this.selectParam.changeTimeEnd = dayjs(this.changeTimeArr[1]).format(
            "YYYY-MM-DD HH:mm:ss"
          );
        }else{
          this.selectParam.changeTimeStart = null;
          this.selectParam.changeTimeEnd = null;
        }
        this.queryLists();
      },

      queryLists(){
        if(this.submitDataFlag == true){
          this.changeTimeArr = [];
          this.changeTimeArr.push(dayjs(new Date()).add(-1, "year"));
          this.changeTimeArr.push(dayjs(new Date()));
          if (this.changeTimeArr.length == 2) {
            this.selectParam.changeTimeStart = dayjs(this.changeTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.changeTimeEnd = dayjs(this.changeTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }else{
            this.selectParam.changeTimeStart = null;
            this.selectParam.changeTimeEnd = null;
          }
        }else{
          if (this.changeTimeArr != null && this.changeTimeArr.length == 2) {
            this.selectParam.changeTimeStart = dayjs(this.changeTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.changeTimeEnd = dayjs(this.changeTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }else{
            this.selectParam.changeTimeStart = null;
            this.selectParam.changeTimeEnd = null;
          }
        }
        this.selectParam.blastFurnaceNumber = this.getBlastFurnaceNumber();
        queryList(this.selectParam).then(response=>{
          this.tableData = response.rows
          this.mainTableConfig.pageConfig.total = response.total
        });
        this.submitDataFlag = false
      },
      pageChange ({ pageSize, currentPage }) {
        this.mainTableConfig.pageConfig.pageNum = currentPage
        this.mainTableConfig.pageConfig.pageSize = pageSize
        this.selectParam.pageNum=this.mainTableConfig.pageConfig.pageNum
        this.selectParam.pageSize=this.mainTableConfig.pageConfig.pageSize
        this.queryLists()
      },


      /*关闭编辑框触发 失去焦点 进行保存数据*/
      editClosedEvent(row, column) {
        this.updateDataArr.push(row.row)
      },

      /* 重置按钮 */
      resetData(){
        this.clear();
      },

    /*  清空提交表单数据*/
      clear(){
        this.parameter={
            tuyereNumber:'',
            diameter:'',
            length:'',
            area:'',
            manufacturer:'',
            changeTime:'',
            changePeople:'',
            remark:'',
        }
      },

      /* 多选框全部选中*/
      selectAllChangeEvent ({ checked }) {
        if(checked == true){
          const $table = this.$refs.tableRef
          if ($table) {
            const records = $table.getCheckboxRecords()
            this.checkboxArr = records
            console.log(checked ? '所有勾选事件' : '所有取消事件', records)
          }
        }else{
          this.checkboxArr = []
        }

      },
      /* 删除 */
      handleDelete(){
        console.log("this.checkboxArr1111:",this.checkboxArr)
        if(this.checkboxArr != []){
          tuyereDelete(this.checkboxArr).then(response=>{
            this.queryLists()
            this.$modal.msgSuccess("删除成功");
          })
        }

      },
      /* 保存 */
      handleUpdate(){
        if(this.updateDataArr !=[]){
          tuyereEdit(this.updateDataArr).then(response=>{
            this.$modal.msgSuccess("编辑成功");
          });
        }
      },
      /*多选框触发事件*/
      handleCheckboxChange({records, rowIndex, row, checked}) {

        if(checked  == true){
          console.log("records:",records)
          this.checkboxArr = records
          console.log("this.checkboxArr:",this.checkboxArr)
        }else{
          const $table = this.$refs.tableRef
          if ($table) {
            $table.clearCheckboxRow()
          }
          this.checkboxArr = []
        }
      },

      // 更换时间 获取本地时间
      changeTimeOn() {
        this.parameter.changeTime = this.filterYMDhMS(new Date())
      },
      /* 中国标准时间转化为年月日时分秒*/
      filterYMDhMS(time) {
        console.log('time:', time)
        var date = new Date(time)
        var y = date.getFullYear()
        var m = date.getMonth() + 1
        m = m < 10 ? '0' + m : m
        var d = date.getDate()
        d = d < 10 ? '0' + d : d
        var h = date.getHours()
        h = h < 10 ? '0' + h : h
        var minute = date.getMinutes()
        minute = minute < 10 ? '0' + minute : minute
        var s = date.getSeconds()
        s = s < 10 ? '0' + s : s
        return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + s
        // return String(y) + String(m) + String(d) + '-' + String(h) + String(minute)
      },

    },
  }
</script>

<style lang="scss" scoped>
  .mes_table_second{
    margin-top: 5px;
  }
  .mes_table{
    margin-top: -10px;
  }
</style>
