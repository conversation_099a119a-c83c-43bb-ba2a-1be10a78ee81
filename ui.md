### 点位配置
*  el-form size="small"
*  如果没有 el-form 包裹，可以直接修改 操作组件的 size="small"   

###  表格高度
*  :height="dltableHeight"
* queryParams.pageSize=30
 mounted() {
    this.$nextTick(() => {
      /*mes_new_table 到顶部的高炉 */
      let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
     /*mes_new_table 屏幕高度-mes_new_table到顶部的高炉-50（分页控件） */
      this.dltableHeight = document.body.clientHeight - topValue - 50;
    })
  },

 ### 界面间距 
* 四周：10像素（padding、margin）
* 控件行：margin-bottom：8px;
 * 外层间距： <div class="app-container" style="padding:10px;">

 ### pagination 分页框
 