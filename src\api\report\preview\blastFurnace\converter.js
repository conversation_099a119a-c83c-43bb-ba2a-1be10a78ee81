import request from '@/utils/request'

// 查询操作日志记录列表
export function listIronWeightAndPackAgeWeight(query) {
  return request({
    url: '/api/blastFurnace/iron/listIronWeightAndPackAgeWeight',
    method: 'get',
    params: query
  })
}


// // 高炉转炉数据 编辑
export function ironWeightInputEdit(data) {
  return request({
    url: '/api/blastFurnace/iron/ironWeightInputEdit',
    method: 'put',
    data: data
  })
}

//  计算预测温度
export function calculatedWenDuMethod(data) {
  return request({
    url: '/api/blastFurnace/iron/calculatedWenDuMethod',
    method: 'post',
    data: data
  })
}


// 计算包的权重
export function calculatedBackageaWeight(data) {
  return request({
    url: '/api/blastFurnace/iron/calculatedBackageaWeight',
    method: 'post',
    data: data
  })
}





