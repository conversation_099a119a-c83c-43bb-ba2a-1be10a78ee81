import request from '@/utils/request'

/**烧结产量数据总览 */
export function cpesScreenDay(query) {
    return request({
        url: '/public/cpesScreen/Day',
        method: 'get',
        params: query
    });
}
export function cpesScreenMonth(query) {
    return request({
        url: '/public/cpesScreen/Month',
        method: 'get',
        params: query
    });
}
/** 烧结化学成分合格率 */
export function cpesScreenComponent(query) {
    return request({
        url: '/public/cpesScreen/Component',
        method: 'get',
        params: query
    });
}
/** 烧结矿成品合格率 */
export function cpesScreenPassRate(query) {
    return request({
        url: '/public/cpesScreen/PassRate',
        method: 'get',
        params: query
    });
}
/** 烧结物料消耗 */
export function cpesScreenMaterial(query) {
    return request({
        url: '/public/cpesScreen/Material',
        method: 'get',
        params: query
    });
}
/** 点火温度、
终点温度、
煤气流量、
主管废气温度、
主管废气压力、
混合料温度、
混合料水分 */
export function cpesScreenMetrics(query) {
    return request({
        url: '/public/cpesScreen/Metrics',
        method: 'get',
        params: query
    });
}