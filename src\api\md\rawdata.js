import request from '@/utils/request'



// 查询报表单元列表
export function savetRepClassResultAndInout(data) {
  return request({
    url: '/api/rep/classresult/savetRepClassResultAndInout',
    method: 'post',
    data: data
  })
}
// 显示result表最新早中夜三班数据
export function zhanShiDataList(wordDate) {
  return request({
    url: '/api/rep/classresult/zhanShiDataList/'+ wordDate,
    method: 'get'
  })
}


// // 查询报表单元列表
// export function listUnit(query, selectVO) {
//   return request({
//     url: '/api/md/reportunit/list',
//     method: 'get',
//     params: query,
//     selectVO: selectVO
//   })
// }
//
// // 查询报表单元详细
// export function getUnit(reportUnitId) {
//   return request({
//     url: '/api/md/reportunit/' + reportUnitId,
//     method: 'get'
//   })
// }
//
// // 新增报表单元
// export function addUnit(data) {
//   return request({
//     url: '/api/md/reportunit',
//     method: 'post',
//     data: data
//   })
// }
//
// // 修改报表单元
// export function updateUnit(data) {
//   return request({
//     url: '/api/md/reportunit',
//     method: 'put',
//     data: data
//   })
// }
//
// // 删除报表单元
// export function delUnit(reportUnitId) {
//   return request({
//     url: '/api/md/reportunit/' + reportUnitId,
//     method: 'delete'
//   })
// }
//
// // 修改加工中心
// export function saveOrUpdate(data) {
//   return request({
//     url: "/api/md/reportunit/saveOrUpdate",
//     method: "post",
//     data: data,
//   });
// }
//
// // 查询报表单元详细
// export function getEditInfo(reportUnitId) {
//   return request({
//     url: '/api/md/reportunit/getEditInfo/' + reportUnitId,
//     method: 'get'
//   })
// }
//
// export function getbyReportUnitID(reportUnitId) {
//   return request({
//     url: '/api/md/reportunit/getbyReportUnitID/' + reportUnitId,
//     method: 'get'
//   })
// }
//
// // 获取报表页签类型
// export function getReportTabType(prodCenterCode) {
//   return request({
//     url: '/api/md/reportunit/getReportTabType/' + prodCenterCode,
//     method: 'get',
//   })
// }
//
//
// export function getByReportCode(reportCode) {
//   return request({
//     url: '/api/md/reportunit/getByReportCode/' + reportCode,
//     method: 'get'
//   })
// }
//
// export function queryProdCenterCodeOperSouce(prodCenterCode, operSource) {
//   return request({
//     url: '/api/md/reportunit/queryProdCenterCodeOperSouce/' + prodCenterCode + '/' + operSource,
//     method: 'get'
//   })
// }
//
// // 同步单元报表单元
// export function syncUnitData(reportUnitId) {
//   return request({
//     url: '/api/md/reportunit/syncUnitData/' + reportUnitId,
//     method: 'get'
//   })
// }
//
//
// export function queryByprodCenterCode(data) {
//   return request({
//     url: "/api/md/reportunit/queryByprodCenterCode",
//     method: "post",
//     data: data,
//   });
// }
