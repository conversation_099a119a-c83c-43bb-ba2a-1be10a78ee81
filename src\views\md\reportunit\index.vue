<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="单元编码" prop="reportUnitCode">
        <el-input v-model="queryParams.reportUnitCode" placeholder="请输入单元编码" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="单元名称" prop="reportUnitName">
        <el-input v-model="queryParams.reportUnitName" placeholder="请输入单元名称" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
                   @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                   @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>

    </el-row>
    <el-row>
      <el-col :span="6">
        <div class="prodTable">
          <el-table class="prodTable" row-key="prodCenterId" highlight-current-row :height="productcenterHeight"
                    :data="productcenterList" :border="true"
                    @current-change="handleCurrentChange"
                    :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column min-width="150" fixed="left" label="编码" align="center" prop="prodCenterCode"/>
            <el-table-column min-width="150" fixed="left" label="名称" align="center" prop="prodCenterName"/>
          </el-table>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="mes_new_table" style="margin-left: 10px">
          <dlTable refName="dlTable" :height="mainHeight" :stripe="true" :border="true" :columns="columns"
                   :pageConfig="pageConfig" :tableData="unitList" :basicConfig="basicConfig" @handleOrder="getList"
                   @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
                   @page-current-change="numChange"
          >
          </dlTable>
        </div>
      </el-col>
    </el-row>


    <!-- 添加或修改报表单元对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="加工中心" prop="prodCenterid">
          <treeselect v-model="form.prodCenterid" :options="editProductCenterList"
                      :show-count="true" placeholder="请选择加工中心"
          />
        </el-form-item>
        <el-form-item label="单元编码" prop="reportUnitCode">
          <el-input v-model="form.reportUnitCode" placeholder="请输入单元编码"/>
        </el-form-item>

        <el-form-item label="单元名称" prop="reportUnitName">
          <el-input v-model="form.reportUnitName" placeholder="请输入单元名称"/>
        </el-form-item>

        <el-form-item label="包含内容" prop="operSource">
          <el-select multiple clearable v-model="form.operSource" placeholder="请选择包含内容" style="width: 100%">
            <el-option v-for="dict in dict.type.OPER_SOURCE" :key="dict.value" :label="dict.label"
                       :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="请选择包含内容" style="width: 100%">
            <el-option v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.label"
                       :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUnit,
  getUnit,
  delUnit,
  addUnit,
  updateUnit,
  saveOrUpdate,
  getEditInfo,
  syncUnitData
} from '@/api/md/reportunit'

import { treeselect, queryByCode, listProductcenter } from '@/api/md/productcenter'

import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Unit',
  dicts: ['OPER_SOURCE', 'SUMMARY_TYPE', 'record_status'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报表单元表格数据
      unitList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      //加工中心菜单
      editProductCenterList: [],
      currentProdCenter: null,
      productcenterList: [],
      productcenterHeight: 600,
      mainHeight: 600,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        prodCenterCode: [
          { required: true, message: '加工中心不能为空', trigger: 'blur' }
        ],
        reportUnitCode: [
          { required: true, message: '单元编码不能为空', trigger: 'blur' }
        ],
        reportUnitName: [
          { required: true, message: '单元名称不能为空', trigger: 'blur' }
        ],
        operSource: [
          { required: true, message: '包含内容不能为空', trigger: 'change' }
        ],
        summaryType: [
          { required: true, message: '汇总方式不能为空', trigger: 'change' }
        ]
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: true // 表头排序是否保存在localStorage中
      },
      columns: [
        {
          label: '加工中心编码', // 表头描述
          fieldIndex: 'prodCenterCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true,// 是否显示筛选icon 和排序 icon
          fixed:true
        },
        {
          label: '加工中心名称', // 表头描述
          fieldIndex: 'prodCenterName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true,// 是否显示筛选icon 和排序 icon
          fixed:true
        },
        {
          label: '单元编码', // 表头描述
          fieldIndex: 'reportUnitCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true,// 是否显示筛选icon 和排序 icon
          fixed:true
        },
        {
          label: '单元名称', // 表头描述
          fieldIndex: 'reportUnitName', // 表格显示内容绑定值
          width: 240,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true,// 是否显示筛选icon 和排序 icon
          fixed:true
        },
        {
          label: '包含内容', // 表头描述
          fieldIndex: 'operSource', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '汇总方式', // 表头描述
          fieldIndex: 'summaryType', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '报表页签', // 表头描述
          fieldIndex: 'type', // 表格显示内容绑定值
          width: 180,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '状态', // 表头描述
          fieldIndex: 'status', // 表格显示内容绑定值
          width: 80,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 100,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建者', // 表头描述
          fieldIndex: 'createBy', // 表格显示内容绑定值
          width: 100,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {

          label: '创建时间', // 表头描述
          fieldIndex: 'createTime', // 表格显示内容绑定值
          width: 150,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新者', // 表头描述
          fieldIndex: 'updateBy', // 表格显示内容绑定值
          width: 100,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        },
        {
          label: '更新时间', // 表头描述
          fieldIndex: 'updateTime', // 表格显示内容绑定值
          width: 150,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true // 是否显示筛选icon 和排序 icon
        }
      ],
      selectVO: ''
    }
  },
  created() {
    var prodQueryparm = { status: '生效' }
    listProductcenter(prodQueryparm).then(response => {
      this.productcenterList = this.handleTree(response.data, 'prodCenterId')
    })
    this.getList(null);
  },
  methods: {
    /** 查询报表单元列表 */
    getList(selectVO) {
      if (selectVO) {
        this.selectVO = selectVO
      }
      this.queryList()
    },
    queryList() {
      if (this.currentProdCenter != null && this.currentProdCenter !== '') {
        this.queryParams.prodCenterCode = this.currentProdCenter.prodCenterCode;
      }
      listUnit(this.queryParams, this.selectVO).then((response) => {
        this.unitList = response.rows
        this.pageConfig.total = response.total
        this.loading = false
      })
    },
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize
      this.queryParams.pageSize = pageSize
      this.selectVO = selectVO
      this.getList(selectVO)
    },
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum
      this.queryParams.pageNum = pageNum
      this.selectVO = selectVO
      this.getList(selectVO)
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        reportUnitId: null,
        prodCenterid: null,
        reportUnitCode: null,
        reportUnitName: null,
        operSource: null,
        summaryType: null,
        remark: null,
        menuPerms: null,
        status: '生效'
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.reportUnitId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      treeselect().then((response) => {

        this.editProductCenterList = response.data
        this.open = true
        this.title = '添加报表单元'

      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const reportUnitId = row.reportUnitId || this.ids
      treeselect().then((response) => {
        this.editProductCenterList = response.data
        getEditInfo(reportUnitId).then((response) => {
          const obj = response.data
          obj.operSource = obj.operSource.split(',')
          this.form = obj
          this.open = true
          this.title = '修改报表单元'
        })
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form))
          if (obj.operSource != null) {
            obj.operSource = obj.operSource.join()
          }

          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess('保存成功')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reportUnitIds = row.reportUnitId || this.ids
      this.$modal
        .confirm('确认删除选中的报表单元？')
        .then(function() {
          return delUnit(reportUnitIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        '/api/md/reportunit/export',
        {
          ...this.queryParams
        },
        `unit_${new Date().getTime()}.xlsx`
      )
    },
    handleCurrentChange(selection) {
      this.currentProdCenter = selection
      this.getList(null)
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('prodTable')[0].getBoundingClientRect().top
      this.productcenterHeight = document.body.clientHeight - topValue - 10

      topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top
      this.mainHeight = document.body.clientHeight - topValue - 40

    })
  }
}
</script>
