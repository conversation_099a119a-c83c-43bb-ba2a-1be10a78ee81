<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="daterange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出
        </el-button>
      </el-col>
    </el-row>

    <div class="tableInfo">
      <vxe-grid
        ref="tableMainRef"
        :column-config="{resizable: true}"
        v-bind="mainTable.gridOptions"
        :height="300"
      >
      </vxe-grid>
    </div>

    <div id="mainChars" style="margin-top: 10px;"
         :style="{ width: '100%',  height: `${tableHeight - 350}px` }"
    ></div>


  </div>
</template>

<script>
import * as echarts from 'echarts'
import {
  queryForSumTable
} from '@/api/cpes/evaluate'
import dayjs from 'dayjs'
import BWorkClassSelect from '@/components/BWorkClassSelect/index.vue'
import BWorkShiftSelect from '@/components/BWorkShiftSelect/index.vue'
import XEUtils from 'xe-utils'

export default {
  name: 'productionIntegration',
  components: { BWorkShiftSelect, BWorkClassSelect },
  data() {
    return {
      dateRange: [],
      tableHeight: 300,
      mainTable: {
        selectedRadioRow: null,
        loading: true,
        single: true,
        multiple: true,
        selectId: [],
        gridOptions: {
          border: true,
          stripe: true,
          loading: false,
          height: 300,
          columnConfig: {
            resizable: true
          },
          rowConfig: {
            isHover: true
          },
          radioConfig: {
            labelField: 'name',
            trigger: 'row'
          },
          columns: [
            { type: 'checkbox', width: 60 }
          ],
          data: []
        }
      },
      showSearch: true,

      // 查询参数
      queryParams: {
        operType: null,
        prodCenterCode: null,
        workModeId: null
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).subtract(1, 'day').startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))

    this.queryList()
  },
  methods: {
    queryList() {
      this.queryParams.prodCenterCode = this.getProdCenterCode()

      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD 00:00:00'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD 00:00:00'
        )
      }
      this.queryParams.workModeId = 9
      this.mainTable.tableData = []
      this.mainTable.gridOptions.columns = []

      queryForSumTable(this.queryParams).then(response => {
        this.mainTable.gridOptions.columns.push({ type: 'radio', field: 'radio', width: 'auto', fixed: 'left' })
        for (let i = 0; i < response.data.col.length; i++) {
          var item = response.data.col[i]
          this.mainTable.gridOptions.columns.push({
            field: item.key,
            title: item.value,
            width: 'auto',
            headerAlign: 'center'
          })
        }
        this.mainTable.gridOptions.data = response.data.rec
        this.refreshChars()
      })

    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    },

    /** 导出按钮操作 */
    handleExport() {
      const $table = this.$refs.tableMainRef
      if ($table) {
        $table.exportData({
          type: 'csv',
          filename: '烧结生产综合', // 文件名
          sheetName: 'Sheet1' // sheet名称
        })
      }
    },
    refreshChars() {

      var chartDom = document.getElementById('mainChars')
      var myChart = echarts.init(chartDom)

      var option = {
        title: {
          text: '日统计曲线图'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['产量', '固耗', '转鼓', '返矿', '一级品']
        },
        grid: {
          left: '3%',
          right: '3%',
          bottom: '0%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: []
        },
        yAxis: [
          {
            type: 'value',
            name: '产量/固消耗/返矿',
            position: 'left',
            alignTicks: true,
            axisLabel: {
              formatter: '{value}'
            }
          },
          {
            type: 'value',
            name: '转鼓/一级品',
            position: 'right',
            alignTicks: true,
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],

        series: []
      }
      option.xAxis.data = []
      option.series = []
      this.mainTable.gridOptions.data.forEach(item => {
        option.xAxis.data.push(item.WorkDate)
      })

      {
        var itemList = []
        for (var i = 0; i < option.xAxis.data.length; i++) {
          var date = option.xAxis.data[i]

          var filteredData = this.mainTable.gridOptions.data.filter(item => {
            return item.WorkDate === date
          })
          if (filteredData.length > 0) {
            itemList.push(filteredData[0].OUTPUT_DAY)
          }
        }
        option.series.push({
          name: '产量',
          type: 'line',
          yAxisIndex: 0,
          data: itemList
        })
      }

      {
        var itemList = []
        for (var i = 0; i < option.xAxis.data.length; i++) {
          var date = option.xAxis.data[i]

          var filteredData = this.mainTable.gridOptions.data.filter(item => {
            return item.WorkDate === date
          })
          if (filteredData.length > 0) {
            itemList.push(filteredData[0].RETURN_ORE_DAY)
          }
        }
        option.series.push({
          name: '返矿',
          type: 'line',
          yAxisIndex: 0,
          data: itemList
        })
      }
      {
        var itemList = []
        for (var i = 0; i < option.xAxis.data.length; i++) {
          var date = option.xAxis.data[i]

          var filteredData = this.mainTable.gridOptions.data.filter(item => {
            return item.WorkDate === date
          })
          if (filteredData.length > 0) {
            itemList.push(filteredData[0].SOLID_FUEL_DAY)
          }
        }
        option.series.push({
          name: '固耗',
          type: 'line',
          yAxisIndex: 0,
          data: itemList
        })
      }
      {
        var itemList = []
        for (var i = 0; i < option.xAxis.data.length; i++) {
          var date = option.xAxis.data[i]

          var filteredData = this.mainTable.gridOptions.data.filter(item => {
            return item.WorkDate === date
          })
          if (filteredData.length > 0) {
            itemList.push(filteredData[0].TUMBLER_DAY)
          }
        }
        option.series.push({
          name: '转鼓',
          type: 'line',
          yAxisIndex: 1,
          data: itemList
        })
      }

      {
        var itemList = []
        for (var i = 0; i < option.xAxis.data.length; i++) {
          var date = option.xAxis.data[i]

          var filteredData = this.mainTable.gridOptions.data.filter(item => {
            return item.WorkDate === date
          })
          if (filteredData.length > 0) {
            itemList.push(filteredData[0].GRADE_A_DAY)
          }
        }
        option.series.push({
          name: '一级品',
          type: 'line',
          yAxisIndex: 1,
          data: itemList
        })
      }
      console.log(JSON.stringify(option))
      option && myChart.setOption(option)
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode
    }
  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
