<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">

      <el-form-item label="报表单元编码" prop="reportUnitCode">
        <BReportUnitSelect ref="reportUnitRef" v-model="queryParams.reportUnitCode"
                           :prod-center-code="queryParams.prodCenterCode"
        />
      </el-form-item>

      <el-form-item label="业务类型" prop="operType">
        <el-input v-model="queryParams.operType" placeholder="请输入业务类型" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="元素编码" prop="elementCode">
        <el-input v-model="queryParams.elementCode" placeholder="请输入元素编码" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="元素名称" prop="elementName">
        <el-input v-model="queryParams.elementName" placeholder="请输入元素名称" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            icon="el-icon-plus"
            plain
            size="mini"
            type="primary"
            @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="el-icon-edit"
            plain
            size="mini"
            type="success"
            @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="el-icon-delete"
            plain
            size="mini"
            type="danger"
            @click="handleDelete"
        >删除
        </el-button>

      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div class="prodTable">
          <BProdCenterTree ref="prodTree" :height="productcenterHeight" @currentChange="handleCurrentChange"/>
        </div>

      </el-col>
      <el-col :span="18">
        <vxe-table
            ref="tableMainRef"
            :data="mainTableConfig.tableData"
            :row-config="{isHover: true}"
            :column-config="{resizable: true}"
            :height="productcenterHeight-40"
            border
            @radio-change="handleRadioChange"
            header-align="center"
            stripe
            style="margin-left: 10px"
        >
          <vxe-column align="center" fixed="left" type="radio" width="60"></vxe-column>
          <vxe-column field="stanadId" title="标准ID" width="auto"></vxe-column>
          <vxe-column field="prodCenterCode" title="加工中心编码" width="auto"></vxe-column>
          <vxe-column field="prodCenterName" title="加工中心名称" width="auto"></vxe-column>
          <vxe-column field="reportUnitCode" title="报表单元编码" width="auto"></vxe-column>
          <vxe-column field="reportUnitName" title="报表单元名称" width="auto"></vxe-column>
          <vxe-column field="operType" title="业务类型" width="auto"></vxe-column>
          <vxe-column field="beginTime" title="开始时间" width="auto"></vxe-column>
          <vxe-column field="endTime" title="结束时间" width="auto"></vxe-column>
          <vxe-column field="elementCode" title="元素编码" width="auto"></vxe-column>
          <vxe-column field="elementName" title="元素名称" width="auto"></vxe-column>
          <vxe-column field="upperLimitValue" title="上限值" width="auto"></vxe-column>
          <vxe-column field="lowerLimitValue" title="下限值" width="auto"></vxe-column>
          <vxe-column field="remark" title="备注" width="auto"></vxe-column>
          <vxe-column field="status" title="状态" width="auto"></vxe-column>
          <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
          <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
          <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
          <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>
        </vxe-table>
        <vxe-pager
            :current-page.sync="mainTableConfig.pageConfig.pageNum"
            :page-size.sync="mainTableConfig.pageConfig.pageSize"
            :total="mainTableConfig.pageConfig.total"
            @page-change="pageChange"
        >
        </vxe-pager>

      </el-col>
    </el-row>


    <!-- 添加或修改汇总配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="900px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">

        <el-row>
          <el-col :span="12">
            <el-form-item label="加工中心" prop="prodCenterCode">
              <BProdCenterTreeSelect @currentChange="editPordCenterChange" ref="prodCenterRef"
                                     v-model="form.prodCenterCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表单元" prop="reportUnitCode">
              <BReportUnitSelect ref="reportUnitRef" :prod-center-code="form.prodCenterCode"
                                 v-model="form.reportUnitCode"
              />
            </el-form-item>
          </el-col>

        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="业务类型" prop="operType">
              <el-input v-model="form.operType" placeholder="请输入 业务类型"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="beginTime">
              <el-date-picker
                  v-model="form.beginTime"
                  type="datetime"
                  style="width: 100%;"
                  placeholder="选择 开始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="元素编码" prop="elementCode">
              <el-input v-model="form.elementCode" placeholder="请输入 元素编码"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="元素名称" prop="elementName">
              <el-input v-model="form.elementName" placeholder="请输入 元素名称"/>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="上限值" prop="upperLimitValue">
              <el-input v-model="form.upperLimitValue" placeholder="请输入 上限值"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下限值" prop="lowerLimitValue">
              <el-input v-model="form.lowerLimitValue" placeholder="请输入 下限值"/>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入 备注"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择包含内容" style="width: 100%;">
                <el-option v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {
  queryForManger, saveOrUpdate, getStanad, delStanad
} from '@/api/md/internalstanad'
import dayjs from 'dayjs'

import BCollPointDialog from '@/components/BCollPointDialog/index.vue'
import BProdCenterTree from '@/components/BProdCenterTree/index.vue'
import BProdCenterTreeSelect from '@/components/BProdCenterTreeSelect/index.vue'
import BReportUnitSelect from '@/components/BReportUnitSelect/index.vue'
import BWorkModeSelect from '@/components/BWorkModeSelect/index.vue'

export default {
  name: 'SummaryMaster',
  components: { BReportUnitSelect, BProdCenterTreeSelect, BProdCenterTree, BCollPointDialog, BWorkModeSelect },
  dicts: ['record_status'],
  data() {
    return {

      // 遮罩层
      loading: true,

      // 选中数组
      currentId: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 汇总配置表格数据
      configList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        reportUnitCode: null,
        operType: null,
        elementCode: null,
        elementName: null
      },
      // 表单参数
      form: {
        detailList: []
      },
      // 表单校验
      rules: {
        prodCenterCode: [
          {
            required: true, message: '加工中心 不能为空', trigger: 'blur'
          }
        ],
        reportUnitCode: [
          {
            required: true, message: '报表单元 不能为空', trigger: 'blur'
          }
        ],
        operType: [
          {
            required: true, message: '业务类型 不能为空', trigger: 'blur'
          }
        ],
        beginTime: [
          {
            required: true, message: '业务类型 不能为空', trigger: 'blur'
          }
        ],
        elementCode: [
          {
            required: true, message: '元素编码 不能为空', trigger: 'blur'
          }
        ],
        elementName: [
          {
            required: true, message: '元素名称 不能为空', trigger: 'blur'
          }
        ],
        status: [
          {
            required: true, message: '状态 不能为空', trigger: 'blur'
          }
        ]
      },
      productcenterHeight: 500,
      currentProdCenter: null,
      mainTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      }

    }
  },
  created() {
    this.handleQuery()
  }
  ,
  methods: {
    pageChange({ pageSize, currentPage }) {
      this.mainTableConfig.pageConfig.pageNum = currentPage
      this.mainTableConfig.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTableConfig.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTableConfig.pageConfig.pageSize
      this.queryList()
    },

    /** 查询汇总配置列表 */
    getList(selectVO) {
      this.loading = true
      if (selectVO) {
        this.selectVO = selectVO
      }
      this.queryList()
    }
    ,
    queryList() {
      this.mainTableConfig.tableData = []
      this.currentId = null
      if (this.currentProdCenter != null) {
        this.queryParams.prodCenterCode = this.currentProdCenter.prodCenterCode
      }
      queryForManger(this.queryParams).then(response => {
        this.mainTableConfig.tableData = response.rows
        this.mainTableConfig.pageConfig.total = response.total
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    }
    ,
    // 表单重置
    reset() {
      this.form = {
        stanadId: null,
        prodCenterCode: null,
        reportUnitCode: null,
        operType: null,
        beginTime: null,
        endTime: null,
        elementCode: null,
        elementName: null,
        upperLimitValue: null,
        lowerLimitValue: null,
        remark: null,
        status: '生效'
      }
      this.resetForm('form')
    }
    ,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    }

    ,
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.beginTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
      this.open = true
      this.title = '添加内控标准'
    }
    ,
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRow = this.currentId
      if (selectedRow == null) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }

      getStanad(selectedRow).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改内控标准'
      })
    }
    ,
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          var reqPar=JSON.parse(JSON.stringify(this.form))
          reqPar.beginTime = dayjs(this.form.beginTime).format('YYYY-MM-DD HH:mm:ss')
          saveOrUpdate(reqPar).then(response => {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.open = false
            this.getList(null)
          }).catch(error => {

          })
        }
      })
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRow = this.currentId
      if (selectedRow == null) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }

      this.$modal
          .confirm('是否确认删除？')
          .then(function() {
            return delStanad(selectedRow)
          })
          .then(() => {
            this.getList()
            this.$modal.msgSuccess('删除成功')
          })
          .catch(() => {
          })
    }
    ,
    handleCurrentChange(selection) {
      this.currentProdCenter = selection
      this.form.detailList = selection.prodCenterCode
      this.queryParams.prodCenterCode = selection.prodCenterCode
      this.getList(null)
    }
    ,
    editPordCenterChange(val) {
      this.$refs.reportUnitRef.clearSelect()
    },
    handleRadioChange({ row }) {
      this.currentId = row.stanadId
    }
  }
  ,

  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('prodTable')[0].getBoundingClientRect().top
      this.productcenterHeight = document.body.clientHeight - topValue - 10

    })
  }
}

</script>
