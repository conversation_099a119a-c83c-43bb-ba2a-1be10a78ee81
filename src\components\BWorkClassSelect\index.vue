<template>
  <el-select v-model="selectValue" clearable style="width: 100%;">
    <el-option v-for="dict in workModeIdList" :key="dict.workClassName" :label="dict.workClassName"
               :value="dict.workClassName"
    ></el-option>
  </el-select>
</template>
<script>
import { queryWorkClass } from '@/api/system/work.js'

export default {
  name: 'BWorkClassSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    modelName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      workModeIdList: []
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    modelName: {
      immediate: true,
      deep: true,
      handler(val) {
        queryWorkClass(val).then((response2) => {
          this.workModeIdList = response2.data
        })
      }
    }
  }
}
</script>
