import request from '@/utils/request'

// 查询汇总配置列表
export function listConfig(query,selectVO) {
  return request({
    url: '/api/summary/config/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询汇总配置详细
export function getConfig(configId) {
  return request({
    url: '/api/summary/config/' + configId,
    method: 'get'
  })
}

// 新增汇总配置
export function addConfig(data) {
  return request({
    url: '/api/summary/config',
    method: 'post',
    data: data
  })
}

// 修改汇总配置
export function updateConfig(data) {
  return request({
    url: '/api/summary/config',
    method: 'put',
    data: data
  })
}

// 删除汇总配置
export function delConfig(configId) {
  return request({
    url: '/api/summary/config/' + configId,
    method: 'delete'
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/summary/config/saveOrUpdate',
    method: 'post',
    data: data
  })
}

export function analysisMethod() {
  return request({
    url: '/api/summary/config/analysisMethod',
    method: 'get'
  })
}

export function detailType() {
  return request({
    url: '/api/summary/config/detailType',
    method: 'get'
  })
}

export function resultDetailConfig() {
  return request({
    url: '/api/summary/config/resultDetailConfig',
    method: 'get'
  })
}


export function createConfigCode(data) {
  return request({
    url: '/api/summary/config/createConfigCode',
    method: 'post',
    data: data
  })
}

export function syncStoreRule(data) {
  return request({
    url: '/api/summary/config/syncStoreRule',
    method: 'get',
    data: data
  })
}

export function getDetialGroup(configCode) {
  return request({
    url: '/api/summary/config/getDetialGroup/' + configCode,
    method: 'get'
  })
}
