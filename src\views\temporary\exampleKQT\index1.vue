<template>
  <!--  <div class="main" @wheel.prevent="handleWheel" :style="{ transform: `scale(${scale})`, transformOrigin: 'center center' }"> -->
  <div class="main">
    <div class="header-bar">一原料仓储管理</div>
    <div class="bottom-barBg">
      <div class="leftBarBg"></div>
      <div class="rightBarBg" :style="{ width: rightPartVisible ? '310px' : '0' }"></div>
    </div>
    <div class="box">
      <div class="showRightPart" @click="showRightPart()" :style="{ right: rightPartVisible ? '320px' : '0' }"></div>
      <div class="left">
        <div class="coordinate_axis1">
          <div v-for="i in 15" :key="i">
            <!--  <div :style="{ left: (i - 1) * (100 / 14) + '%' }" class="tick"></div>
            <div :style="{ left: (i - 1) * (100 / 14) + '%' }" class="tick-label">
              <span>{{ (15 - i) * 50 }}</span>
            </div> -->
          </div>
        </div>
      </div>
      <div class="right" :style="{ width: rightPartVisible ? '310px' : '0' }">
        <div class="tab-bar">库存信息</div>
        <!--  <div class="tab-bar">出入库履历</div>
        <div class="tab-bar">作业指令</div> -->
      </div>
    </div>
    <GirdLayoutTest0 :layoutData='layoutData5' mainTitle='5' :rightPartVisible="rightPartVisible" style="margin: 22px 0 0 0;" />
    <div class="boxDJ">
      <div class="lineDJ">
      </div>
      <div class="lineDJ1" :style="{ width: rightPartVisible ? '310px' : '0' }"></div>
    </div>
    <!--   <div class="container" ref="container">
      <div ref="div1" class="bigMon" :style="{ marginLeft: width + '%'}"></div>
      <div ref="div2" :style="{ marginLeft: width1 + '%',backgroundColor: name == '取料' ? '#00ed00' : '#19acff'}" class="materialIn"></div>
      <svg class="connector">
        <line :x1="line.x1" :y1="line.y1" :x2="line.x2" :y2="line.y2" stroke="black" stroke-width="2" />
      </svg>
    </div> -->
    <GirdLayoutTest1 :layoutData='layoutData4' :rightPartVisible="rightPartVisible" mainTitle="4" />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1" :style="{ width: rightPartVisible ? '320px' : '0' }"></div>
    </div>
    <GirdLayoutTest2 :layoutData='layoutData3' :rightPartVisible="rightPartVisible" mainTitle='3' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1" :style="{ width: rightPartVisible ? '320px' : '0' }"></div>
    </div>
    <GirdLayoutTest3 :layoutData='layoutData2' :rightPartVisible="rightPartVisible" mainTitle='2' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1" :style="{ width: rightPartVisible ? '320px' : '0' }"></div>
    </div>
    <GirdLayoutTest4 :layoutData='layoutData1' :rightPartVisible="rightPartVisible" mainTitle='1' />
    <div class="boxDJ">
      <div class="lineDJ"></div>
      <div class="lineDJ1" :style="{ width: rightPartVisible ? '320px' : '0' }"></div>
    </div>
  </div>
</template>
    
  <script>
import GirdLayoutTest0 from '@/components/GirdLayoutTest/index1'
import GirdLayoutTest1 from '@/components/GirdLayoutTest/index1'
import GirdLayoutTest2 from '@/components/GirdLayoutTest/index1'
import GirdLayoutTest3 from '@/components/GirdLayoutTest/index1'
import GirdLayoutTest4 from '@/components/GirdLayoutTest/index1'
import { stockmapInfo } from "@/api/wms/stockmap";

export default {
  components: { GirdLayoutTest0, GirdLayoutTest1, GirdLayoutTest2, GirdLayoutTest3, GirdLayoutTest4 },
  data() {
    return {
      loading: false,
      layoutData1: [],
      layoutData2: [],
      layoutData3: [],
      layoutData4: [],
      layoutData5: [],
      width: 23,
      width1: 28,
      line: {
        x1: 0,
        y1: 0,
        x2: 0,
        y2: 0
      },
      name: '取料',
      scale: 1,           // 初始缩放比例
      scaleStep: 0.05,    // 每次缩放的步长
      minScale: 0.5,      // 最小缩放比例
      maxScale: 1,       // 最大缩放比例
      rightPartVisible: true,
    }
  },
  mounted() {
    this.updateLinePosition();
    // 监听窗口变化，重新计算连线位置
    window.addEventListener('resize', this.updateLinePosition);
    this.getList();
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateLinePosition);
  },
  methods: {
    /** 查询料条数据 */
    getList() {
      this.loading = true;
      stockmapInfo().then(response => {
        if (response.code === 200) {
          var list = this.addSequenceNumber(response.data);
          var data = this.groupedPlans(list);
          setTimeout(() => {
            this.layoutData1 = data[1].map(item => {
              return {
                x: item.start_position,
                y: 0,
                w: item.stack_length,
                h: 2,
                minH: 2,
                maxH: 2,
                i: item.sequence,
                stack_name: item.stack_name,
                end_position: item.end_position,
                STOCK_WEIGHT: item.STOCK_WEIGHT
              }
            });
            this.layoutData2 = data[2].map(item => {
              return {
                x: item.start_position,
                y: 0,
                w: item.stack_length,
                h: 2,
                minH: 2,
                maxH: 2,
                i: item.sequence,
                stack_name: item.stack_name,
                end_position: item.end_position,
                STOCK_WEIGHT: item.STOCK_WEIGHT
              }
            });
            this.layoutData3 = data[3].map(item => {
              return {
                x: item.start_position,
                y: 0,
                w: item.stack_length,
                h: 2,
                minH: 2,
                maxH: 2,
                i: item.sequence,
                stack_name: item.stack_name,
                end_position: item.end_position,
                STOCK_WEIGHT: item.STOCK_WEIGHT
              }
            });
            this.layoutData4 = data[4].map(item => {
              return {
                x: item.start_position,
                y: 0,
                w: item.stack_length,
                h: 2,
                minH: 2,
                maxH: 2,
                i: item.sequence,
                stack_name: item.stack_name,
                end_position: item.end_position,
                STOCK_WEIGHT: item.STOCK_WEIGHT
              }
            });
            this.layoutData5 = data[5].map(item => {
              return {
                x: item.start_position,
                y: 0,
                w: item.stack_length,
                h: 2,
                minH: 2,
                maxH: 2,
                i: item.sequence,
                stack_name: item.stack_name,
                end_position: item.end_position,
                STOCK_WEIGHT: item.STOCK_WEIGHT
              }
            });
          }, 500)
        } else {
          this.$message.error(response.msg || "获取数据失败");
        }
        this.loading = false;
        setTimeout(() => {
          this.getList();
        }, 40 * 1000);
      }).catch(error => {
        console.error("获取数据出错:", error);
      });
    },
    // 给数组添加顺序号的方法
    addSequenceNumber(arr) {
      if (!arr || arr.length === 0) return [];
      return arr.map((item, index) => {
        // 顺序号从1开始
        return {
          ...item,
          sequence: index + 1
        };
      });
    },
    groupedPlans(list) {
      const groups = {};
      list.forEach(item => {
        if (!groups[item.cross_region]) {
          groups[item.cross_region] = [];
        }
        groups[item.cross_region].push(item);
      });
      return groups;
    },
    showRightPart() {
      this.rightPartVisible = !this.rightPartVisible
    },
    handleWheel(event) {
      // 判断滚轮方向（正数为向上滚动，负数为向下滚动）
      const delta = event.deltaY > 0 ? -1 : 1;
      // 计算新的缩放值
      const newScale = this.scale + delta * this.scaleStep;
      // 限制缩放范围
      if (newScale >= this.minScale && newScale <= this.maxScale) {
        this.scale = newScale;
      }
    },
    updateLinePosition() {
      // 等待DOM更新完成
      this.$nextTick(() => {
        const node1 = this.$refs.div1;
        const node2 = this.$refs.div2;
        if (node1 && node2) {
          const rect1 = this.$refs.div1.getBoundingClientRect();
          const rect2 = this.$refs.div2.getBoundingClientRect();
          const containerRect = this.$refs.container.getBoundingClientRect();
          // 计算两个节点的中心点相对于容器的位置
          this.line = {
            x1: rect1.left + rect1.width / 2 - containerRect.left,
            y1: rect1.top + rect1.height / 2 - containerRect.top,
            x2: rect2.left + rect2.width / 2 - containerRect.left,
            y2: rect2.top + rect2.height / 2 - containerRect.top
          };
        }
      });
    }
  },
  watch: {
  }
}
  </script>
  <style lang="scss" scoped>
.main {
  margin: 0;
  padding: 0 0 12% 0;
  background: url("~@/assets/images/imageKQT/bg.png") center/100% 100% no-repeat;
  .header-bar {
    position: absolute;
    height: 6vh;
    width: 100%;
    text-align: center;
    font-size: 1.6rem;
    font-weight: bolder;
    letter-spacing: 10px;
    background: url("~@/assets/images/imageKQT/headerBg.png") center/100% 100%
      no-repeat;
  }
  .bottom-barBg {
    display: flex;
    position: absolute;
    width: 100%;
    height: 91.8%;
    margin: 10vh 0 0 0;
    .leftBarBg {
      flex: 1;
      background: url("~@/assets/images/imageKQT/2.png") 55% 100%/102% 100%
        no-repeat;
    }
    .rightBarBg {
      width: 310px;
      background: url("~@/assets/images/imageKQT/3.png") 80% 100%/120% 100%
        no-repeat;
    }
  }
  .box {
    display: flex;
    height: 99px;
    position: sticky;
    top: 5vh;
    .showRightPart {
      position: absolute;
      width: 20px;
      height: 50%;
      cursor: pointer;
    }
    .left {
      flex: 1;
      padding: 0 4vw 0 1.3vw;
      margin: 0 0 0 10px;
      background: url("~@/assets/images/imageKQT/11.png") center/100% 100%
        no-repeat;
      .coordinate_axis1 {
        direction: rtl;
        display: flex;
        /*  height: 20px; */
        position: relative;
        .tick {
          position: absolute;
          top: 0;
          width: 1px;
          height: 8px;
          background-color: #ccc;
        }
        .tick-label {
          position: absolute;
          top: 0px;
          color: #666;
          transform: translateX(-50%) translateY(-136%);
          text-align: center;
        }
      }
    }

    .right {
      flex-shrink: 0;
      flex-grow: 0;
      margin: 0 0 0 10px;
      height: 37px;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-content: space-around;
      justify-content: center;
      align-items: center;
      .tab-bar {
        line-height: 30px;
        width: 30%;
        height: 100%;
        color: #233263;
        font-size: 14px;
        font-weight: bolder;
        text-align: center;
        cursor: pointer;
        background: url("~@/assets/images/imageKQT/5.png") center/100% 100%
          no-repeat;
        &:hover,
        &.active {
          color: #fff;
          background: url("~@/assets/images/imageKQT/4.png") center/100% 100%
            no-repeat;
        }
      }
    }
  }
  .boxDJ {
    display: flex;
    height: 7.6%;
    position: absolute;
    width: 100%;
    .lineDJ {
      flex: 1;
      height: 18px;
      background: url("~@/assets/images/imageKQT/22.png") center/100% 100%
        no-repeat;
      margin: 30px 10px 20px 38px;
    }
    .lineDJ1 {
      flex-shrink: 0;
      flex-grow: 0;
      background: transparent;
    }
  }
  .container {
    width: 79.8%;
    height: 108px;
    position: absolute;
    margin: -20px 0 0 30px;
    overflow: hidden;
    .bigMon {
      width: 38px;
      height: 36px;
      border-radius: 80px;
      position: absolute;
      margin: 2.6% 0 0 0;
      border: 2px solid #000000;
      background: rgb(253 33 33 / 100%);
      z-index: 2;
    }
    .materialIn {
      width: 18px;
      height: 18px;
      border-radius: 80px;
      margin: 1% 0 0 0;
      position: absolute;
      z-index: 2;
      border: 2px solid #000000;
    }
    .connector {
      position: absolute;
      width: 1400px;
      height: 80px;
      pointer-events: none;
      z-index: 1;
    }
  }
}
</style>