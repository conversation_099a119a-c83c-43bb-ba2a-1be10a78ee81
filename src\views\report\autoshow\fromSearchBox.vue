<template>
	<!-- <div class="app-container" style="position: relative;height: 70px;">
		<v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vfr">
		</v-form-render>
		<el-button type="primary" @click="search" v-show="true"
			style="position:absolute;left: 90%;top:22%">查询</el-button>
	</div> -->
	<div style="position: relative;height: 45px;">
		<v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vfr">
		</v-form-render>
		<el-button-group style="position:absolute;left: 90%;top:20%">
		<el-button type="primary" @click="search" v-show="true" size="small">查询</el-button>
		<el-button type="success" @click="handleExport" v-show="true" size="small">导出</el-button>
		</el-button-group>
	</div>
</template>
<script>
import { getformjson } from '@/api/formtemplate/searchbox';

export default {
	data() {
		return {
			formJson: {},
			formData: {},
			optionData: {},
			pageKey: '',
			defFormData: {},
		}
	},
	created() {
		this.pageKey = this.$route.query.pageKey;
		this.defFormData = this.$route.query;
		if (this.$route.query.pageKey == null) {
			// this.pageKey = 'CPES004013';
		}
		getformjson(this.pageKey).then((response) => {
			this.formjson = response;
			this.$refs.vfr.setFormJson(this.formjson);
			this.$nextTick(function () {
				//this.$refs.vfr.setFieldValue("ID-teamnotes-PROD_CENTER_CODE","CPES001");
				// 设置搜索框默认值
				// Object.keys(this.defFormData).forEach(k => {
				// 	console.log('key:', k, '   value:', this.defFormData[k]);
				// 	this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
				// });
			});
			console.log("searchbox加载完成 created");
			return "123321";
		}).then(response=>{
			console.log("then以后:",response)
			Object.keys(this.defFormData).forEach(k => {
					console.log('key:', k, '   value:', this.defFormData[k]);
					this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
				});
			this.search();
		});
	},
	mounted() {

	},
	methods: {
		search() {
			this.$parent.$parent.searchBoxAction();
			// this.$refs.vfr.getFormData().then(formData => {
			// 	//校验成功
			// 	this.$message.success(JSON.stringify(formData));
			// }).catch(error => {
			// 	// 校验失败
			// 	this.$message.error(error);
			// });
		},
		handleExport() {
			this.$parent.$parent.searchBoxAction();
			this.$parent.$parent.handleExport();
		}
	}
}
</script>
