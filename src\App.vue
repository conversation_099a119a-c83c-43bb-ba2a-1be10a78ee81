<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'App',
  metaInfo() {
    return {
      title: this.$store.state.settings.dynamicTitle && this.$store.state.settings.title,
      titleTemplate: title => {
        return title ? `${title} - ${process.env.VUE_APP_TITLE}` : process.env.VUE_APP_TITLE
      },
      meta: [
        { name: "Cache-Control", content: "no-cache, no-store, must-revalidate" },
      ],
    }
  }
}
</script>
<style>
 .mes_new_table .cell {
  padding: 0px !important;
  line-height: 2em !important;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
}

 .mes_new_table .el-table th.el-table cell>.celll {
  line-height: 20px;
  padding: 6px 0;
}

 .mes_new_table .el-pagination {
  margin-top: 3px !important;
  margin-bottom: 3px !important;

}

 .mes_new_table thead tr th {
  height: 20px;
  padding: 0 !important;
}
 .mes_new_table tr {
  font-size: 14px;
  font-family: "SimSun", "宋体", serif;
}

 .mes_new_table .my-edit-table:before {
  height: 28px !important;
}
 .mes_new_table .el-table--mini .el-table__cell {
  padding: 0;
}
 /*为偶数行添加背景色 ( 不要放开 因为原生表格有重影)*/
/* .mes_new_table .el-table tbody tr:nth-child(even) {*/
/*  background-color: rgba(253, 245, 230, 0);*/
/*}*/

 /*奇数行添加背景色*/
 .mes_new_table .el-table tbody tr:nth-child(odd) {
  background-color: #ebf1f9;
}

 .mes_new_table .el-table__body tr.current-row>td {
  background: #68bef7 !important;
  color: #ffffff !important;
}
/deep/  .mes_new_table .has-gutter th {
  font-weight: normal;
}
</style>
