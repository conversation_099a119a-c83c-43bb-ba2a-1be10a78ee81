<template>
  <div class="app-container">
    <el-row><fromSearchBox ref="searchbox"></fromSearchBox></el-row>
    <el-row :gutter="10" class="mb8" v-show="flgedit">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col> -->
    </el-row>

    <div class="mes_new_table">
      <dlTable size="small" refName="dlTable" :height="660" :stripe="true" :border="true" :columns="columns" v-loading="loading"
        :pageConfig="pageConfig" :tableData="unitList" :basicConfig="basicConfig" @handleOrder="getList"
        @handleFilter="getList" @selection-change="handleSelectionChange" @size-change="sizeChange"
        @page-current-change="numChange">
      </dlTable>
    </div>


    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogForm" :title="title" :visible.sync="formopen" v-if="formopen" width="800px" @close='closeDialog' append-to-body>
      <fromBuilder ref="fromBuilder"></fromBuilder>
      <!-- <div v-show="false" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div> -->
    </el-dialog>

    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogPeiBi" :title="title" :visible.sync="peibiopen" v-if="peibiopen" fullscreen append-to-body>
      <mixratiochange :reportCode="unitCode" :propWorkDate="propWorkDate" :propResultGroup="propResultGroup" />
    </el-dialog>

    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogConsume" :title="title" :visible.sync="consumeopen" v-if="consumeopen" fullscreen append-to-body>
      <rawStockSonsume :propReportCode="unitCode" :propWorkDate="propWorkDate" :propProdCenterCode="prodCenterCode" :propWorkShiftName="propWorkShiftName" />
    </el-dialog>

    <!-- 添加或修改报表单元对话框 -->
    <el-dialog ref="dialogWork" :title="title" :visible.sync="workopen" width="800px" append-to-body>
      <!-- <fromBuilder ref="fromBuilder"></fromBuilder> -->
      <div>dialogWork</div>
    </el-dialog>

  </div>
</template>

<script>
import {
  listUnit,
  getUnit,
  delUnit,
  addUnit,
  updateUnit,
  saveOrUpdate,
  getEditInfo,
} from "@/api/md/reportunit";

import { treeselect, queryByCode } from "@/api/md/productcenter";
import { getChart, getcolsdy, gettabledatady } from "@/api/formtemplate/chart";

import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

import fromSearchBox from './fromSearchBox';
import fromBuilder from './fromBuilder';

// import mixratiochange from '../../rep/mixratiochange/index'
// import rawStockSonsume from '../../rep/rawStockSonsume/index'

// 暂时处理
import mixratiochange from '../../md/teamnotes/index'
import rawStockSonsume from '../../md/teamnotes/index'
import teamnotes from '../../md/teamnotes/index'

export default {
  name: "Unit",
  dicts: ["OPER_SOURCE", "SUMMARY_TYPE", "record_status", "form_template_details_status_config"],
  components: { Treeselect, fromSearchBox, fromBuilder, mixratiochange, teamnotes, rawStockSonsume },
  data() {
    return {
      // 页面编码
      pageKey: '',
      // 报表单元code
      unitCode:'',
      // 加工中心code
      prodCenterCode:'',
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 选择所有的数据
      selectionData: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 报表单元表格数据
      unitList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      formopen: false,
      peibiopen: false,
      workopen: false,
      consumeopen: false,
      flgedit: false,
      // 表单页类型
      formtype: '1',
      //加工中心菜单
      editProductCenterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 20, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: false, // 是否展示表格配置按钮
        headerSortSaveType: true // 表头排序是否保存在localStorage中
      },
      columns: [],
      selectVO: '',
      propWorkDate: '',
      propResultGroup: '',
      propWorkShiftName: '',
    };
  },
  created() {

  },
  mounted() {
    // this.$refs.dialogForm.rendered = true;
    this.pageKey = this.$route.query.pageKey;
    this.unitCode = this.$route.query.unitCode;
    console.log(this.pageKey);
    this.getcols();
    // this.handleQuery();
  },
  methods: {
    /** 查询报表单元列表 */
    getList() {
      console.log("加载表格数据");
      this.getcols();
      this.getTableData();
    },
    getcols() {
      // this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        getcolsdy(this.pageKey,this.unitCode,JSON.stringify(data)).then((resp) => {
          // 241018数据加载方式更改为进入页面直接查询,查询时有获取列的逻辑,所以此处注释
          // this.columns = resp.columns;
          this.formtype = resp.formtype;
          this.prodCenterCode = resp.prodcode;
          // this.loading = false;
          this.flgedit = resp.allowedit === '1';
        })
      });
    },
    getTableData() {
      this.searchBoxAction();
    },
    sizeChange(pageSize) {
      //console.log(pageSize);
      this.pageConfig.pageSize = pageSize;
      this.queryParams.pageSize = pageSize;
      this.getList();
    },
    numChange(pageNum) {
      console.log("numChange");
      this.pageConfig.pageNum = pageNum;
      this.queryParams.pageNum = pageNum;
      this.getList();
    },
    getProdCenterCode() {
      return this.$route.query.prodCenterCode;
    },
    // 取消按钮
    cancel() {
      this.open = false;
      if (this.formtype === '2') {
        this.peibiopen = false;
      } else if (this.formtype === '3') {
        this.workopen = false;
      } else if (this.formtype === '4') {
        this.consumeopen = false;
      } else {
        this.formopen = false;
      }
      // this.reset();
    },
    // 表单重置
    reset() {
      // this.$refs.fromBuilder.clearFormData();
    },
    // 初始化表单布局
    initForm() {
      // if (this.selectionData.length === 1) {
      //   this.$refs.fromBuilder.filterParam = { "con1": this.selectionData[0].MASTERID };
      // }
      // this.$refs.fromBuilder.pageKey = this.pageKey;
      // this.$refs.fromBuilder.defFormData = this.$route.query;
      // this.$refs.fromBuilder.renderForm(this.pageKey);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      //console.log("selectionselection:", JSON.stringify(selection))
      this.selectionData = selection;
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
      handleAdd() {
      // this.$refs.fromBuilder.saveflag = false;
      console.log(this.formtype);
      if (this.formtype === '2') {
        this.propResultGroup = '';
        this.propWorkDate = '';
        this.peibiopen = true;
      } else if (this.formtype === '3') {
        this.workopen = true;
      } else if (this.formtype === '4') {
        this.propWorkDate = '';
        this.propWorkShiftName = '';
        this.consumeopen = true;
      } else {
        console.log("其他");
        this.formopen = true;
      }
      // 配比

      // 交接班

      // 表单
      this.open = true;
      this.title = "增加数据";
      this.initForm()
      this.reset();
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      console.log('update');
      // this.$refs.fromBuilder.saveflag = false;
      if (this.formtype === '2') {
        this.propResultGroup = this.selectionData[0].RESULT_GROUP;
        this.propWorkDate = this.selectionData[0].work_date;
        this.peibiopen = true;
      } else if (this.formtype === '3') {
        this.workopen = true;
      } else if (this.formtype === '4') {
        console.log("this.selectionData[0]",this.selectionData[0]);
        this.propWorkDate = this.selectionData[0].work_date;
        this.propWorkShiftName = this.selectionData[0].work_clsass;
        this.consumeopen = true;
      } else {
        this.formopen = true;
      }
      try {
        this.reset();
      } catch (error) {

      }
      this.open = true;
      this.title = "修改数据";
      this.initForm()
      // console.log("loadData");
      // this.$refs.fromBuilder.loadData();
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          const obj = JSON.parse(JSON.stringify(this.form));
          if (obj.operSource != null) {
            obj.operSource = obj.operSource.join();
          }
          saveOrUpdate(obj).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const reportUnitIds = row.reportUnitId || this.ids;
      this.$modal
        .confirm("确认删除选中的报表单元？")
        .then(function () {
          return delUnit(reportUnitIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        this.download(
          "/formtemp/common/dltable/exportdy",
          {
            "pageKey": this.pageKey,
            "unitCode": this.unitCode,
            "filter": JSON.stringify(data),
          },
          `${this.unitCode}_${new Date().getTime()}.xlsx`
        ).then(ret=>{
          this.loading = false;
        }).error(ret=>{
          this.loading = true;
        });
      })
    },
    searchBoxAction() {
      this.loading = true;
      this.$refs.searchbox.$refs.vfr.getFormData().then(data => {
        //alert(JSON.stringify(data));
        let tbconfig = {
          pagenum : this.pageConfig.pageNum,
          pagesize : this.pageConfig.pageSize
        };
        //console.log(JSON.stringify(tbconfig));
        gettabledatady(this.pageKey, this.unitCode,JSON.stringify(data),JSON.stringify(tbconfig)).then((resp) => {
          this.columns = JSON.parse(resp.colinfo);
          this.unitList = JSON.parse(resp.tbdata);
          this.pageConfig.total = resp.total;
          this.loading = false;
        })
      })
    },
    //关闭弹框的事件
    closeDialog(){
      // saveflag
      if(this.$refs.fromBuilder.saveflag){
        this.searchBoxAction();
      }
      this.$refs.fromBuilder.saveflag = false;
      this.reset();
    },
  },
};
</script>
