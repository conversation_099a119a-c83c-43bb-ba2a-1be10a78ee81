import request from '@/utils/request'

/**
 * MPS生产计划管理API接口统一管理
 *
 * 包含以下页面：
 * 1. 年度生产计划
 * 2. 月生产计划
 * 3. 生产计划编制-新增、修改、查询、、删除
 * 4. 日产计划相关接口
 *
 * 使用方式：
 * import { addProductPlan, listProductPlan } from "@/api/feedingplan/mps"
 */


//生产计划编制页-表格查询
export function listProductPlanYear(query) {
  return request({
    url: '/api/productPlan/saveDataList',
    method: 'get',
    params: query
  })
}

// 年度生产计划列表查询
export function listYearPlan(query) {
  return request({
    url: '/api/productPlan/list',
    method: 'get',
    params: query
  })
}

// 查询月生产计划列表
export function listMonthPlan(query) {
  return request({
    url: '/mps/monthplan/list',
    method: 'get',
    params: query
  })
}

// 查询月生产计划详细
export function getMonthPlan(planId) {
  return request({
    url: '/mps/monthplan/' + planId,
    method: 'get'
  })
}

// 新增月生产计划
export function addMonthPlan(data) {
  return request({
    url: '/mps/monthplan',
    method: 'post',
    data: data
  })
}

// 修改月生产计划
export function updateMonthPlan(data) {
  return request({
    url: '/mps/monthplan',
    method: 'put',
    data: data
  })
}



// 生产计划编制-新增
export function addProductPlan(data) {
  return request({
    url: '/api/productPlan/add',
    method: 'post',
    data: data
  })
}

// 生产计划编制界面-列表查询
export function listProductPlan(query) {
  return request({
    url: '/api/productPlan/list',
    method: 'get',
    params: query
  })
}

// 查询生产计划详细（planCreat页面使用）
export function getProductPlan(planId) {
  return request({
    url: `/api/productPlan/${planId}`,
    method: 'get'
  })
}

// 修改生产计划（planCreat页面使用）
export function updateProductPlan(data) {
  return request({
    url: '/api/productPlan/edit',
    method: 'post',
    data: data
  })
}

// 删除生产计划（planCreat页面使用）
export function delProductPlan(data) {
  return request({
    url: `/api/productPlan/deleteData`,
    method: 'delete',
    data: data
  })
}



// 下发生产计划（planCreat页面使用）
export function issueProductPlan(data) {
  return request({
    url: '/api/productPlan/add',
    method: 'post',
    data: data
  })
}



