<template>
  <div class="echartsG_box">
    <div class="title-bar">
      <div class="title-text">烧结化学成分合格率</div>
      <div class="changeButton">
        <div class="buttonGroup" @click="intDate('早班','#4086ff')">
          <div class="buttonName">早班</div>
          <div class="buttonColor"></div>
        </div>
        <div class="buttonGroup" @click="intDate('中班','#95D475')">
          <div class="buttonName">中班</div>
          <div class="buttonColor" style="background-color: #95D475;"></div>
        </div>
        <div class="buttonGroup" @click="intDate('夜班','#FC703C')">
          <div class="buttonName">夜班</div>
          <div class="buttonColor" style="background-color: #FC703C;"></div>
        </div>
      </div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一烧结'}" @click="chagngeGl('一烧结')">一烧结</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二烧结'}" @click="chagngeGl('二烧结')">二烧结</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsBH" class="echartsGauge"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { cpesScreenComponent } from "@/api/analyse/sintering";
import { log } from 'vxe-pc-ui';

export default {
  name: 'EChartsBH',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      activeGL: '一烧结',
      chart: null,
      param: {
        prodCenterCode: 'CPES01'
      },
      dateZ: [],
      dateW: [],
      dateY: [],
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    chagngeGl(name) {
      if (name == '一烧结') {
        this.activeGL = name
        this.param.prodCenterCode = 'CPES01';
        this.initValue();
      }
      if (name == '二烧结') {
        this.activeGL = name
        this.param.prodCenterCode = 'CPES02';
        this.initValue();
      }
    },
    initValue() {
      cpesScreenComponent(this.param).then(res => {
        if (res.code === 200) {
          var data = res.data;
          var date1 = [];
          var date2 = [];
          var date3 = [];
          data.forEach(item => {
            const values = [
              item['转鼓-PassRate'],
              item['RLeave1Rate'],
              item['FeO-PassRate'],
              item['R-PassRate']
            ];
            switch (item.WorkClass) {
              case '夜':
                date1 = values;
                break;
              case '早':
                date2 = values;
                break;
              case '中':
                date3 = values;
                break;
            }
          });
          this.dateZ = date2;
          this.dateW = date3;
          this.dateY = date1;
          this.$nextTick(() => {
            if (this.chart) {
              this.chart.dispose();
            }
            const chartDom = this.$refs.echartsBH;
            this.initChart('#4086ff', chartDom, this.dateZ);
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    intDate(name, color) {
      this.$nextTick(() => {
        if (this.chart) {
          this.chart.dispose();
        }
        if (name == '早班') {
          const chartDom = this.$refs.echartsBH;
          this.initChart(color, chartDom, this.dateZ);
        }
        if (name == '中班') {
          const chartDom = this.$refs.echartsBH;
          this.initChart(color, chartDom, this.dateW);
        }
        if (name == '夜班') {
          const chartDom = this.$refs.echartsBH;
          this.initChart(color, chartDom, this.dateY);
        }
      })
    },
    initChart(color, container, date) {
      let that = this;
      this.chart = echarts.init(container);
      const option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let tooltipHtml = '';
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        grid: {
          left: "3%",
          right: "5%",
          bottom: "5%",
          top: "5%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          position: "bottom",
          interval: 10,
          scale: true,
          min: 0,
          max: 100,
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            fontSize: 11,
            color: "#4E5969",
            formatter: '{value}%'
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: { color: "#F2F2F5", width: 2, type: "solid" },
          },
        },
        yAxis: {
          type: "category",
          axisLine: {
            show: false,
          },
          axisLabel: {
            show: true,
            fontSize: 12,
            color: "#4E5969",
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          data: ['转鼓', 'SiO2', 'FeO', 'R2'],
        },
        series: [
          {
            name: '',
            type: "bar",
            stack: "total",
            barGap: "0%",
            barWidth: 10,
            label: {
              show: false,
            },
            itemStyle: {
              normal: {
                color: color,
                barBorderRadius: 20,
              },
            },
            data: date,
          }
        ],
      };
      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsG_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 8px 10px;
  justify-content: space-between;
}
.title-text {
  width: 160px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
}
.changeButton {
  width: 40%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
}
.buttonGroup {
  width: 30%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-evenly;
  align-content: center;
  align-items: center;
  cursor: pointer;
}
.buttonName {
  font-family: sourcehanRegular;
  color: black;
  font-size: 12px;
}
.buttonColor {
  width: 15px;
  height: 4px;
  background-color: #4086ff;
}
.echarts-container {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsGauge {
  width: 100%;
  height: 100%;
}
.buttonGLXL.el-button {
  border: none;
  background: #f2f3f5;
  color: #0852ff;
  height: 25px;
  width: 55px;
  padding: 0px;
  font-size: 12px;
  font-family: Arial, Helvetica, sans-serif;
}
</style>