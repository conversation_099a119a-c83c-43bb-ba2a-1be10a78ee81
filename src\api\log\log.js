import request from '@/utils/request'

// 查询操作日志记录列表
export function listLog(query) {
    return request({
        url: '/monitor/operlog/list',
        method: 'get',
        params: query
    })
}

// 查询操作日志记录详细
export function getLog(id) {
    return request({
        url: '/monitor/operlog/' + id,
        method: 'get'
    })
}

// 新增操作日志记录
export function addLog(data) {
    return request({
        url: '/monitor/operlog',
        method: 'post',
        data: data
    })
}

// 修改操作日志记录
export function updateLog(data) {
    return request({
        url: '/monitor/operlog',
        method: 'put',
        data: data
    })
}

// 删除操作日志记录
export function delLog(id) {
    return request({
        url: '/monitor/operlog/' + id,
        method: 'delete'
    })
}

//清空操作日志记录
export function clearLog() {
    return request({
        url: '/monitor/operlog/clearLog',
        method: 'post'
    })
}

// 导出操作日志记录
export function exportLog(query) {
    return request({
        url: '/monitor/operlog/export',
        method: 'get',
        params: query,
        responseType: 'blob' // 表明返回服务器返回的数据类型 很重要！！
    })
}