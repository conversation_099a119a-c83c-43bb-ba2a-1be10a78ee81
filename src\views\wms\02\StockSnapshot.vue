<template>
    <div style="margin: 10px;">
        <vxe-grid v-bind="gridOptions" style="margin: 10px;" @page-change="pageChangeEvent">
            <template #form>
                <vxe-form ref="formRef" v-bind="formOptions">
                    <template #action>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="searchEvent">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetEvent">重置</el-button>
                    </template>
                </vxe-form>
            </template>
            <template #toolbarButtons>
                <el-button type="primary" plain size="mini" @click="generateStockSnapshot">生成快照</el-button>
            </template>
        </vxe-grid>
    </div>
</template>
<script>
import {
    getStockSnapshotList,
    insertstockSnapshotData
} from "@/api/wms/stocksnapshot";
import { initMaterialList } from "@/api/md/material";

export default {
    name: 'StockSnapshot',
    data() {
        const materialRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
            },
            options: []
        }
        const materialsRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
                multiple: true,
            },
            options: []
        }
        return {
            gridOptions: {
                columns: [],
                data: [],

                height: 800,
                border: true,
                stripe: true,
                align: 'center',

                columnConfig: {
                    resizable: true
                },
                rowConfig: {
                    isHover: true,
                    isCurrent: true,
                },

                pagerConfig: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },

                editConfig: {
                    trigger: 'dblclick',
                    mode: 'cell',
                },

                toolbarConfig: {
                    custom: true,
                    zoom: true,
                    slots: { buttons: "toolbarButtons" },
                },
                customConfig: {
                    immediate: true
                },
            },
            formOptions: {
                data: {
                    workDate: '',
                    mateName: '',
                    mateCode: ''
                },
                items: [
                    {
                        field: 'workDate', title: '工作日期',
                        itemRender: { name: "VxeDatePicker", props: { type: "date" } }
                    },
                    { field: 'mateCode', title: '物料名称', itemRender: materialRender },
                    { slots: { default: 'action' } }
                ]
            },
            materialRender,
            materialsRender,
        }
    },

    mounted() {
        // 设置默认工作日期为昨天
        this.setYesterdayDate();
        this.initGridData();
        this.initData();
    },

    methods: {
      initData() {
          initMaterialList().then(response => {
              let data = response.data
              let list = []
              for (let i = 0; i < data.length; i++) {
                  list.push({
                      value: `${data[i].materialNumber}`,
                      label: `${data[i].shortName ? data[i].shortName : data[i].materialName}` + `(${data[i].materialNumber})`
                  })
              }
              this.materialRender.options = list
              this.materialsRender.options = list
          })
      },
        // 设置默认工作日期为昨天
        setYesterdayDate() {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);

            // 格式化为YYYY-MM-DD
            const year = yesterday.getFullYear();
            const month = String(yesterday.getMonth() + 1).padStart(2, '0');
            const day = String(yesterday.getDate()).padStart(2, '0');

            this.formOptions.data.workDate = `${year}-${month}-${day}`;
            console.log(this.formOptions.data.workDate)
        },

        initGridData() {
            this.gridOptions.columns = [
                { type: 'seq', width: 50 },
                { field: 'storehouseName', title: '仓库名称', },
                { field: 'crossRegion', title: '跨区', },
                { field: 'stackingPosition', title: '垛位', },
                { field: 'mateCode', title: '物料编码', },
                { field: 'mateName', title: '物料名称', },
                { field: 'stockWeight', title: '库存重量', },
                { field: 'consumeWeight', title: '供料出库重量', },
                // { field: 'consumeRemark', title: '消耗备注', },
                { field: 'purchaseCarCount', title: '采购车数', },
                { field: 'purchaseWeight', title: '采购重量', },
                { field: 'innerInCarCount', title: '调拨入库车数', },
                { field: 'innerInWeight', title: '调拨入库重量', },
                { field: 'innerOutCarCount', title: '调拨出库车数', },
                { field: 'innerOutWeight', title: '调拨出库重量', },
                // { field: 'purchaseRemark', title: '采购备注', },
            ]

            this.handlePageData()
        },

        async searchEvent() {
            this.gridOptions.pagerConfig.currentPage = 1;
            this.handlePageData()
        },

        resetEvent() {
            // 重置时重新设置为昨天的日期
            this.setYesterdayDate();
            this.$refs.formRef.reset();
            this.searchEvent();
        },

        handlePageData() {
            this.gridOptions.loading = true;
            this.formOptions.data.storehouseCode = this.getStoreHouseCode()
            getStockSnapshotList(this.formOptions.data).then((response) => {
                let data = response.data;
                const { pageSize, currentPage } = this.gridOptions.pagerConfig;
                this.gridOptions.pagerConfig.total = data.length;
                this.gridOptions.data = data.slice(
                    (currentPage - 1) * pageSize,
                    currentPage * pageSize
                );
                this.gridOptions.loading = false;
            });
        },

        pageChangeEvent({ pageSize, currentPage }) {
            this.gridOptions.pagerConfig.currentPage = currentPage;
            this.gridOptions.pagerConfig.pageSize = pageSize;
            this.handlePageData();
        },

        generateStockSnapshot() {
            this.$modal
                .confirm("是否生成快照？")
                .then(() => {
                    return insertstockSnapshotData(this.formOptions.data)
                })
                .then(() => {
                    this.handlePageData();
                    this.$modal.msgSuccess("生成快照成功");
                })
                .catch(() => {
                    return
                });
        },

        getProdCenterCode() {
            return this.$route.query.prodCenterCode
        },

        getStoreHouseCode() {
            return this.$route.query.storehouseCode
        },
    }
}
</script>
