<template>
  <div style="width: 100%; height: 100%; position: absolute; top: 0;left: 0;">
    <iframe :src="bingUrl" scrolling="auoto" style="width: 100%; height: 100%; position: absolute; top: 0;left: 0;">
    </iframe>
  </div>
</template>

<script>
    export default {
      name: "standingBook",
      props: ['url'],
      data() {
        return {
          bingUrl: '',
        }
      },
      created() {
        var usedURL;
        if (this.url == undefined) {
          usedURL = this.$route.query.url
        } else {
          usedURL = this.url;
        }
        this.bingUrl = usedURL;
      },
    }

</script>

