<template>
   <div class="app-container">
      <el-form :model="mangerQueryParams" ref="mangerForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="编码" prop="materialNumber">
            <el-input v-model="mangerQueryParams.materialNumber" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
         </el-form-item>
         <el-form-item label="名称" prop="materialName">
            <el-input v-model="mangerQueryParams.materialName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索 </el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置 </el-button>
         </el-form-item>
      </el-form>
      <el-row>
         <el-col :span="4" style="padding-right: 10px">
            <el-tree
               class="tree-border"
               :data="materialCategoryTree.materialCategoryList"
               ref="categoryTree"
               default-expand-all
               node-key="id"
               empty-text="正在加载"
               :props="defaultProps"
               @node-click="categoryTreeNodeClick"
            ></el-tree>
         </el-col>

         <el-col :span="20">
            <el-row :gutter="10" class="mb8">
               <el-col :span="1.5">
                  <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增 </el-button>
               </el-col>
               <el-col :span="1.5">
                  <el-button type="danger" plain icon="el-icon-delete" size="mini" @click="handleDelete">移除 </el-button>
               </el-col>
               <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <el-table v-loading="mangerTableConfig.loading" :data="mangerTableConfig.tableDataSouce" @selection-change="handleSelectionChange">
               <el-table-column type="selection" width="55" align="center" />
               <el-table-column label="分类" align="center" prop="tMdMaterialCategory.categoryName" />
               <el-table-column label="编码" align="center" prop="materialNumber" />
               <el-table-column label="名称" align="center" prop="materialName" />
               <el-table-column label="状态" align="center" prop="materialStatus">
                  <template slot-scope="scope">
                     <el-tag v-if="scope.row.materialStatus == '生效'" type="success">{{ scope.row.materialStatus }} </el-tag>
                     <el-tag v-if="scope.row.materialStatus == '失效'" type="danger">{{ scope.row.materialStatus }} </el-tag>
                  </template>
               </el-table-column>

               <el-table-column label="助记码" align="center" prop="helpCode" />
               <el-table-column label="别名" align="center" prop="aliasName" />
               <el-table-column label="简称" align="center" prop="shortName" />
               <el-table-column label="计量单位" align="center" prop="mdBaseUnit.unitName" />
               <el-table-column label="注册商标" align="center" prop="registeredMark" />
               <el-table-column label="批准文号" align="center" prop="warrantNumber" />
               <el-table-column label="净重" align="center" prop="grossWeight" />
               <el-table-column label="毛重" align="center" prop="netWeight" />
               <el-table-column label="重量单位" align="center" prop="mdWeightUnit.unitName" />
               <el-table-column label="ERP关联" align="center" prop="erpId" />
               <el-table-column label="规格型号" align="center" prop="specModel" />
               <el-table-column label="外文名称" align="center" prop="foreignName" />
               <el-table-column label="长度" align="center" prop="lengthValue" />
               <el-table-column label="长度单位" align="center" prop="mdLengthUnit.unitName" />
               <el-table-column label="宽度" align="center" prop="widthValue" />
               <el-table-column label="宽度单位" align="center" prop="mdWidthUnit.unitName" />
               <el-table-column label="高度" align="center" prop="heightValue" />
               <el-table-column label="高度单位" align="center" prop="mdHeightUnit.unitName" />
               <el-table-column label="体积" align="center" prop="volumeValue" />
               <el-table-column label="体积单位" align="center" prop="mdVolumnUnit.unitName" />
               <el-table-column label="备注" align="center" prop="remark" />
            </el-table>
            <pagination
               v-show="mangerTableConfig.rowCount > 0"
               :total="mangerTableConfig.rowCount"
               :page.sync="mangerQueryParams.pageNum"
               :limit.sync="mangerQueryParams.pageSize"
               @pagination="getList"
            />
         </el-col>
      </el-row>

      <!-- 添加或修改物料管理对话框 -->
      <el-dialog title="选择物料" :visible.sync="chooseMaterialDialogConfig.open" width="800px" append-to-body>
         <el-form :model="chooseQueryParams" ref="chooseForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
            <el-form-item label="编码" prop="materialNumber">
               <el-input v-model="chooseQueryParams.materialNumber" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="名称" prop="materialName">
               <el-input v-model="chooseQueryParams.materialName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
               <el-button type="primary" icon="el-icon-search" size="mini" @click="handleChooseQuery">搜索 </el-button>
               <el-button icon="el-icon-refresh" size="mini" @click="resetchooseQuery">重置 </el-button>
            </el-form-item>
         </el-form>
         <el-table height="450" v-loading="chooseTableConfig.loading" :data="chooseTableConfig.tableDataSouce" @selection-change="handleChooseSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="分类" align="center" prop="tMdMaterialCategory.categoryName" />
            <el-table-column label="编码" min-width="120px" align="center" prop="materialNumber" />
            <el-table-column label="名称" min-width="150px" align="center" prop="materialName" />
            <el-table-column label="状态" align="center" prop="materialStatus">
               <template slot-scope="scope">
                  <el-tag v-if="scope.row.materialStatus == '生效'" type="success">{{ scope.row.materialStatus }} </el-tag>
                  <el-tag v-if="scope.row.materialStatus == '失效'" type="danger">{{ scope.row.materialStatus }} </el-tag>
               </template>
            </el-table-column>
            <el-table-column label="别名" align="center" prop="aliasName" />
            <el-table-column label="简称" align="center" prop="shortName" />
            <el-table-column label="计量单位" align="center" prop="mdBaseUnit.unitName" />
            <el-table-column label="注册商标" align="center" prop="registeredMark" />
            <el-table-column label="批准文号" align="center" prop="warrantNumber" />
            <el-table-column label="净重" align="center" prop="grossWeight" />
            <el-table-column label="毛重" align="center" prop="netWeight" />
            <el-table-column label="重量单位" align="center" prop="mdWeightUnit.unitName" />
            <el-table-column label="规格型号" align="center" prop="specModel" />
            <el-table-column label="长度" align="center" prop="lengthValue" />
            <el-table-column label="长度单位" align="center" prop="mdLengthUnit.unitName" />
            <el-table-column label="宽度" align="center" prop="widthValue" />
            <el-table-column label="宽度单位" align="center" prop="mdWidthUnit.unitName" />
            <el-table-column label="高度" align="center" prop="heightValue" />
            <el-table-column label="高度单位" align="center" prop="mdHeightUnit.unitName" />
            <el-table-column label="体积" align="center" prop="volumeValue" />
            <el-table-column label="体积单位" align="center" prop="mdVolumnUnit.unitName" />
            <el-table-column label="备注" align="center" prop="remark" />
         </el-table>
         <pagination
            v-show="chooseTableConfig.rowCount > 0"
            :total="chooseTableConfig.rowCount"
            :page.sync="chooseQueryParams.pageNum"
            :limit.sync="chooseQueryParams.pageSize"
            @pagination="getChooseList"
         />
         <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
         </div>
      </el-dialog>
   </div>
</template>

<script>
import { queryFormManger, chooseMaterial } from "@/api/md/material"
import { treeselectByMaterialCategoryCode } from "@/api/md/materialCategory"
import Treeselect from "@riophae/vue-treeselect"
import { getToken } from "@/utils/auth"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"

export default {
   name: "Material",
   dicts: ["effective_or_not"],
   components: { Treeselect },
   data() {
      return {
         /**
          * 物料分类树
          */
         materialCategoryTree: {
            materialCategoryList: [],
            materialCategoryCurrent: {},
         },
         /**
          * 选择物料窗口配置
          */
         chooseMaterialDialogConfig: {
            open: false,
         },
         // 管理窗口搜索是否显示
         showSearch: true,
         /**
          * 物料管理表格配置
          */
         mangerTableConfig: {
            // 遮罩层
            loading: true,
            // 表格列表
            tableDataSouce: [],
            // 总条数
            rowCount: 0,
            // 选中数组
            tableSelectItems: [],
         },
         // 查询参数
         mangerQueryParams: {
            pageNum: 1,
            pageSize: 10,
            materialCategoryCode: null,
            materialNumber: null,
            materialName: null,
            prodCenterCode: null,
         },
         defaultProps: {
            children: "children",
            label: "label",
         },
         chooseQueryParams: {
            pageNum: 1,
            pageSize: 10,
            materialNumber: null,
            materialName: null,
            prodCenterCode: null,
         },
         /**
          * 物料管理表格配置
          */
         chooseTableConfig: {
            // 遮罩层
            loading: true,
            // 表格列表
            tableDataSouce: [],
            // 总条数
            rowCount: 0,
            // 选中数组
            tableSelectItems: [],
         },
      }
   },
   created() {
      this.getList()
      this.queryCategoryList()
   },
   methods: {
      getProductCode() {
         return this.$route.query.prodCenterCode
      },
      queryCategoryList() {
         treeselectByMaterialCategoryCode(this.getProductCode()).then(response => {
            const result = [{ id: 0, label: "(全部)", children: response.data }]
            this.materialCategoryTree.materialCategoryList = result
         })
      },
      /** 查询物料管理列表 */
      getList() {
         this.mangerTableConfig.loading = true
         if (this.materialCategoryTree.materialCategoryCurrent != null) {
            this.mangerQueryParams.materialCategoryID = this.materialCategoryTree.materialCategoryCurrent.id
         }
         this.mangerQueryParams.prodCenterCode = this.getProductCode()
         queryFormManger(this.mangerQueryParams).then(response => {
            this.mangerTableConfig.tableDataSouce = response.rows
            this.mangerTableConfig.rowCount = response.total
            this.mangerTableConfig.loading = false
         })
      },
      // 取消按钮
      cancel() {
         this.chooseMaterialDialogConfig.open = false
      },
      submitForm() {},
      /** 搜索按钮操作 */
      handleQuery() {
         this.mangerQueryParams.pageNum = 1
         this.getList()
      },
      /** 重置按钮操作 */
      resetQuery() {
         this.resetForm("mangerForm")
         this.handleQuery()
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
         this.mangerTableConfig.tableSelectItems = selection
      },
      /** 新增按钮操作 */
      handleAdd() {
         this.getChooseList()
         this.chooseMaterialDialogConfig.open = true
      },
      /** 删除按钮操作 */
      handleDelete(row) {
         // const materialIds = row.materialId || this.ids
         // this.$modal
         //    .confirm("是否确认删除物料？")
         //    .then(function () {
         //       return delMaterial(materialIds)
         //    })
         //    .then(() => {
         //       this.getList()
         //       this.$modal.msgSuccess("删除成功")
         //    })
         //    .catch(() => {})
      },
      categoryTreeNodeClick(data) {
         if (data.id != 0) {
            this.materialCategoryTree.materialCategoryCurrent = data
         } else {
            this.materialCategoryTree.materialCategoryCurrent = {}
         }
         this.getList()
      },
      getChooseList() {
         this.chooseQueryParams.prodCenterCode = this.getProductCode()
         chooseMaterial(this.chooseQueryParams).then(response => {
            this.chooseTableConfig.tableDataSouce = response.rows
            this.chooseTableConfig.rowCount = response.total
            this.chooseTableConfig.loading = false
         })
      },
      /** 搜索按钮操作 */
      handleChooseQuery() {
         this.chooseQueryParams.pageNum = 1
         this.getChooseList()
      },
      /** 重置按钮操作 */
      resetchooseQuery() {
         this.resetForm("chooseForm")
         this.handleChooseQuery()
      },
      // 多选框选中数据
      handleChooseSelectionChange(selection) {
         this.chooseTableConfig.tableSelectItems = selection
      },
   },
}
</script>
