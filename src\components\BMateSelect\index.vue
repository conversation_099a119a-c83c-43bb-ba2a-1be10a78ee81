<template>
  <el-select v-model="selectValue" clearable style="width: 100%;">
    <el-option v-for="item in materialListEdit" :key="item.materialNumber"
               :label="`${item.materialNumber}/${item.aliasName != null ? item.aliasName : item.materialName}${item.specModel == null ? '' : '/规格' + item.specModel}`"
               :value="item.materialNumber"
    >
      <span style="float: left">{{ item.materialName }}/{{ item.materialNumber }}</span>
      <span v-if="item.specModel != undefined && item.specModel != ''" style="float: left"> /规格：{{
          item.specModel
        }}</span>
      <span v-if="item.aliasName != undefined && item.aliasName != ''" style="float: left"> /别名：{{
          item.aliasName
        }}</span>
      <span v-if="item.tMdMaterialCategory!=null"
            style="float: right; color: #8492a6; font-size: 13px; padding-left: 10px;;"
      >{{
          item.tMdMaterialCategory.categoryName
        }}</span>
    </el-option>
  </el-select>
</template>
<script>
import { queryAllUsed } from '@/api/md/material'

export default {
  name: 'BMateSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data() {
    return {
      materialListEdit: []
    }
  },
  created() {

  },
  mounted() {
    queryAllUsed().then((response) => {
      this.materialListEdit = response.data
    })
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    prodCenterCode: {
      immediate: true,
      deep: true,
      handler(val) {
        queryAllUsed().then((response) => {
          this.materialListEdit = response.data
        })
      }
    }
  }
}
</script>
