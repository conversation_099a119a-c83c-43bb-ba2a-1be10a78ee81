<template>
  <div class="echartsL_box">
    <div class="title-bar">
      <div class="title-text">{{ title || '-' }}</div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉' || activeGL === '一烧结'}" @click="chagngeGl('1')">一{{ type }}</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉' || activeGL === '二烧结'}" @click="chagngeGl('2')">二{{ type }}</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsL" class="echartsL"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { cpesScreenMaterial } from "@/api/analyse/sintering";

export default {
  name: '<PERSON>hartsL',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  components: {
  },
  directives: {},
  data() {
    return {
      activeGL: this.type == '高炉' ? '一高炉' : '一烧结',
      chartL: null,
      param: {
        prodCenterCode: 'CPES01'
      },
      timer: null
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chartL) {
          this.chartL.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chartL) {
      this.chartL.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
    clearInterval(this.timer);
  },
  methods: {
    initValue() {
      /*       var data = {
              '2025-07-12': {
                '早': [
                  { "workDate": "2025-07-12", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-12", "workClass": "早", "targetNumber": 222.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-12", "workClass": "早", "targetNumber": 3954.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-12", "workClass": "早", "targetNumber": 25.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-12", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-12", "workClass": "午", "targetNumber": 222.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-12", "workClass": "午", "targetNumber": 222.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-12", "workClass": "午", "targetNumber": 222.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-12", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-12", "workClass": "夜", "targetNumber": 222.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-12", "workClass": "夜", "targetNumber": 3954.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-12", "workClass": "夜", "targetNumber": 25.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-13': {
                '早': [
                  { "workDate": "2025-07-13", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-13", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-13", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-13", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-13", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-13", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-13", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-13", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-13", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-13", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-13", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-13", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-14': {
                '早': [
                  { "workDate": "2025-07-14", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-14", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-14", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-14", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-14", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-14", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-14", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-14", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-14", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-14", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-14", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-14", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-15': {
                '早': [
                  { "workDate": "2025-07-15", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-15", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-15", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-15", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-15", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-15", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-15", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-15", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-15", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-15", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-15", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-15", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-16': {
                '早': [
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-16': {
                '早': [
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-16", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-17': {
                '早': [
                  { "workDate": "2025-07-17", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-17", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-17", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-17", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-17", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-17", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-17", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-17", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-17", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-17", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-17", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-17", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-18': {
                '早': [
                  { "workDate": "2025-07-18", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-18", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-18", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-18", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-18", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-18", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-18", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-18", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-18", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-18", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-18", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-18", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-19': {
                '早': [
                  { "workDate": "2025-07-19", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-19", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-19", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-19", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-19", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-19", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-19", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-19", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-19", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-19", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-19", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-19", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-20': {
                '早': [
                  { "workDate": "2025-07-20", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-20", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-20", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-20", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-20", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-20", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-20", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-20", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-20", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-20", "workClass": "夜", "targetNumber": 240.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-20", "workClass": "夜", "targetNumber": 3700.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-20", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-21': {
                '早': [
                  { "workDate": "2025-07-21", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-21", "workClass": "早", "targetNumber": 250.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-21", "workClass": "早", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-21", "workClass": "早", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-21", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-21", "workClass": "午", "targetNumber": 230.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-21", "workClass": "午", "targetNumber": 250.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-21", "workClass": "午", "targetNumber": 210.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-21", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-21", "workClass": "夜", "targetNumber": 300.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-21", "workClass": "夜", "targetNumber": 300.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-21", "workClass": "夜", "targetNumber": 28.000, "categoryName": "厂内循环料" }
                ]
              },
              '2025-07-22': {
                '早': [
                  { "workDate": "2025-07-22", "workClass": "早", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-22", "workClass": "早", "targetNumber": 260.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-22", "workClass": "早", "targetNumber": 3900.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-22", "workClass": "早", "targetNumber": 32.000, "categoryName": "厂内循环料" }
                ],
                '午': [
                  { "workDate": "2025-07-22", "workClass": "午", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-22", "workClass": "午", "targetNumber": 245.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-22", "workClass": "午", "targetNumber": 270.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-22", "workClass": "午", "targetNumber": 220.000, "categoryName": "厂内循环料" }
                ],
                '夜': [
                  { "workDate": "2025-07-22", "workClass": "夜", "targetNumber": 0.00000, "categoryName": "其他" },
                  { "workDate": "2025-07-22", "workClass": "夜", "targetNumber": 255.000, "categoryName": "烧结燃料" },
                  { "workDate": "2025-07-22", "workClass": "夜", "targetNumber": 3800.000, "categoryName": "烧结熔剂" },
                  { "workDate": "2025-07-22", "workClass": "夜", "targetNumber": 30.000, "categoryName": "厂内循环料" }
                ]
              }
            } */
      cpesScreenMaterial(this.param).then(res => {
        if (res.code === 200) {
          var rawData = res.data;
          // 1. 准备数据
          const colors = ['#FFC0CB', '#7B68EE', '#87CEEB', '#00FA9A'];
          const categories = ['其他', '烧结燃料', '烧结熔剂', '厂内循环料'];
          const dates = Object.keys(rawData).sort();
          const shifts = ['早', '中', '夜'];
          // 2. 构建x轴数据（每天三个班次）
          const xAxisData = [];
          dates.forEach(date => {
            shifts.forEach(shift => {
              xAxisData.push(`${date} ${shift}`);
            });
          });
          // 3. 构建系列数据
          const series = categories.map((category, index) => {
            const data = [];
            dates.forEach(date => {
              shifts.forEach(shift => {
                const items = rawData[date][shift] || [];
                const item = items.find(d => d.categoryName === category);
                data.push(item ? item.targetNumber : 0);
              });
            });
            return {
              name: category,
              type: 'bar',
              stack: 'day',
              barWidth: 25,
              itemStyle: { color: colors[index] },
              emphasis: { focus: 'series' },
              data: data,
              label: index === 3 ? {
                show: true,
                position: 'top',
                formatter: function (params) {
                  // 只在柱状图底部显示班次（早/午/夜）
                  return xAxisData[params.dataIndex][xAxisData[params.dataIndex].length - 1];
                },
                distance: 5,
                rotate: 0,
                color: '#333',
                fontSize: 12
              } : { show: false }
            };
          });
          // 4. 处理x轴刻度线（每天一个主刻度）
          const axisLineInterval = 3; // 每天3个班次
          const axisLabelFormatter = (value, index) => {
            // 只在每天的第一个班次（早班）显示日期
            if (index % axisLineInterval === 1) {
              const [fullDate] = value.split(' ');
              const [, month, day] = fullDate.split('-');
              return `${month}-${day}`;
            }
            return '';
          };
          const option = {
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(246, 248, 252, 0.8)',
              borderColor: '#fff',
              borderWidth: 2,
              borderRadius: 5,
              textStyle: {
                color: '#36373B',
                fontSize: 13
              },
              extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
              padding: 10,
              axisPointer: { type: 'shadow' },
              formatter: function (params) {
                const [date, shift] = params[0].axisValue.split(' ');
                const result = [`<div style="margin-bottom:5px;font-size: 13px;color: #000;"> ${`${date} ${shift}`} </div>`];
                params.forEach(item => {
                  const color = item.color;
                  result.push(
                    `<div style="display:flex;align-items:center;margin-bottom: 3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${item.value}</span>
                </div>`)
                });
                return result.join(' ');
              },
            },
            legend: {
              type: 'plain',
              icon: 'circle',
              left: '15%',
              top: '0',
              itemHeight: 12,
              itemWidth: 12,
              itemGap: 50,
              textStyle: {
                fontSize: 12
              },
              data: categories,
            },
            grid: {
              top: '15%',
              left: '2%',
              right: '2%',
              bottom: '2%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: xAxisData,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#6E7079',
                  width: 1
                }
              },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#3E4B5C',
                  fontSize: 11
                },
                interval: 0,
                margin: 10,
                formatter: axisLabelFormatter
              },
              axisTick: {
                alignWithLabel: false,
                length: 8,
                interval: (index) => index % axisLineInterval === 0 // 每天一个主刻度
              }
            },
            yAxis: {
              type: 'value',
              axisLine: { show: false },
              axisTick: { show: false },
              axisLabel: {
                show: true,
                textStyle: {
                  color: '#979A9F',
                  fontSize: 12
                }
              },
              splitLine: {
                show: true,
                lineStyle: {
                  color: '#F3F3F4'
                }
              },
            },
            series: series,
            color: colors
          };
          this.$nextTick(() => {
            this.initChart(option);
            window.addEventListener('resize', this.resizeChart);
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(optionNew) {
      let that = this;
      let len = 0;
      const chartDom = this.$refs.echartsL;
      this.chartL = echarts.init(chartDom);
      const option = optionNew;
      this.chartL.setOption(option);
      /*    this.timer = setInterval(() => {
           if (len === xDataCokeWeight.length) {
             len = 0;
           }
           this.chartL.dispatchAction({
             type: "showTip",
             seriesIndex: 0,
             dataIndex: len,
           });
           len++;
         }, 3000); */
    },
    resizeChart() {
      if (this.chartL) {
        this.chartL.resize();
      }
    },
    chagngeGl(name) {
      if (name == '1') {
        if (this.type == '高炉') {
          this.activeGL = '一高炉';
          this.param.prodCenterCode = 'CPES01';
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '一烧结';
          this.param.prodCenterCode = 'CPES01';
          this.initValue();
        }
        clearInterval(this.timer);
      }
      if (name == '2') {
        if (this.type == '高炉') {
          this.activeGL = '二高炉';
          this.param.prodCenterCode = 'CPES02';
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '二烧结';
          this.param.prodCenterCode = 'CPES02';
          this.initValue();
        }
        clearInterval(this.timer);
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsL_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 10%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-text {
  width: 230px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 18px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsL {
  width: 100%;
  height: 100%;
}
</style>