import request from '@/utils/request'

// 查询烧结仓位校准列表
export function listCalibration(query, selectVO) {
  return request({
    url: '/api/cpes/postionCalibration/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

export function queryForManger(query) {
  return request({
    url: '/api/cpes/postionCalibration/queryForManger',
    method: 'get',
    params: query
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/postionCalibration/saveOrUpdate',
    method: 'post',
    data: data
  })
}
export function getpostionList(prodCenterCode) {
  return request({
    url: '/api/cpes/postionCalibration/getpostionList/' + prodCenterCode,
    method: 'get'
  })
}
export function getEdit(query) {
  return request({
    url: '/api/cpes/postionCalibration/getEdit',
    method: 'get',
    params: query
  })
}

export function sync(query) {
  return request({
    url: '/api/cpes/postionCalibration/sync',
    method: 'get',
    params: query
  })
}

export function deleteForManger(query) {
  return request({
    url: '/api/cpes/postionCalibration/deleteForManger',
    method: 'get',
    params: query
  })
}

// 查询烧结仓位校准详细
export function getCalibration(calibrationId) {
  return request({
    url: '/api/cpes/postionCalibration/' + calibrationId,
    method: 'get'
  })
}

// 新增烧结仓位校准
export function addCalibration(data) {
  return request({
    url: '/api/cpes/postionCalibration',
    method: 'post',
    data: data
  })
}

// 修改烧结仓位校准
export function updateCalibration(data) {
  return request({
    url: '/api/cpes/postionCalibration',
    method: 'put',
    data: data
  })
}

// 删除烧结仓位校准
export function delCalibration(calibrationId) {
  return request({
    url: '/api/cpes/postionCalibration/' + calibrationId,
    method: 'delete'
  })
}
