import request from '@/utils/request'
import { getToken } from '@/utils/auth'

// 登录方法
export function login(username, password, code,uuid) {
    const data = {
        username,
        password,
        code,
        uuid,
        'grant_type': 'password'
    }
    return request({
        url: '/oauth/token',
        headers: {
            'Authorization': 'Basic bGluZ3hpYW86bGluZ3hpYW8=',
            isToken: false
        },
        method: 'post',
        params: data
    })
}

// 注册方法
export function register(data) {
    return request({
        url: '/register',
        headers: {
            isToken: false
        },
        method: 'post',
        data: data
    })
}

// 获取用户详细信息
export function getInfo() {
    return request({
        url: '/getInfo',
        method: 'get'
    })
}

// 退出方法
export function logout() {
    let params = {
        access_token: getToken()
    }
    return request({
        url: '/api/exit',
        method: 'get',
        params: params
    })
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: '/captchaImage',
        headers: {
            isToken: false
        },
        method: 'get',
        timeout: 20000
    })
}
