import request from '@/utils/request'

// 查询表单数据列表
export function listData(query) {
  return request({
    url: '/api/md/TMdMaterialExpand/list',
    method: 'get',
    params: query
  })
}

// 查询表单类型详细
export function getType(materialExpandId) {
  return request({
    url: '/api/md/TMdMaterialExpand/queryById/' + materialExpandId,
    method: 'get'
  })
}

// 查询表单类型详细
export function addType(data) {
  return request({
    url: '/api/md/TMdMaterialExpand/addType',
    method: 'post',
    data: data
  })
}

// 查询表单类型详细
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/TMdMaterialExpand/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 修改表单类型
export function updateType(data) {
  return request({
    url: '/api/md/TMdMaterialExpand/updateType',
    method: 'put',
    data: data
  })
}

// 查询字典表单数据列表（字典类型为 material_tags）
export function dictList(query) {
  return request({
    url: '/api/md/TMdMaterialExpand/dictList',
    method: 'get',
    params: query
  })
}

// 删除物料扩展属性
export function delExpand(materialExpandId) {
  return request({
    url: '/api/md/TMdMaterialExpand/delExpand/' + materialExpandId,
    method: 'delete'
  })
}



export function searchByTypeAndValue(type, value) {
  return request({
    url: '/api/md/TMdMaterialExpand/searchByTypeAndValue/' + type + "/" + value,
    method: 'get',
  })
}

export function searchByMateralValue() {
  return request({
    url: '/api/md/TMdMaterialExpand/searchByMateralValue',
    method: 'get',
  })
}


// 查询表单数据列表
export function tMdMaterialExpandList(query) {
  return request({
    url: '/api/md/TMdMaterialExpandMiddle/middle/list',
    method: 'get',
    params: query
  })
}

