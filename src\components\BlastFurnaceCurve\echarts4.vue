<template>
  <div class="echarts4_box">
    <div class="title-bar">
      <div class="title-text">{{ title || '-'}}</div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉' || activeGL === '一烧结'}" @click="chagngeGl('1')">一{{ type }}</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉' || activeGL === '二烧结'}" @click="chagngeGl('2')">二{{ type }}</el-button>
      </div>
    </div>
    <div class="echarts-tooltip">
      <div class="tooltip-text">
        <div class="tooltip-name">
          当日计划产量
          <span class="tooltip-value" :style="{'color': planRatioDay > 0 ? '#F76161' : '#50D166'}">
            {{ planRatioDay == '-' ?  planRatioDay : (planRatioDay*100).toFixed(2)}}%
            <img v-if="planRatioDay != '-' && planRatioDay != 0"
                 :src="planRatioDay > 0 ? require('@/assets/images/blastFurnace/<EMAIL>') : require('@/assets/images/blastFurnace/<EMAIL>')" alt="">
          </span>
        </div>
        <div class="tooltip-mark">
          <img :src="require('@/assets/images/blastFurnace/<EMAIL>')" alt=""> {{ weightDayP }} 吨
        </div>
      </div>
      <div class="tooltip-text">
        <div class="tooltip-name">
          当日实际产量
          <span class="tooltip-value" :style="{'color': realRatioDay > 0 ? '#F76161' : '#50D166'}">
            {{ realRatioDay == '-' ?  realRatioDay : (realRatioDay*100).toFixed(2) }}%
            <img v-if="realRatioDay != '-' && realRatioDay != 0"
                 :src="realRatioDay > 0 ? require('@/assets/images/blastFurnace/<EMAIL>') : require('@/assets/images/blastFurnace/<EMAIL>')" alt="">
          </span>
        </div>
        <div class="tooltip-mark">
          <img :src="require('@/assets/images/blastFurnace/<EMAIL>')" alt=""> {{ weightDayR }} 吨
        </div>
      </div>
      <div class="tooltip-text">
        <div class="tooltip-name">
          当月计划产量
          <span class="tooltip-value" :style="{'color': planRatioMounth > 0 ? '#F76161' : '#50D166'}">
            {{ planRatioMounth == '-' ?  planRatioMounth : (planRatioMounth*100).toFixed(2) }}%
            <img v-if="planRatioMounth != '-'"
                 :src="planRatioMounth > 0 ? require('@/assets/images/blastFurnace/<EMAIL>') : require('@/assets/images/blastFurnace/<EMAIL>')" alt="">
          </span>
        </div>
        <div class="tooltip-mark">
          <img :src="require('@/assets/images/blastFurnace/<EMAIL>')" alt=""> {{ weightMonthP }} 吨
        </div>
      </div>
      <div class="tooltip-text">
        <div class="tooltip-name">
          当月实际产量
          <span class="tooltip-value" :style="{'color': realRatioMounth > 0 ? '#F76161' : '#50D166'}">
            {{ realRatioMounth == '-' ?  realRatioMounth : (realRatioMounth*100).toFixed(2) }}%
            <img v-if="realRatioMounth != '-'"
                 :src="realRatioMounth > 0 ? require('@/assets/images/blastFurnace/<EMAIL>') : require('@/assets/images/blastFurnace/<EMAIL>')" alt="">
          </span>
        </div>
        <div class="tooltip-mark">
          <img :src="require('@/assets/images/blastFurnace/<EMAIL>')" alt=""> {{ weightMonthR }} 吨
        </div>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsL" class="echartsL"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenDay, ironScreenMonth } from "@/api/analyse/blastfurnace";
import { cpesScreenDay, cpesScreenMonth } from "@/api/analyse/sintering";

export default {
  name: 'ECharts4',
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    }
  },
  components: {
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chartL) {
          this.chartL.resize();
        }
      }, 300);
    }
  },
  directives: {},
  data() {
    return {
      activeGL: this.type == '高炉' ? '一高炉' : '一烧结',
      chartL: null,
      planRatioDay: null,
      realRatioDay: null,
      planRatioMounth: null,
      realRatioMounth: null,
      weightDayP: null,
      weightDayR: null,
      weightMonthP: null,
      weightMonthR: null,
      param: {
        blastFurnaceCode: '1'
      },
      param1: {
        prodCenterCode: '1'
      },
      timer: null
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chartL) {
      this.chartL.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
    clearInterval(this.timer);
  },
  methods: {
    generateMonthDates(year, month) {
      const daysInMonth = new Date(year, month, 0).getDate();
      return Array.from({ length: daysInMonth }, (_, i) => {
        const day = i + 1;
        return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      });
    },
    isToday(dateString) {
      if (!dateString) return false;
      const apiDate = new Date(dateString);
      const today = new Date();
      return (
        apiDate.getFullYear() === today.getFullYear() &&
        apiDate.getMonth() === today.getMonth() &&
        apiDate.getDate() === today.getDate()
      );
    },
    initValue() {
      if (this.type == '高炉') {
        ironScreenDay(this.param).then(res => {
          if (res.code === 200) {
            var data = res.data;
            var xDataR = [];
            var xDataP = [];
            var yData = [];
            var planRatioDayArr = [];
            var realRatioDayArr = [];
            data.forEach((item, index) => {
              xDataR[index] = item.realNumber || 0;
              xDataP[index] = item.planNumber || 0;
              yData[index] = item.plugTime || '0';
              planRatioDayArr[index] = item.planRatio || '';
              realRatioDayArr[index] = item.realRatio || '';
            });
            if (this.isToday(yData[0])) {
              this.weightDayP = xDataP[0];
              this.weightDayR = xDataR[0];
              this.planRatioDay = planRatioDayArr[0];
              this.realRatioDay = realRatioDayArr[0];
            } else {
              this.weightDayP = '-';
              this.weightDayR = '-';
              this.planRatioDay = '-';
              this.realRatioDay = '-';
            }
            ironScreenMonth(this.param).then(response => {
              if (response.code === 200) {
                var xDataRM = [];
                var xDataPM = [];
                if (xDataR.length > 0) {
                  xDataRM = Array(xDataR.length).fill(response.data[0].realNumber);
                  xDataPM = Array(xDataP.length).fill(response.data[0].planNumber);
                  this.weightMonthP = response.data[0].planNumber.toFixed(2);
                  this.weightMonthR = response.data[0].realNumber.toFixed(2);
                  this.planRatioMounth = response.data[0].planRatio.toFixed(2);
                  this.realRatioMounth = response.data[0].realRatio.toFixed(2);
                } else {
                   if(response.data.length > 0){
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = now.getMonth() + 1;
                    yData = this.generateMonthDates(year, month);
                    xDataRM = Array(yData.length).fill(response.data[0].realNumber);
                    xDataPM = Array(yData.length).fill(response.data[0].planNumber);
                  }else{
                    yData = [];
                    xDataRM = [];
                    xDataPM = [];
                  }
                  this.weightMonthP = '-';
                  this.weightMonthR = '-';
                  this.planRatioMounth = '-';
                  this.realRatioMounth = '-';
                }
                this.$nextTick(() => {
                  this.initChart(xDataR, xDataP, xDataRM, xDataPM, yData);
                  window.addEventListener('resize', this.resizeChart);
                })
              } else {
                this.$message.error(response.msg || "获取数据失败");
              }
            });
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        }).catch((error) => {
          console.error("获取数据出错:", error);
        });
      } else if (this.type == '烧结')  {
       cpesScreenDay(this.param1).then(res => {
          if (res.code === 200) {
            var data = res.data;
            var xDataR = [];
            var xDataP = [];
            var yData = [];
            var planRatioDayArr = [];
            var realRatioDayArr = [];
            data.forEach((item, index) => {
              xDataR[index] = item.realNumber || 0;
              xDataP[index] = item.targetNumber || 0;
              yData[index] = item.workDate || '0';
              planRatioDayArr[index] = item.targetRatio || 0;
              realRatioDayArr[index] = item.realRatio || 0;
            });
            if (this.isToday(yData.slice(-1)[0])) {
              this.weightDayP = xDataP.slice(-1)[0].toFixed(2);
              this.weightDayR = xDataR.slice(-1)[0].toFixed(2);
              this.planRatioDay = planRatioDayArr.slice(-1)[0].toFixed(2);
              this.realRatioDay = realRatioDayArr.slice(-1)[0].toFixed(2);
            } else {
              this.weightDayP = '-';
              this.weightDayR = '-';
              this.planRatioDay = '-';
              this.realRatioDay = '-';
            }
            cpesScreenMonth(this.param1).then(response => {
              if (response.code === 200) {
                var xDataRM = [];
                var xDataPM = [];
                if (xDataR.length > 0) {
                  xDataRM = Array(xDataR.length).fill(response.data[0].realNumber);
                  xDataPM = Array(xDataP.length).fill(response.data[0].targetNumber);
                  this.weightMonthP = response.data[0].targetNumber.toFixed(2);
                  this.weightMonthR = response.data[0].realNumber.toFixed(2);
                  this.planRatioMounth = response.data[0].targetRatio.toFixed(2);
                  this.realRatioMounth = response.data[0].realRatio.toFixed(2);
                } else {
                  if(response.data.length > 0){
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = now.getMonth() + 1;
                    yData = this.generateMonthDates(year, month);
                    xDataRM = Array(yData.length).fill(response.data[0].realNumber);
                    xDataPM = Array(yData.length).fill(response.data[0].targetNumber);
                  }else{
                    yData = [];
                    xDataRM = [];
                    xDataPM = [];
                  }
                  this.weightMonthP = '-';
                  this.weightMonthR = '-';
                  this.planRatioMounth = '-';
                  this.realRatioMounth = '-';
                }
                this.$nextTick(() => {
                  this.initChart(xDataR, xDataP, xDataRM, xDataPM, yData);
                  window.addEventListener('resize', this.resizeChart);
                })
              } else {
                this.$message.error(response.msg || "获取数据失败");
              }
            });
          } else {
            this.$message.error(res.msg || "获取数据失败");
          }
        }).catch((error) => {
          console.error("获取数据出错:", error);
        });
      }
    },
    initChart(xDataR, xDataP, xDataRM, xDataPM, yData) {
      let that = this;
      let len = 0;
      const chartDom = this.$refs.echartsL;
      this.chartL = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: xDataR.length > 0 && xDataP.length > 0 && xDataRM.length > 0 && xDataPM.length > 0 && yData.length > 0
          }]
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getFullYear()} - ${date.getMonth() + 1}-${date.getDate()}</div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        grid: {
          top: '8%',
          left: '1.5%',
          right: '2%',
          bottom: '5%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgb(244,245,248)',
              width: 3
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#4F5B6A',
              fontSize: 12,
              fontWeight: 500
            },
            formatter: function (value) {
              return value.split('-').slice(1).join('-');
            },
          },
          axisTick: {
            show: false
          },
          data: yData
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#7E8996',
              fontSize: 12
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F0F2F6',
              type: 'solid',
              width: 2
            }
          },
        },
        series: [
          {
            name: '当日计划产量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: yData.length == 1 ? true : false,
            triggerEvent: true,
            itemStyle: {
              color: '#F2994A'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: '#FFE4BA'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataP
          },
          {
            name: '当日实际产量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: yData.length == 1 ? true : false,
            triggerEvent: true,
            itemStyle: {
              color: 'rgba(15,198,194,1)'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(232,255,251,1)'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: xDataR
          },
          {
            name: '当月计划产量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: yData.length == 1 ? true : false,
            triggerEvent: true,
            itemStyle: {
              color: '#165DFF'
            },
            /*        areaStyle: {
                     opacity: 0.8,
                     color: new echarts.graphic.LinearGradient(
                       0, 1, 0, 0,
                       [
                         {
                           offset: 0,
                           color: 'rgba(255,255,255,0.43)'
                         },
                         {
                           offset: 1,
                           color: '#a1bbff'
                         }
                       ]
                     )
                   }, */
            emphasis: {
              focus: 'series'
            },
            data: xDataPM
          },
          {
            name: '当月实际产量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 10,
            showSymbol: yData.length == 1 ? true : false,
            triggerEvent: true,
            itemStyle: {
              color: '#722ED1'
            },
            /*   areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(
                  0, 1, 0, 0,
                  [
                    {
                      offset: 0,
                      color: 'rgba(255,255,255,0.43)'
                    },
                    {
                      offset: 1,
                      color: '#c5a6f1'
                    }
                  ]
                )
              }, */
            emphasis: {
              focus: 'series'
            },
            data: xDataRM
          }
        ]
      };
      this.chartL.setOption(option);
      /* this.timer = setInterval(() => {
        if (len === yData.length) {
          len = 0;
        }
        this.chartL.dispatchAction({
          type: "showTip",
          seriesIndex: 0,
          dataIndex: len,
        });
        len++;
      }, 3000); */
    },
    resizeChart() {
      if (this.chartL) {
        this.chartL.resize();
      }
    },
    chagngeGl(name) {
      if (name == '1') {
        if (this.type == '高炉') {
          this.activeGL = '一高炉';
          this.param.blastFurnaceCode = name;
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '一烧结';
          this.param1.prodCenterCode = name;
          this.initValue();
        }
        clearInterval(this.timer);
      }
      if (name == '2') {
        if (this.type == '高炉') {
          this.activeGL = '二高炉';
          this.param.blastFurnaceCode = name;
          this.initValue();
        }
        if (this.type == '烧结') {
          this.activeGL = '二烧结';
          this.param1.prodCenterCode = name;
          this.initValue();
        }
        clearInterval(this.timer);
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echarts4_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 10%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-text {
  width: 230px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 18px;
}
.button-group {
}
.echarts-tooltip {
  width: 100%;
  height: 25%;
  display: flex;
}
.tooltip-text {
  width: 25%;
  height: 100%;
  padding: 10px 0 0 23px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}
.tooltip-name {
  width: 100%;
  height: 30%;
  font-family: Arial, Helvetica, sans-serif;
  color: black;
  font-size: 15px;
}
.tooltip-value {
  width: 58%;
  float: right;
  height: 100%;
}
.tooltip-mark {
  width: 100%;
  height: 70%;
  font-family: Arial, Helvetica, sans-serif;
  color: black;
  font-size: 20px;
  padding: 10px 0 0 0;
  letter-spacing: 3px;
}
.tooltip-mark > img {
  width: 32px;
  height: 32px;
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsL {
  width: 100%;
  height: 100%;
}
</style>