import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listLog(query,selectVO) {
  return request({
    url: '/api/TApiMeterWeightLog/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询【请填写功能名称】详细
export function getLog(fid) {
  return request({
    url: '/api/TApiMeterWeightLog/' + fid,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addLog(data) {
  return request({
    url: '/api/TApiMeterWeightLog',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateLog(data) {
  return request({
    url: '/api/TApiMeterWeightLog',
    method: 'put',
    data: data
  })
}

// 删除【请填写功能名称】
export function delLog(fid) {
  return request({
    url: '/api/TApiMeterWeightLog/' + fid,
    method: 'delete'
  })
}
