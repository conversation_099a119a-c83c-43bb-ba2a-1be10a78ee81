import request from '@/utils/request'

export function selectMachineData(query){
    return request({
        url: '/api/wms/machineOrder/list',
        method: 'get',
        params: query
      });
}

export function insertMachineWorkData(data){
  return request({
      url: '/api/wms/machineOrder/insert',
      method: 'post',
      data: data
    });
}

export function updateMachineData(data){
  return request({
      url: '/api/wms/machineOrder/update',
      method: 'put',
      data: data
    });
}

export function deleteMachineData(ids) {
  const idArray = Array.isArray(ids) ? ids : [ids]
  const encodedIds = idArray
    .map(id => encodeURIComponent(id))
    .join(',') 
  return request({
    url: `/api/wms/machineOrder/delete/${encodedIds}`, 
    method: 'post',
  });
}


export function initMaterialList(query){
  return request({
      url: '/api/wms/machineOrder/init',
      method: 'get',
      params: query
    });
} 