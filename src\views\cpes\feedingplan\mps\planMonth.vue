<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="开始日期" prop="startTime">
        <el-date-picker
          v-model="queryParams.startTime"
          type="date"
          placeholder="选择开始日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="结束日期" prop="endTime">
        <el-date-picker
          v-model="queryParams.endTime"
          type="date"
          placeholder="选择结束日期"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
        />
      </el-form-item>
      <el-form-item label="加工中心" prop="prodCenterCode">
        <el-select v-model="queryParams.prodCenterCode" placeholder="请选择加工中心" clearable>
          <el-option
            v-for="center in processingCenterList"
            :key="center.id"
            :label="center.name"
            :value="center.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <!-- <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <vxe-table
      ref="tableRef"
      v-loading="loading"
      :data="planList"
      border
      stripe
      size="mini"
    >
      <vxe-column field="playDay" title="日期" width="100" align="center" fixed="left"></vxe-column>
      <vxe-column field="prodCenterName" title="加工中心" width="90" align="center" fixed="left">
        <template #default="{ row }">
          <span>{{ getProdCenterName(row) }}</span>
        </template>
      </vxe-column>
      <!-- <vxe-column field="statusName" title="状态" width="80" align="center" fixed="left">
        <template #default="{ row }">
          <span>{{ getStatusName(row) }}</span>
        </template>
      </vxe-column> -->

      <!-- 日计划 -->
      <vxe-colgroup title="日计划" align="center">
        <vxe-column field="dayStriveTarget" title="力争目标" width="90" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.dayStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dayEnsureTarget" title="必保目标" width="90" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.dayEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dayActualComplete" title="实际完成" width="90" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.dayActualComplete) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="dayCompletionRate" title="完成率(%)" width="90" align="center">
          <template #default="{ row }">
            <span>{{ calculateCompletionRate(row) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>



      <!-- 备注 -->
      <vxe-column field="remark" title="备注" width="150" align="left" show-overflow="tooltip"></vxe-column>

    </vxe-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  listMonthPlan,
  exportMonthPlan,
  getProcessingCenterList
} from "@/api/feedingplan/mps";
import request from '@/utils/request';

export default {
  name: "PlanMonthQuery",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 月度计划表格数据
      planList: [],
      // 加工中心列表
      processingCenterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        startTime: undefined,  // 对应后端的startTime
        endTime: undefined,    // 对应后端的endTime
        prodCenterCode: undefined
      }
    };
  },
  created() {
    this.getList();
    this.getProcessingCenterList();
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {

    /** 查询月度计划列表 */
    getList() {
      this.loading = true;

      // 调用新的API接口
      request({
        url: '/api/productPlanDay/list',
        method: 'get',
        params: this.queryParams
      }).then(response => {
        this.planList = response.rows || [];
        console.log("this.planList:",JSON.stringify(this.planList))
        this.total = response.total || 0;
        this.loading = false;
      }).catch(() => {

        this.planList = [

        ];
        this.total = this.planList.length;
        this.loading = false;
      });
    },

    /** 获取加工中心列表 */
    getProcessingCenterList() {
      this.processingCenterList = [
        { id: 'CPES01', name: '1#烧结', code: 'CPES01' },
        { id: 'CPES02', name: '2#烧结', code: 'CPES02' },
        { id: 'IPES01', name: '1#高炉', code: 'IPES01' },
        { id: 'IPES02', name: '2#高炉', code: 'IPES02' },
      ];

    
    },



    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      return Number(value).toLocaleString();
    },

    /** 格式化百分比 */
    formatPercent(value) {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      return Number(value).toFixed(1);
    },

    /** 计算完成率 */
    calculateCompletionRate(row) {
      if (!row.dayActualComplete || !row.dayStriveTarget || row.dayStriveTarget === 0) {
        return '-';
      }
      const rate = (row.dayActualComplete / row.dayStriveTarget * 100).toFixed(1);
      return rate;
    },

    /** 获取生产中心名称 */
    getProdCenterName(row) {
      // 如果后端直接返回了生产中心名称，直接使用
      if (row.prodCenterName) {
        return row.prodCenterName;
      }
      // 否则根据productPlanFid或其他字段推断
      // 这里可以根据实际业务逻辑来映射
      return row.prodCenterCode || '未知';
    },

    /** 获取状态名称 */
    getStatusName(row) {
      // 如果后端直接返回了状态名称，直接使用
      if (row.statusName) {
        return row.statusName;
      }
      // 否则根据状态码映射
      const statusMap = {
        0: '草稿',
        1: '已下发',
        2: '执行中',
        3: '已完成'
      };
      return statusMap[row.status] || '未知';
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导出按钮操作 */
    handleExport() {
      this.download('/mps/monthplan/export', {
        ...this.queryParams
      }, `月度生产计划查询_${new Date().getTime()}.xlsx`);
    }
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 覆盖全局样式，取消绝对定位 */
.pagination-container .el-pagination {
  position: static !important;
  right: auto !important;
}



/* 表格样式优化 */
::v-deep .vxe-table {
  font-size: 13px;
}

::v-deep .vxe-table .vxe-header--column {
  background-color: #f5f7fa;
  font-weight: bold;
}

::v-deep .vxe-table .vxe-body--row:hover {
  background-color: #f5f7fa;
}




/* 表单样式优化 */
::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-input-number {
  width: 100%;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}




</style>
