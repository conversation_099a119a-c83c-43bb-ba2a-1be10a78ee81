<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="40px">

      <el-form-item label-width="80px" label="创建时间" prop="createTime">
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
                        end-placeholder="结束日期" style="width: 220px;">
        </el-date-picker>
      </el-form-item>

      <el-form-item label="工序" prop="processes">
        <el-select v-model="queryParams.processes" placeholder="请选择包含内容" clearable>
          <el-option v-for="item in this.proList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>

      <el-form-item label-width="60px" label="预警点" prop="factorys" >
        <el-select v-model="queryParams.factorys" placeholder="请选择包含内容" clearable>
          <el-option v-for="item in this.factoryList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="80px" label="内容性质" prop="性质">
        <el-select v-model="queryParams.propertys" placeholder="请选择包含内容" clearable>
          <el-option v-for="item in this.propeList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="80px" label="内容类型" prop="contentType">
        <el-select v-model="queryParams.contentType" placeholder="请选择包含内容" clearable>
          <el-option v-for="item in this.contentTypeList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label-width="80px" label="处理状态" prop="processStatus">
          <el-select v-model="queryParams.processStatus" placeholder="请选择包含内容" clearable>
            <el-option v-for="item in this.processStatusList" :key="item.processStatusLable" :label="item.processStatusLable" :value="item.processStatusValue">
            </el-option>
          </el-select>
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">预警处理
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="multiple"
                   @click="handleUpdateAll">预警批量处理
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div class="mes_new_table">
      <el-table size="mini" border :data="tableData" style="width: 100%;" @selection-change="handleSelectionChange"
                tooltip-effect="light">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="推送时间" prop="createTime" fixed="left" min-width="150"></el-table-column>
        <el-table-column label="工序" prop="processes" fixed="left" min-width="50"></el-table-column>
        <el-table-column label="厂区" prop="factorys" fixed="left" min-width="180"></el-table-column>

        <el-table-column label="内容类型" prop="contentType"></el-table-column>
        <el-table-column label="发送内容" :show-overflow-tooltip="true" align="center" min-width="700">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.processMode" raw-content placement="top-start" v-if="scope.row.processMode">
            <span v-if="scope.row.processMode && scope.row.processMode.length <= 15">
              {{ scope.row.processMode }}
            </span>
              <span v-if="scope.row.processMode && scope.row.processMode.length > 15">
<!--              {{ scope.row.processMode.substr(0, 15) + '...' }}-->
                 {{ scope.row.processMode }}
            </span>
            </el-tooltip>
            <span v-else-if="scope.row.processMode == null"> </span>

          </template>
        </el-table-column>
        <el-table-column label="内容性质" prop="propertys" min-width="100"></el-table-column>

        <el-table-column label="处理状态" prop="processStatus">
          <template slot-scope="scope">

            <el-tag v-if="scope.row.processStatus == '未处理'" type="danger" style="width: 100%;">
              {{ scope.row.processStatus }}
            </el-tag>
            <el-tag v-else-if="scope.row.processStatus != '' && scope.row.processStatus != null" type="success"
                    style="width: 100%;">
              {{ scope.row.processStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="机器编码" prop="robotInnerCode" min-width="120"></el-table-column>

        <el-table-column label="业务流水号" prop="operationSerialNumber" width="240"></el-table-column>
        <el-table-column label="处理方案" prop="resourceInfo" :show-overflow-tooltip="true" align="center" min-width="90">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.resourceInfo" raw-content placement="top-start"
                        v-if="scope.row.resourceInfo">
            <span v-if="scope.row.resourceInfo && scope.row.resourceInfo.length <= 15">
              {{ scope.row.resourceInfo }}
            </span>
              <span v-if="scope.row.resourceInfo && scope.row.resourceInfo.length > 15">
              {{ scope.row.resourceInfo.substr(0, 15) + '...' }}
            </span>
            </el-tooltip>
            <span v-else-if="scope.row.resourceInfo == null"> </span>
          </template>
        </el-table-column>
        <el-table-column label="处理人员" prop="processBy"></el-table-column>
        <el-table-column label="处理时间" prop="processTime" min-width="160"></el-table-column>
        <el-table-column label="创建者" prop="createBy"></el-table-column>
        <el-table-column label="修改时间" prop="updateTime" min-width="180"></el-table-column>
        <el-table-column label="更新者" prop="updateBy"></el-table-column>
      </el-table>
    </div>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                @pagination="getList"/>


    <!-- 添加或修改企业微信机器人等级对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="110px">
        <el-form-item label="工序" prop="processes">
          <el-input v-model="form.processes" placeholder="请输入工序" disabled/>
        </el-form-item>

        <el-form-item label="厂区" prop="factorys">
          <el-input v-model="form.factorys" placeholder="请输入厂区" disabled/>
        </el-form-item>

        <el-form-item label="处理人员" prop="processBy">
          <el-input v-model="form.processBy" placeholder="请输入处理人员"/>
        </el-form-item>
        <el-form-item label="处理方案" prop="resourceInfo">
          <el-input type="textarea" v-model="form.resourceInfo" placeholder="请输入描述"/>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import {
    listGrade,
    getGrade,
    delGrade,
    addGrade,
    updateGrade,
    updateGradeAll,
    getProcess,
    getFactory,
    getContentType,
    getpropertys,
    finishWarning
  } from '@/api/warn/grade/index'
  import VueMarkdown from 'vue-markdown'
  import dayjs from 'dayjs'

  export default {
    name: 'Grade',
    components: { VueMarkdown },
    data() {
      return {
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        // 企业微信机器人等级表格数据
        gradeList: [],
        // 弹出层标题
        title: '',
        // 是否显示弹出层
        open: false,
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: 10,
          processes: null,
          factorys: null,
          contentType: null,
          propertys: null,
          startTime: null,
          endTime: null,
          processStatus: null
        },
        // 表单参数
        form: {},
        // 表单校验
        rules: {
          processBy: [
            {
              required: true, message: '处理人员不呢为空', trigger: 'blur'
            }
          ],
          resourceInfo: [
            {
              required: true, message: '处理方案不能为空', trigger: 'blur'
            }
          ]
        },
        // 日期范围
        dateRange: [],
        tableData: [],
        proList: [],
        factoryList: [],
        contentTypeList: [],
        propeList: [],
        selectVO: '',
        // 显示 添加或修改
        submitFlag: '',
        // 数据详情
        dataDetail: {},
        // 处理状态下拉框
        processStatusList:[
          {
            "processStatusLable":"已处理",
            "processStatusValue":"已处理"
          },
          {
            "processStatusLable":"未处理",
            "processStatusValue":"未处理"
          },
        ]
      }
    },
    created() {
      this.dateRange.push(dayjs(new Date()).add(-1, 'day').startOf('date'))
      this.dateRange.push(dayjs(new Date()).endOf('date'))

      getProcess(this.getInnerCode()).then((response) => {
        this.proList = response.data
      })
      getFactory(this.getInnerCode()).then((response) => {
        this.factoryList = response.data
      })
      getContentType(this.getInnerCode()).then((response) => {
        this.contentTypeList = response.data
      })
      getpropertys(this.getInnerCode()).then((response) => {
        this.propeList = response.data
      })
      this.getList(null)
    },
    methods: {
      getInnerCode() {
        return this.$route.query.innerCode
      },
      /** pageNum事件 */
      numChange(pageNum, selectVO) {
        this.queryParams.pageNum = pageNum
        this.selectVO = selectVO
        this.getList(selectVO)
      },
      /** pageSize事件 */
      sizeChange(pageSize, selectVO) {
        this.queryParams.pageSize = pageSize
        this.selectVO = selectVO
        this.getList(selectVO)
      },
      /** 查询企业微信机器人等级列表 */
      getList(selectVO) {
        this.loading = true
        if (selectVO) {
          this.selectVO = selectVO
        }
        this.queryList()
      },
      queryList() {

        if (this.dateRange.length == 2) {
          this.queryParams.startTime = dayjs(this.dateRange[0]).format(
            'YYYY-MM-DD HH:mm:ss'
          )
          this.queryParams.endTime = dayjs(this.dateRange[1]).format(
            'YYYY-MM-DD HH:mm:ss'
          )
        }
        this.queryParams.robotInnerCode = this.getInnerCode()
        listGrade(this.queryParams, this.selectVO).then(response => {
          this.tableData = response.rows
          this.total = response.total
          this.loading = false
          this.queryParams.endTime = null
          this.queryParams.startTime = null
        })
      },

      // 取消按钮
      cancel() {
        this.open = false
        this.reset()
      },
      // 表单重置
      reset() {
        this.form = {
          robotId: null,
          processes: null,
          operationSerialNumber: null,
          factorys: null,
          contentType: null,
          propertys: null,
          processMode: null,
          robotInnerCode: null,
          processBy: null,
          processTime: null,
          processStatus: '0',
          sendTime: null,
          createTime: null,
          createBy: null,
          updateTime: null,
          updateBy: null,
          resourceInfo: null
        }
        this.resetForm('form')
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1
        this.getList()
      },

      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.robotId)
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },

      /** 修改按钮操作 */
      handleUpdate(row) {
        this.reset()
        const robotId = row.robotId || this.ids
        getGrade(robotId).then(response => {
          this.form = response.data
          this.open = true
          this.title = '预警信息处理'
        })
      },
      /** 提交按钮 */
      submitForm() {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            finishWarning(this.form).then((response) => {
              this.$modal.msgSuccess('处理成功')
              this.open = false
              this.getList()
            })
          }
        })
      },
      /** 批量修改按钮操作 */
      handleUpdateAll(row) {
        const robotId = row.robotId || this.ids
        this.$modal.confirm('确定对选中的预警信息进行批量处理吗？').then(function() {
          return updateGradeAll(robotId)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('修改成功')
        }).catch(() => {
        })
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const robotId = row.robotId || this.ids
        this.$modal.confirm('确定删除选中的推送信息吗？').then(function() {
          return delGrade(robotId)
        }).then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        }).catch(() => {
        })
      },
      /** 查询详情按钮操作 */
      handleDetial(row) {
        const robotId = row.robotId || this.ids
        getGrade(robotId).then((response) => {
          this.dataDetail = response.data
        })

      },
      /** 导出按钮操作 */
      handleExport() {
        this.download('api/lingxiao/grade/export', {
          ...this.queryParams
        }, `grade_${new Date().getTime()}.xlsx`)
      }

    }

  }

</script>

<style scoped>
  .demo-table-expand {
    font-size: 0;
  }

  .demo-table-expand label {
    width: 90px;
    color: #99a9bf;
  }

  .demo-table-expand .el-form-item {
    margin-right: 0;
    margin-bottom: 0;
    width: 50%;
  }

  .text {
    font-size: 14px;
  }

  .item {
    margin-bottom: 18px;
  }

  .clearfix:before,
  .clearfix:after {
    display: table;
    content: "";
  }

  .clearfix:after {
    clear: both
  }

  .box-card {
    width: 480px;
  }


  /*设置el-table轻提示的宽度*/
  .el-tooltip__popper {
    max-width: 30%;
  }
</style>
