import request from '@/utils/request'

// 查询SQL日志列表
export function listLog(query, selectVO) {
  return request({
    url: '/api/system/sqlLog/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询SQL日志详细
export function getLog(logId) {
  return request({
    url: '/api/system/sqlLog/' + logId,
    method: 'get'
  })
}

// 新增SQL日志
export function addLog(data) {
  return request({
    url: '/api/system/sqlLog',
    method: 'post',
    data: data
  })
}

// 修改SQL日志
export function updateLog(data) {
  return request({
    url: '/api/system/sqlLog',
    method: 'put',
    data: data
  })
}

// 删除SQL日志
export function delLog(logId) {
  return request({
    url: '/api/system/sqlLog/' + logId,
    method: 'delete'
  })
}

export function getServerIp() {
  return request({
    url: '/api/system/sqlLog/getServerIp/',
    method: 'get'
  })
}

export function getServerPort() {
  return request({
    url: '/api/system/sqlLog/getServerPort/',
    method: 'get'
  })
}
