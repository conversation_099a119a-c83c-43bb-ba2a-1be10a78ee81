import request from "@/utils/request";

// 查询物料分类列表
export function listMaterialCategory(query) {
  return request({
    url: "/api/md/materialcategory/list",
    method: "get",
    params: query,
  });
}

// 查询物料分类详细
export function getMaterialCategory(categoryId) {
  return request({
    url: "/api/md/materialcategory/" + categoryId,
    method: "get",
  });
}

// 新增物料分类
export function addMaterialCategory(data) {
  return request({
    url: "/api/md/materialcategory/",
    method: "post",
    data: data,
  });
}

// 修改物料分类
export function updateMaterialCategory(data) {
  return request({
    url: "/api/md/materialcategory/",
    method: "put",
    data: data,
  });
}

// 删除物料分类
export function delMaterialCategory(categoryId) {
  return request({
    url: "/api/md/materialcategory/" + categoryId,
    method: "delete",
  });
}

export function treeselectByGroup(group) {
  return request({
    url: "/api/md/materialcategory/treeselectByGroup/" + group,
    method: "get",
  });
}

export function treeselect() {
  return request({
    url: "/api/md/materialcategory/treeselect/",
    method: "get",
  });
}

export function treeselectByMaterialCategoryCode(categoryCode) {
  return request({
    url:
      "/api/md/materialcategory/treeselectByMaterialCategoryCode/" +
      categoryCode,
    method: "get",
  });
}

export function moveUp(categoryId) {
  return request({
    url: "/api/md/materialcategory/moveUp/" + categoryId,
    method: "get",
  });
}

export function moveDown(categoryId) {
  return request({
    url: "/api/md/materialcategory/moveDown/" + categoryId,
    method: "get",
  });
}
