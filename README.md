## 一、开发

```bash
# 克隆项目
git clone https://gitee.com/y_project/Lingxiao-Vue

# 进入项目目录
cd lingxiao-ui

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装依赖，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npmmirror.com

# 启动服务
npm run dev
# 启动服务 添加拖动组件
npm install vue-splitpane
#  添加全屏组件
npm install screenfull
#  添加jquery
npm install jquery --save
# 添加滚动组件
npm install --save vue-count-to

#库区图组件
pnpm install vue-grid-layout@2.4.0  --save  
pnpm install --save vue-draggable-resizable-gorkys 
npm install @vxe-ui/plugin-render-element@3.0.0
```

## 二、浏览器访问 http://localhost:81

## 三、node 版本兼容
```json
 "scripts": {
        "dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve",
        "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build",
        "build:stage": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode staging",
        "build:local": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --mode local",
        "preview": "node build/index.js --preview",
        "lint": "eslint --ext .js,.vue src"
    },

```



## 四、发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod

# 德龙表格融合配置
https://vdijl5we41.feishu.cn/docx/NrhMdazoGorexDx5TdycskQMnvJ

# 服务器分布说明：
    正式服务器地址：http://*********:6005/#/login
    测试服务器地址: http://**********:6005/#/index
#正式服务器发布流程
    1、终端录入：npm run build:prod
    2、dist 为打包内容
    3、上传服务器覆盖文件
#测试服务器发布流程
    1、终端录入：npm run build:stage
    2、dist 为打包内容
    3、上传服务器覆盖文件