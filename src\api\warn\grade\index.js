import request from '@/utils/request'

// 查询企业微信机器人等级列表
export function listGrade(query,selectVO) {
  return request({
    url: '/api/TWarnRobotGrade/grade/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询企业微信机器人等级详细
export function getGrade(robotId) {
  return request({
    url: '/api/TWarnRobotGrade/grade/getInfo/' + robotId,
    method: 'get'
  })
}

// 新增企业微信机器人等级
export function addGrade(data) {
  return request({
    url: '/api/TWarnRobotGrade/grade/add',
    method: 'post',
    data: data
  })
}

// 修改企业微信机器人等级
export function updateGrade(data) {
  console.log("query:",JSON.stringify(data))
  return request({
    url: '/api/TWarnRobotGrade/grade/edit',
    method: 'put',
    data: data
  })
}


// 删除企业微信机器人等级
export function delGrade(robotId) {
  return request({
    url: '/api/TWarnRobotGrade/grade/remove/' + robotId,
    method: 'delete'
  })
}

// 批量修改企业微信机器人等级
export function updateGradeAll(robotId) {
  return request({
    url: '/api/TWarnRobotGrade/grade/updateGradeAll/' + robotId,
    method: 'put'
  })
}


export function getProcess(innerCode) {
  return request({
    url: '/api/TWarnRobotGrade/grade/getProcess/' + innerCode,
    method: 'get'
  })
}
export function getFactory(innerCode) {
  return request({
    url: '/api/TWarnRobotGrade/grade/getFactory/' + innerCode,
    method: 'get'
  })
}

export function getContentType(innerCode) {
  return request({
    url: '/api/TWarnRobotGrade/grade/getContentType/' + innerCode,
    method: 'get'
  })
}

export function getpropertys(innerCode) {
  return request({
    url: '/api/TWarnRobotGrade/grade/getpropertys/' + innerCode,
    method: 'get'
  })
}
export function finishWarning(data) {
  return request({
    url: '/api/TWarnRobotGrade/grade/finishWarning',
    method: 'post',
    data: data
  })
}