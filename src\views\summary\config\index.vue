<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">

      <el-form-item label="报表单元编码" prop="reportUnitCode">
        <BReportUnitSelect ref="reportUnitRef" v-model="queryParams.reportUnitCode"
                           :prod-center-code="queryParams.prodCenterCode"
        />
      </el-form-item>
      <el-form-item label="搜索关键字" prop="searchWorld">
        <el-input
          v-model="queryParams.searchWorld"
          clearable
          placeholder="搜索关键字"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
        <el-button
          icon="el-icon-refresh-right"
          plain
          size="mini"
          type="success"
          @click="handleSync"
        >同步存储规则
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div class="prodTable">
          <BProdCenterTree ref="prodTree" :height="productcenterHeight" @currentChange="handleCurrentChange"/>
        </div>

      </el-col>
      <el-col :span="18">
        <vxe-table
          ref="tableMainRef"
          :data="mainTableConfig.tableData"
          :row-config="{isHover: true}"
          :column-config="{resizable: true}"
          border
          @cell-click="cellClickEvent"
          header-align="center"
          height="400"
          stripe
          style="margin-left: 10px"
        >
          <vxe-column align="center" fixed="left" type="checkbox" width="auto"></vxe-column>
          <vxe-column field="prodCenterCode" title="加工中心编码" width="auto"></vxe-column>
          <vxe-column field="prodCenterName" fixed="left" title="加工中心名称" width="auto"></vxe-column>
          <vxe-column field="reportUnitCode" title="报表单元编码" width="auto"></vxe-column>
          <vxe-column field="reportUnitName" fixed="left" title="报表单元名称" width="auto"></vxe-column>
          <vxe-column field="workMode.workModeName" title="班制" width="auto"></vxe-column>
          <vxe-column field="configCode" fixed="left" title="配置编号" width="auto"></vxe-column>
          <vxe-column field="configName" fixed="left" title="配置名称" width="auto"></vxe-column>
          <vxe-column field="analysisMethod" fixed="left" title="分析方式" width="auto"></vxe-column>

          <vxe-column field="resultDetailConfig1" title="结果配置1" width="auto"></vxe-column>
          <vxe-column field="resultDetailCode1" title="结果编号1" width="auto"></vxe-column>
          <vxe-column field="resultDetailName1" title="结果名称1" width="auto"></vxe-column>

          <vxe-column field="resultDetailConfig2" title="结果配置2" width="auto"></vxe-column>
          <vxe-column field="resultDetailCode2" title="结果编号2" width="auto"></vxe-column>
          <vxe-column field="resultDetailName2" title="结果名称2" width="auto"></vxe-column>

          <vxe-column field="resultDetailConfig3" title="结果配置3" width="auto"></vxe-column>
          <vxe-column field="resultDetailCode3" title="结果编号3" width="auto"></vxe-column>
          <vxe-column field="resultDetailName3" title="结果名称3" width="auto"></vxe-column>

          <vxe-column field="tagsGroup" title="标签" width="auto"></vxe-column>
          <vxe-column field="masterGroupRoule" title="分组规则" width="auto"></vxe-column>
          <vxe-column field="finalRoule1" title="值1规则" width="auto"></vxe-column>
          <vxe-column field="finalRoule2" title="值2规则" width="auto"></vxe-column>


          <vxe-column field="remark" title="备注" width="auto"></vxe-column>
          <vxe-column field="configStatus" title="状态" width="auto"></vxe-column>
          <vxe-column field="lastSumNode" title="最后采集" width="auto"></vxe-column>
          <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
          <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
          <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
          <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>
        </vxe-table>
        <vxe-pager
          :current-page.sync="mainTableConfig.pageConfig.pageNum"
          :page-size.sync="mainTableConfig.pageConfig.pageSize"
          :total="mainTableConfig.pageConfig.total"
          @page-change="pageChange"
        >
        </vxe-pager>
        <div class="detial_table">
          <vxe-table
            ref="tableDetailRef"
            :data="detailTableConfig.tableData"
            :height="detilHeight"
            :row-config="{isHover: true}"
            :column-config="{resizable: true}"
            border
            header-align="center"
            stripe
            style="margin-left: 10px; margin-top: 10px"
          >
            <vxe-column field="tCollectPoint.pointProcess" width="auto" title="点位工序"></vxe-column>
            <vxe-column field="tCollectPoint.pointOperation" width="auto" title="点位业务"></vxe-column>
            <vxe-column field="tCollectPoint.pointCode" width="auto" title="点位编号"></vxe-column>
            <vxe-column field="tCollectPoint.pointName" width="auto" title="点位名称"></vxe-column>
            <vxe-column field="detailType" title="明细性质" width="auto"></vxe-column>
            <vxe-column field="groupName" title="分组名称" width="auto"></vxe-column>
            <vxe-column field="sortNumber" title="序号" width="auto"></vxe-column>
            <vxe-column field="tCollectPoint.pointDesc" width="auto" title="点位描述" show-overflow></vxe-column>
            <vxe-column field="tCollectPoint.storeCode" width="auto" title="存储编号"></vxe-column>
            <vxe-column field="tCollectPoint.startCollectionTime" width="auto" title="开始采集时间"></vxe-column>

          </vxe-table>
        </div>
      </el-col>
    </el-row>


    <!-- 添加或修改汇总配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="900px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">

        <el-row>
          <el-col :span="8">
            <el-form-item label="加工中心" prop="prodCenterCode">
              <BProdCenterTreeSelect @currentChange="editPordCenterChange" ref="prodCenterRef"
                                     v-model="form.prodCenterCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="报表单元" prop="reportUnitCode">
              <BReportUnitSelect ref="reportUnitRef" :prod-center-code="form.prodCenterCode"
                                 v-model="form.reportUnitCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="班制" prop="workModeId">
              <BWorkModeSelect v-model="form.workModeId"/>
            </el-form-item>
          </el-col>
        </el-row>


        <el-row>
          <el-col :span="12">
            <el-form-item label="配置编号" prop="configCode">
              <el-input v-model="form.configCode" placeholder="请输入配置编号">
                <el-button icon="el-icon-setting" size="mini" slot="append" type="primary" @click="createCode"
                ></el-button>
              </el-input>

            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="配置名称" prop="configName">
              <el-input v-model="form.configName" placeholder="请输入配置名称"/>
            </el-form-item>
          </el-col>

        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="分组规则" prop="masterGroupRoule">
              <el-select v-model="form.masterGroupRoule" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleTypeList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="值1规则" prop="finalRoule1">
              <el-select v-model="form.finalRoule1" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleValueList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="值2规则" prop="finalRoule2">
              <el-select v-model="form.finalRoule2" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleValueList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分析方式" prop="analysisMethod">
              <el-select v-model="form.analysisMethod" clearable placeholder="请选择采集值" style="width: 100%;">
                <el-option v-for="dict in editAnalysisMethodList" :key="dict.key" :label="dict.value"
                           :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="标签" prop="tagsGroup">
              <template #label>
                <span>标签</span>
                <el-tooltip content="多个用逗号分开" placement="top">
                  <i class="el-icon-question" style="margin-left: 5px;"></i>
                </el-tooltip>
              </template>
              <el-input v-model="form.tagsGroup" placeholder="多个用逗号分开"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="结果配置1" prop="resultDetailConfig1">
              <el-select v-model="form.resultDetailConfig1" clearable placeholder="请选择采集值" style="width: 100%;">
                <el-option v-for="dict in editResultDetailConfigList" :key="dict.key" :label="dict.value"
                           :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号1" prop="resultDetailCode1">
              <el-input v-model="form.resultDetailCode1" :disabled="form.resultDetailConfig1 != '固定值'"
                        placeholder="请输入结果编号1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称1" prop="resultDetailName1">
              <el-input v-model="form.resultDetailName1" :disabled="form.resultDetailConfig1 != '固定值'"
                        placeholder="请输入结果名称1"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="结果配置2" prop="resultDetailConfig2">
              <el-select v-model="form.resultDetailConfig2" clearable placeholder="请选择采集值" style="width: 100%;">
                <el-option v-for="dict in editResultDetailConfigList" :key="dict.key" :label="dict.value"
                           :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号2" prop="resultDetailCode2">
              <el-input v-model="form.resultDetailCode2" :disabled="form.resultDetailConfig2 != '固定值'"
                        placeholder="请输入结果编号2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称2" prop="resultDetailName2">
              <el-input v-model="form.resultDetailName2" :disabled="form.resultDetailConfig2 != '固定值'"
                        placeholder="请输入结果名称2"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="结果配置3" prop="resultDetailConfig3">
              <el-select v-model="form.resultDetailConfig3" clearable placeholder="请选择采集值" style="width: 100%;">
                <el-option v-for="dict in editResultDetailConfigList" :key="dict.key" :label="dict.value"
                           :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号3" prop="resultDetailCode3">
              <el-input v-model="form.resultDetailCode3" :disabled="form.resultDetailConfig3 != '固定值'"
                        placeholder="请输入结果编号3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称3" prop="resultDetailName3">
              <el-input v-model="form.resultDetailName3" :disabled="form.resultDetailConfig3 != '固定值'"
                        placeholder="请输入结果名称3"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>

          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" placeholder="请输入内容"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-card>
          <div slot="header" class="clearfix">
            <span>点位设置</span>
            <el-button-group style="float: right;">
              <el-button icon="el-icon-plus" size="mini" type="primary" @click="addPointClick">新增</el-button>
              <el-button icon="el-icon-delete" size="mini" type="danger" @click="clearPointClick">清空</el-button>
            </el-button-group>
          </div>
          <div class="mes_table">
            <el-table :border="true" :data="form.detailList" max-height="400" size="mini" style="width: 100%">
              <el-table-column align="center" fixed="left" label="点位编号" min-width="150"
                               prop="tCollectPoint.pointCode"
              />
              <el-table-column align="center" fixed="left" label="点位名称" min-width="150"
                               prop="tCollectPoint.pointName"
              />
              <el-table-column align="center" fixed="left" label="明细性质" min-width="150" prop="detailType">
                <template slot-scope="scope">
                  <el-select v-model="scope.row.detailType" placeholder="请选择采集值">
                    <el-option v-for="dict in editDetailTypeList" :key="dict.key" :label="dict.value"
                               :value="dict.key"
                    ></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column align="center" fixed="left" label="分组名称" min-width="150" prop="groupName">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.groupName" placeholder="请输入分组名称"/>
                </template>
              </el-table-column>

              <el-table-column align="center" label="点位工序" min-width="130" prop="tCollectPoint.pointProcess"/>
              <el-table-column align="center" label="点位业务" min-width="130" prop="tCollectPoint.pointOperation"/>
              <el-table-column align="center" label="点位描述" min-width="130" prop="tCollectPoint.pointDesc"
                               show-overflow-tooltip
              />
              <el-table-column align="center" label="存储编号" min-width="130" prop="tCollectPoint.storeCode"/>
              <el-table-column align="center" label="开始采集时间" min-width="130"
                               prop="tCollectPoint.startCollectionTime"
              />
            </el-table>
          </div>
        </el-card>


      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <BCollPointDialog :is-open="isOpenPoint" @close="pointDialogClose" @submit="pointSubmit"/>
  </div>
</template>

<script>
import {
  listConfig,
  getConfig,
  delConfig,
  saveOrUpdate,
  analysisMethod,
  resultDetailConfig,
  detailType,
  createConfigCode,
  syncStoreRule
} from '@/api/summary/config'

import { getByConfigID, getDetail } from '@/api/summary/detail'
import {
  getRuleType,
  getRuleValue
} from '@/api/summary/storageRule'

import BCollPointDialog from '@/components/BCollPointDialog/index.vue'
import BProdCenterTree from '@/components/BProdCenterTree/index.vue'
import BProdCenterTreeSelect from '@/components/BProdCenterTreeSelect/index.vue'
import BReportUnitSelect from '@/components/BReportUnitSelect/index.vue'
import BWorkModeSelect from '@/components/BWorkModeSelect/index.vue'

export default {
  name: 'SummaryMaster',
  components: { BReportUnitSelect, BProdCenterTreeSelect, BProdCenterTree, BCollPointDialog, BWorkModeSelect },
  data() {
    return {
      isOpenPoint: false,
      // 遮罩层
      loading: true,

      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 汇总配置表格数据
      configList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      RuleTypeList: [],
      RuleValueList: [],
      editDetailTypeList: [],
      editAnalysisMethodList: [],
      editResultDetailConfigList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        reportUnitCode: null,
        searchWorld: null
      },
      // 表单参数
      form: {
        detailList: []
      },
      // 表单校验
      rules: {
        prodCenterCode: [
          {
            required: true, message: '加工中心 不能为空', trigger: 'blur'
          }
        ],
        reportUnitCode: [
          {
            required: true, message: '报表单元 不能为空', trigger: 'blur'
          }
        ],

        configCode: [
          {
            required: true, message: '配置编号 不能为空', trigger: 'blur'
          }
        ],
        configName: [
          {
            required: true, message: '配置名称 不能为空', trigger: 'blur'
          }
        ],
        analysisMethod: [
          {
            required: true, message: '分析方式 不能为空', trigger: 'blur'
          }
        ],
        resultDetailConfig1: [
          {
            required: true, message: '结果配置1 不能为空', trigger: 'blur'
          }
        ],
        resultDetailConfig2: [
          {
            required: true, message: '结果配置2 不能为空', trigger: 'blur'
          }
        ],
        resultDetailConfig3: [
          {
            required: true, message: '结果配置3 不能为空', trigger: 'blur'
          }
        ],
        masterGroupRoule: [
          {
            required: true, message: '分组规则 不能为空', trigger: 'blur'
          }
        ],
        finalRoule1: [
          {
            required: true, message: '值1规则 不能为空', trigger: 'blur'
          }
        ],
        finalRoule2: [
          {
            required: true, message: '值1规则 不能为空', trigger: 'blur'
          }
        ]

      },
      detilHeight: 300,
      productcenterHeight: 500,
      productcenterList: [],
      currentProdCenter: null,
      mainTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      detailTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }

      }
    }
  },
  created() {
    detailType().then((response) => {
      this.editDetailTypeList = response.data
    })
    analysisMethod().then((response) => {
      this.editAnalysisMethodList = response.data
    })
    resultDetailConfig().then((response) => {
      this.editResultDetailConfigList = response.data
    })
    getRuleType().then((response) => {
      this.RuleTypeList = response.data
    })
    getRuleValue().then((response) => {
      this.RuleValueList = response.data
    })

  }
  ,
  methods: {
    pageChange({ pageSize, currentPage }) {
      this.mainTableConfig.pageConfig.pageNum = currentPage
      this.mainTableConfig.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTableConfig.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTableConfig.pageConfig.pageSize
      this.queryList()
    },

    /** 查询汇总配置列表 */
    getList(selectVO) {
      this.loading = true
      if (selectVO) {
        this.selectVO = selectVO
      }
      this.queryList()
    }
    ,
    queryList() {
      this.mainTableConfig.tableData = []
      this.detailTableConfig.tableData = []
      if (this.currentProdCenter != null) {
        this.queryParams.prodCenterCode = this.currentProdCenter.prodCenterCode
      }

      listConfig(this.queryParams, this.selectVO).then(response => {
        this.mainTableConfig.tableData = response.rows
        this.mainTableConfig.pageConfig.total = response.total
        if (this.mainTableConfig.tableData != null && this.mainTableConfig.tableData.length > 0) {
          this.queryDetail(this.mainTableConfig.tableData[0].configId)
        }

      })
    },
    queryDetail(configId) {
      this.detailTableConfig.tableData = []
      getByConfigID(configId).then(response => {
        this.detailTableConfig.tableData = response.data
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    }
    ,
    // 表单重置
    reset() {
      this.form = {
        configId: null,
        prodCenterCode: null,
        prodCenterName: null,
        reportUnitCode: null,
        reportUnitName: null,
        workModeId: null,
        configCode: null,
        configName: null,
        analysisMethod: null,
        resultDetailConfig1: null,
        resultDetailCode1: null,
        resultDetailName1: null,
        resultDetailConfig2: null,
        resultDetailCode2: null,
        resultDetailName2: null,
        resultDetailConfig3: null,
        resultDetailCode3: null,
        resultDetailName3: null,
        tagsGroup: null,
        detailList: []
      }
      this.resetForm('form')
    }
    ,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    }
    ,
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
    ,
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.configId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    }
    ,
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.masterGroupRoule = '无值'
      this.form.finalRoule1 = '数字'
      this.form.finalRoule2 = '无值'
      this.form.resultDetailConfig1 = '无值'
      this.form.resultDetailConfig2 = '无值'
      this.form.resultDetailConfig3 = '无值'
      this.open = true
      this.title = '添加汇总配置'
    }
    ,
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }
      if (selectedRows.length != 1) {
        this.$message.warning('同时只能对一掉数据进行修改')
        return
      }
      getConfig(selectedRows[0].configId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改汇总配置'
      })
    }
    ,
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          saveOrUpdate(this.form).then(response => {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.open = false
            this.getList(null)
          }).catch(error => {

          })
        }
      })
    },
    handleSync(row) {
      this.$modal
        .confirm('是否确认同步？')
        .then(function() {
          return syncStoreRule()
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('同步成功')
        })
        .catch(() => {
        })
    }
    ,
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      const configIds = selectedRows.map(item => item.configId)
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delConfig(configIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    }
    ,
    handleCurrentChange(selection) {
      this.currentProdCenter = selection
      this.form.detailList = selection.prodCenterCode
      this.queryParams.prodCenterCode = selection.prodCenterCode
      this.getList(null)
    }
    ,
    createCode() {
      if (this.form.prodCenterCode == null) {
        this.$message({
          message: '请选择加工中心',
          type: 'warning'
        })
        return
      }
      if (this.form.reportUnitCode == null) {
        this.$message({
          message: '请选择报表单元',
          type: 'warning'
        })
        return
      }

      var par = { prodCenterCode: this.form.prodCenterCode, reportUnitCode: this.form.reportUnitCode }
      createConfigCode(par).then(res => {
        this.form.configCode = res.data
      })
    },
    addPointClick() {
      this.isOpenPoint = false
      this.isOpenPoint = true
    }
    ,
    pointDialogClose() {
      this.isOpenPoint = false
    }
    ,
    clearPointClick() {
      this.form.detailList.splice(0, this.form.detailList.length)
    }
    ,
    pointSubmit(pointList) {
      if (pointList == null || pointList.length === 0) {
        this.$message({
          message: '请选择点位',
          type: 'warning'
        })
        return
      }

      //  不在进行重复点位的验证，部分情况先会有点位共用的情况
      // for (var i = 0; i < pointList.length; i++) {
      //   var point = pointList[i]
      //
      //   if (this.form.detailList != null && this.form.detailList.length > 0) {
      //     var checkItem = this.form.detailList.filter(item => item.pointId === point.pointId)
      //
      //     if (checkItem != null && checkItem.length > 0) {
      //       this.$message({
      //         message: '已添加过该点位' + point.pointName,
      //         type: 'warning'
      //       })
      //       return
      //     }
      //   }
      //
      // }

      pointList.forEach(item => {
        var detail = {
          pointId: item.pointId,
          tCollectPoint: item
        }
        this.form.detailList.push(detail)
      })
    }
    ,
    editPordCenterChange(val) {
      this.$refs.reportUnitRef.clearSelect()
    },
    cellClickEvent({ row, column }) {
      this.queryDetail(row.configId)
    }
  },

  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('prodTable')[0].getBoundingClientRect().top
      this.productcenterHeight = document.body.clientHeight - topValue - 10

      topValue = document.getElementsByClassName('detial_table')[0].getBoundingClientRect().top
      this.detilHeight = document.body.clientHeight - topValue - 10
    })
  }
}

</script>
