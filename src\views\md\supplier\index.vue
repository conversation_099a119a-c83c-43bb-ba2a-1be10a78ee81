<template>
  <div class="app-container">


    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <div>
      <dlTable size="small" refName="supplierTable" :stripe="true" :border="true" :height="height" :columns="columns"
        :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig" @handleOrder="handleOrder"
        @handleFilter="handleFilter" @selection-change="handleSelectionChange" @size-change="sizeChange"
        @page-current-change="numChange">
      </dlTable>
    </div>



    <!-- 添加或修改供应商对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="750px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="企业编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业简称" prop="name">
              <el-input v-model="form.name" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="企业全称" prop="fullName">
              <el-input v-model="form.fullName" placeholder="请输入全称" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="外文名称" prop="foreignName">
              <el-input v-model="form.foreignName" placeholder="请输入外文名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="助记码" prop="mnemonicCode">
              <el-input v-model="form.mnemonicCode" placeholder="请输入助记码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="ERP_ID" prop="erpId">
              <el-input v-model="form.erpId" placeholder="请输入ERP_ID" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="法人代表" prop="artificialPerson">
              <el-input v-model="form.artificialPerson" placeholder="请输入法人代表" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="经营许可证" prop="license">
              <el-input v-model="form.license" placeholder="请输入生产经营许可证" />
            </el-form-item>

          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="营业执照" prop="busLicence">
              <el-input v-model="form.busLicence" placeholder="请输入营业执照" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税务登记号" prop="txRegisterNo">
              <el-input v-model="form.txRegisterNo" placeholder="请输入税务登记号" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="工商注册号" prop="bizRegisterNo">
              <el-input v-model="form.bizRegisterNo" placeholder="请输入工商注册号" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="信用代码" prop="societyCreditCode">
              <el-input v-model="form.societyCreditCode" placeholder="请输入社会统一信用代码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="企业地址" prop="adress">
          <el-input v-model="form.adress" placeholder="请输入企业地址" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSupplier, getSupplier, delSupplier, addSupplier, updateSupplier, saveOrUpdate } from "@/api/md/supplier";

export default {
  name: "Supplier",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 供应商表格数据
      supplierList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        code: null, name: null, status: null, auditBy: null, auditTime: null, societyCreditCode: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        code: [
          { required: true, message: "企业编码不能为空", trigger: "blur" },
        ],
        name: [
          { required: true, message: "企业简称不能为空", trigger: "blur" },
        ],
        fullName: [
          { required: true, message: "企业全称不能为空", trigger: "blur" },
        ],
      },
      basicConfig: {
        index: true, // 是否启用序号列
        needPage: true, // 是否展示分页
        indexName: null, // 序号列名(默认为：序号)
        selectionType: true, // 是否启用多选框
        indexWidth: null, // 序号列宽(默认为：50)
        indexFixed: null, // 序号列定位(默认为：left)
        settingType: true, // 是否展示表格配置按钮
        headerSortSaveType: true // 表头排序是否保存在localStorage中
      },
      pageConfig: {
        pageNum: 1, // 页码
        pageSize: 10, // 每页显示条目个数
        total: 0, // 总数
        background: true, // 是否展示分页器背景色
        pageSizes: [10, 20, 50, 100]// 分页器分页待选项
      },
      columns: [
        {
          label: '企业编码', // 表头描述
          fieldIndex: 'code', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '企业简称', // 表头描述
          fieldIndex: 'name', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '企业全称', // 表头描述
          fieldIndex: 'fullName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '外文名称', // 表头描述
          fieldIndex: 'foreignName', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },

        {
          label: '助记码', // 表头描述
          fieldIndex: 'mnemonicCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: 'ERP_ID', // 表头描述
          fieldIndex: 'erpId', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '法人代表', // 表头描述
          fieldIndex: 'artificialPerson', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '经营许可证', // 表头描述
          fieldIndex: 'license', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '营业执照', // 表头描述
          fieldIndex: 'busLicence', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },

        {
          label: '税务登记号', // 表头描述
          fieldIndex: 'txRegisterNo', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '工商注册号', // 表头描述
          fieldIndex: 'bizRegisterNo', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '信用代码', // 表头描述
          fieldIndex: 'societyCreditCode', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '地址', // 表头描述
          fieldIndex: 'adress', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '备注', // 表头描述
          fieldIndex: 'remark', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建人', // 表头描述
          fieldIndex: 'createBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '创建时间', // 表头描述
          fieldIndex: 'createTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '修改人', // 表头描述
          fieldIndex: 'updateBy', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        },
        {
          label: '修改时间', // 表头描述
          fieldIndex: 'updateTime', // 表格显示内容绑定值
          width: 130,
          sortable: true, // 此属性可以设置排序
          filterable: true, // 是否筛选 默认为false
          searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
          concise: true, // 是否显示筛选icon 和排序 icon
        }
      ],
      tableData: [],
      selectVO: '',
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleOrder(selectVO) {
      this.selectVO = selectVO
      listSupplier(this.queryParams, this.selectVO).then(response => {
        // this.supplierList = response.data.list;
        // this.total = response.data.total;
        this.tableData = response.data.list;
        this.pageConfig.total = response.data.total;
        this.loading = false;
      });
    },
    handleFilter(selectVO) {
      this.selectVO = selectVO
      listSupplier(this.queryParams, this.selectVO).then(response => {
        // this.supplierList = response.data.list;
        // this.total = response.data.total;
        this.tableData = response.data.list;
        this.pageConfig.total = response.data.total;
        this.loading = false;
      });
    },
    /** 查询供应商列表 */
    getList() {
      this.loading = true;
      listSupplier(this.queryParams, this.selectVO).then(response => {
        // this.supplierList = response.data.list;
        // this.total = response.data.total;
        this.tableData = response.data.list;
        this.pageConfig.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        code: null,
        name: null,
        societyCreditCode: null,
        busLicence: null,
        mnemonicCode: null,
        foreignName: null,
        artificialPerson: null,
        adress: null,
        txRegisterNo: null,
        bizRegisterNo: null,
        license: null,
        erpId: null,
        fullName: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加供应商";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getSupplier(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改供应商";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          saveOrUpdate(this.form).then((response) => {
            this.$modal.msgSuccess("保存成功");
            this.open = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除供应商编号为"' + ids + '"的数据项？').then(function () {
        return delSupplier(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** pageNum事件 */
    numChange(pageNum, selectVO) {
      this.pageConfig.pageNum = pageNum
      this.queryParams.pageNum = pageNum
      this.selectVO = selectVO
      listSupplier(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.data.list;
        this.pageConfig.total = response.data.total;
        this.loading = false;
      });
    },
    /** pageSize事件 */
    sizeChange(pageSize, selectVO) {
      this.pageConfig.pageSize = pageSize
      this.queryParams.pageSize = pageSize
      this.selectVO = selectVO
      listSupplier(this.queryParams, this.selectVO).then(response => {
        this.tableData = response.data.list;
        this.pageConfig.total = response.data.total;
        this.loading = false;
      });
    }
  }
};
</script>
<style scoped>
p {
  padding: 0;
  margin: 0;
}
</style>
