import request from '@/utils/request'

// 查询生产信息综合管理列表
export function listInfo(query,selectVO) {
  return request({
    url: '/api/cpes/generalInfo/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询生产信息综合管理详细
export function getInfo(generalInfoId) {
  return request({
    url: '/api/cpes/generalInfo/' + generalInfoId,
    method: 'get'
  })
}

// 新增生产信息综合管理
export function addInfo(data) {
  return request({
    url: '/api/cpes/generalInfo',
    method: 'post',
    data: data
  })
}

// 修改生产信息综合管理
export function updateInfo(data) {
  return request({
    url: '/api/cpes/generalInfo',
    method: 'put',
    data: data
  })
}

// 删除生产信息综合管理
export function delInfo(generalInfoId) {
  return request({
    url: '/api/cpes/generalInfo/' + generalInfoId,
    method: 'delete'
  })
}

export function queryForManger(query) {
  return request({
    url: '/api/cpes/generalInfo/queryForManger',
    method: 'get',
    params: query,
  })
}

export function async(query) {
  return request({
    url: '/api/cpes/generalInfo/async',
    method: 'get',
    params: query,
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/generalInfo/saveOrUpdate',
    method: 'post',
    data: data
  })
}
