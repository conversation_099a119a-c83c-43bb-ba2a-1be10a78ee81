import request from '@/utils/request'

// 查询供应商-物料关系列表
export function listMaterial(query,selectVO) {
  return request({
    url: '/api/md/supplierMaterial/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询供应商-物料关系详细
export function getMaterial(id) {
  return request({
    url: '/api/md/supplierMaterial/' + id,
    method: 'get'
  })
}

// 新增供应商-物料关系
export function addMaterial(data) {
  return request({
    url: '/api/md/supplierMaterial',
    method: 'post',
    data: data
  })
}

// 新增供应商-物料关系
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/supplierMaterial/saveOrUpdate',
    method: 'post',
    data: data
  })
}
// 修改供应商-物料关系
export function updateMaterial(data) {
  return request({
    url: '/api/md/supplierMaterial',
    method: 'put',
    data: data
  })
}

// 删除供应商-物料关系
export function delMaterial(id) {
  return request({
    url: '/api/md/supplierMaterial/' + id,
    method: 'delete'
  })
}
