<template>
    <div style="margin: 10px;">
        <vxe-grid v-bind="gridOptions" style="margin: 10px;" @page-change="handlePageChange"
            @checkbox-change="handleSelectChange" @checkbox-all="handleSelectChange">
            <!-- 搜索表单 -->
            <template #form>
                <vxe-form ref="searchFormRef" v-bind="formOptions">
                    <template #action>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="handleReset">重置</el-button>
                    </template>
                </vxe-form>
            </template>

            <!-- 工具栏 -->
            <template #toolbar>
                <el-button size="mini" plain type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
                <el-button size="mini" plain type="warning" icon="el-icon-edit" @click="handleEdit" :disabled="!canEdit">修改</el-button>
                <el-button size="mini" plain type="danger" icon="el-icon-delete" @click="handleBatchDelete"
                    :disabled="selectedRows.length === 0">删除</el-button>
            </template>
        </vxe-grid>

        <!-- 新增/修改表单弹窗 -->
        <vxe-modal v-model="showFormModal" :title="formTitle" width="800px" :mask-closable="false"
            :loading="formLoading" @close="handleCancelForm">
            <vxe-form ref="ruleFormRef" v-bind="ruleFormOptions" :rules="formRules">
                <template #action>
                    <vxe-button status="primary" @click="handleSubmitForm">确定</vxe-button>
                    <vxe-button @click="handleCancelForm">取消</vxe-button>
                </template>
            </vxe-form>
        </vxe-modal>
    </div>
</template>

<script>
import { getRuleList, insertRuleData, updateRuleData, deleteRuleData } from "@/api/feedingplan/guidelineRule";

export default {
    name: 'RuleManagement',
    data() {
        return {
            // 表格配置
            gridOptions: {
                columns: [],
                data: [],
                border: true,
                stripe: true,
                align: 'center',
                height: 800,
                loading: false,
                columnConfig: { resizable: true },
                rowConfig: { isHover: true, isCurrent: true },
                mouseConfig: { selected: true },
                checkboxConfig: {
                    checkField: "checked",
                    highlight: true,
                    range: true,
                    trigger: "click"
                },
                pagerConfig: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                    pageSizes: [10, 20, 50, 100, 500] // 增加分页大小选择
                },
                toolbarConfig: {
                    custom: true,
                    zoom: true,
                    slots: { buttons: 'toolbar' }
                }
            },

            // 搜索表单配置
            formOptions: {
                data: {
                    name: '',
                    prodCenterCode: ''
                },
                items: [
                    { field: 'name', title: '项目名称', itemRender: { name: 'VxeInput' } },
                    {
                        field: 'prodCenterCode',
                        title: '加工中心',
                        itemRender: {
                            name: 'VxeSelect',
                            options: [
                                { label: '1#高炉', value: 'IPES01' },
                                { label: '2#高炉', value: 'IPES02' }
                            ],
                            props: {
                                placeholder: '请选择加工中心'
                            }
                        }
                    },
                    { slots: { default: 'action' } }
                ]
            },

            // 规则表单配置
            ruleFormOptions: {
                data: this.getEmptyRuleForm(),
                items: [
                    {
                        field: 'prodCenterCode',
                        title: '生产中心',
                        itemRender: {
                            name: 'VxeSelect',
                            options: [
                                { label: '1#高炉', value: 'IPES01' },
                                { label: '2#高炉', value: 'IPES02' }
                            ]
                        },
                        span: 12,
                        required: true
                    },
                    {
                        field: 'name',
                        title: '项目名称',
                        itemRender: { name: 'VxeInput' },
                        span: 12,
                        required: true
                    },
                    { field: 'lowLimit', title: '下限值', itemRender: { name: 'VxeInput' }, span: 12 },
                    {
                        field: 'lowComparator',
                        title: '下限值比较符',
                        itemRender: {
                            name: 'VxeSelect',
                            options: [
                                { label: '大于', value: '>' },
                                { label: '大于等于', value: '>=' },
                                // { label: '小于', value: '<' },
                                // { label: '小于等于', value: '<=' },
                                { label: '等于', value: '=' }
                            ]
                        },
                        span: 12
                    },
                    { field: 'upLimit', title: '上限值', itemRender: { name: 'VxeInput' }, span: 12 },
                    {
                        field: 'upComparator',
                        title: '上限值比较符',
                        itemRender: {
                            name: 'VxeSelect',
                            options: [
                                // { label: '大于', value: '>' },
                                // { label: '大于等于', value: '>=' },
                                { label: '小于', value: '<' },
                                { label: '小于等于', value: '<=' },
                                { label: '等于', value: '=' }
                            ]
                        },
                        span: 12
                    },
                    { field: 'baseValue', title: '基准值', itemRender: { name: 'VxeInput' }, span: 12 },
                    { field: 'tolerance', title: '公差', itemRender: { name: 'VxeInput' }, span: 12 },
                    { slots: { default: 'action' } }
                ]
            },

            // 表单验证规则
            formRules: {
                name: [
                    { required: true, message: '请输入项目名称', trigger: 'blur' }
                ],
                prodCenterCode: [
                    { required: true, message: '请选择生产中心', trigger: 'change' }
                ],
                lowLimit: [
                    { pattern: /^$|^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
                ],
                upLimit: [
                    { pattern: /^$|^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
                ],
                baseValue: [
                    { pattern: /^$|^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
                ],
                tolerance: [
                    { pattern: /^$|^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
                ]
            },

            // 状态变量
            selectedRows: [],
            showFormModal: false,
            formTitle: '新增规则',
            formLoading: false,
            isEdit: false,
            isSubmitting: false
        };
    },

    computed: {
        // 只有选中一条数据时可编辑
        canEdit() {
            return this.selectedRows.length === 1;
        },
        // 获取当前生产中心编码
        currentProdCenterCode() {
            return this.$route.query.prodCenterCode || '';
        }
    },

    mounted() {
        this.initGridColumns();
        this.loadTableData();
    },

    methods: {
        // 获取空表单对象
        getEmptyRuleForm() {
            return {
                id: '',
                prodCenterCode: this.currentProdCenterCode,
                name: '',
                lowLimit: '',
                lowComparator: '',
                upLimit: '',
                upComparator: '',
                baseValue: '',
                tolerance: ''
            };
        },



        // 初始化表格列
        initGridColumns() {
            this.gridOptions.columns = [
                { type: 'checkbox', width: 50 },
                { type: 'seq', width: 50 },
                { field: 'prodCenterCode', title: '生产中心编码' },
                { field: 'prodCenterName', title: '生产中心名称' }, // 新增
                { field: 'name', title: '项目' },
                { field: 'lowLimit', title: '下限值' },
                { field: 'lowComparator', title: '下限值比较符' },
                { field: 'upLimit', title: '上限值' },
                { field: 'upComparator', title: '上限值比较符' },
                { field: 'baseValue', title: '基准值' },
                { field: 'tolerance', title: '公差' }
            ];
        },

        // 加载表格数据
        async loadTableData() {
            try {
                this.gridOptions.loading = true;
                const response = await getRuleList(this.formOptions.data);
                const { currentPage, pageSize } = this.gridOptions.pagerConfig;
                const total = response.data?.length || 0;
                this.gridOptions.pagerConfig.total = total;
                this.gridOptions.data = this.handlePagination(response.data || [], currentPage, pageSize);
            } catch (error) {
                console.error('加载数据失败:', error);
                this.$message.error('加载数据失败，请重试');
            } finally {
                this.gridOptions.loading = false;
            }
        },

        // 处理分页逻辑
        handlePagination(data, currentPage, pageSize) {
            const startIndex = (currentPage - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            return data.slice(startIndex, endIndex);
        },

        // 分页变化处理
        handlePageChange({ currentPage, pageSize }) {
            this.gridOptions.pagerConfig.currentPage = currentPage;
            this.gridOptions.pagerConfig.pageSize = pageSize;
            this.loadTableData();
        },

        // 搜索事件
        handleSearch() {
            this.gridOptions.pagerConfig.currentPage = 1;
            this.loadTableData();
            this.clearSelection();
        },

        // 重置事件
        handleReset() {
            const $form = this.$refs.searchFormRef;
            if ($form) {
                $form.reset();
                this.handleSearch();
            }
        },

        // 选择变化事件
        handleSelectChange({ records }) {
            this.selectedRows = records || [];
        },

        // 清空选择
        clearSelection() {
            this.selectedRows = [];
            this.$refs.tableRef?.setCheckboxRow([]);
        },

        // 新增事件
        handleAdd() {
            this.isEdit = false;
            this.formTitle = '新增规则';
            this.ruleFormOptions.data = this.getEmptyRuleForm();
            this.showFormModal = true;
        },

        // 编辑事件
        handleEdit() {
            if (!this.canEdit) return;

            this.isEdit = true;
            this.formTitle = '修改规则';
            // 深拷贝选中的数据，避免直接修改表格数据
            this.ruleFormOptions.data = { ...this.selectedRows[0] };
            this.showFormModal = true;
        },

        // 提交表单
        async handleSubmitForm() {
            if (this.isSubmitting) return;

            try {
                this.isSubmitting = true;
                console.log('表单数据:', this.ruleFormOptions.data);
                this.formLoading = true;
                // 数据预处理 - 转换数字字段
                const formData = { ...this.ruleFormOptions.data };
                if (this.isEdit) {
                    await updateRuleData(formData);
                    this.$message.success('修改成功');
                } else {
                    await insertRuleData(formData);
                    this.$message.success('新增成功');
                }
                this.showFormModal = false;
                this.loadTableData();
            } catch (error) {
                console.error('表单提交失败:', error);
                this.$message.error(this.isEdit ? '修改失败' : '新增失败');
            } finally {
                this.formLoading = false;
                this.isSubmitting = false;
            }
        },

        // 取消表单
        handleCancelForm() {
            this.showFormModal = false;
            const ruleForm = this.$refs.ruleFormRef;
            if (ruleForm) {
                ruleForm.reset(); // 重置表单数据和验证状态
            }
        },

        // 批量删除
        async handleBatchDelete() {
            if (this.selectedRows.length === 0) return;

            try {
                const confirm = await this.$modal.confirm({
                    title: '删除确认',
                    content: `确定要删除选中的${this.selectedRows.length}条数据吗？`,
                    confirmButtonText: '确认',
                    cancelButtonText: '取消',
                    type: 'warning'
                });

                if (confirm) {
                    const ids = this.selectedRows.map(row => row.id);
                    await deleteRuleData(ids);
                    this.$message.success('删除成功');
                    this.loadTableData();
                    this.clearSelection();
                }
            } catch (error) {
                if (error !== 'cancel') { // 排除取消操作的错误
                    console.error('删除失败:', error);
                    this.$message.error('删除失败');
                }
            }
        }
    }
};
</script>