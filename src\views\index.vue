<template>
    <div class="app-container">
        <el-row :gutter="20">
            <el-col :span="6"
                    :xs="24">
                <!-- 个人信息 -->
                <el-card >
                    <div slot="header"
                         class="clearfix">
                        <span>个人信息</span>
                    </div>
                    <div>
                        <div class="text-center">
                            <userAvatar :user="user" />
                        </div>
                        <ul class="list-group list-group-striped">
                            <li class="list-group-item">
                                <svg-icon icon-class="user" />用户名称
                                <div class="pull-right">{{ user.userName }}</div>
                            </li>
                            <li class="list-group-item">
                                <svg-icon icon-class="phone" />手机号码
                                <div class="pull-right">{{ user.phonenumber }}</div>
                            </li>
                            <li class="list-group-item">
                                <svg-icon icon-class="email" />用户邮箱
                                <div class="pull-right">{{ user.email }}</div>
                            </li>
                            <li class="list-group-item">
                                <svg-icon icon-class="tree" />所属部门
                                <div class="pull-right"
                                     v-if="user.dept">{{ user.dept.deptName }} / {{ postGroup }}</div>
                            </li>
                            <li class="list-group-item">
                                <svg-icon icon-class="peoples" />所属角色
                                <div class="pull-right">{{ roleGroup }}</div>
                            </li>
                        </ul>
                    </div>
                </el-card>
            </el-col>
            <!-- 基本资料 -->
            <el-col :span="6"
                    :xs="24">
                <el-card>
                    <div slot="header"
                         class="clearfix">
                        <span>基本资料</span>
                    </div>
                    <el-tabs v-model="activeTab"
                             class="mar-b">
                        <el-tab-pane label="基本资料"
                                     name="userinfo">
                            <userInfo :user="user" />
                        </el-tab-pane>
                        <el-tab-pane label="修改密码"
                                     name="resetPwd">
                            <resetPwd />
                        </el-tab-pane>
                    </el-tabs>
                </el-card>
            </el-col>
            <el-col :span="12"
                    :xs="24">
                <el-card>
                    <div slot="header"
                         class="clearfix">
                        <span>布局设置</span>
                    </div>
                    <!-- 修改系统布局 -->
                    <el-col :span="14"
                            :xs="24">
                        <div class="setting-drawer-content">
                            <div class="setting-drawer-title">
                                <h3 class="drawer-title">系统布局配置</h3>
                            </div>
                            <div class="drawer-item"
                                 style="line-height: 2.2;">
                                <el-col :span="10"
                                        :xs="24">
                                    <span>开启 TopNav</span>
                                </el-col>
                                <el-col :span="10"
                                        :xs="24">
                                    <el-switch v-model="topNav"
                                               style="margin-left: 30px;" />
                                </el-col>
                            </div>
                            <div class="drawer-item"
                                 style="line-height: 2.2;">
                                <el-col :span="10"
                                        :xs="24">
                                    <span>开启 Tags-Views</span>
                                </el-col>
                                <el-col :span="10"
                                        :xs="24">
                                    <el-switch v-model="tagsView"
                                               style="margin-left: 30px;" />
                                </el-col>
                            </div>
                            <div class="drawer-item"
                                 style="line-height: 2.2;">
                                <el-col :span="10"
                                        :xs="24"><span>固定 Header</span></el-col>
                                <el-col :span="10"
                                        :xs="24">
                                    <el-switch v-model="fixedHeader"
                                               style="margin-left: 30px;" />
                                </el-col>
                            </div>
                            <div class="drawer-item"
                                 style="line-height: 2.2;">
                                <el-col :span="10"
                                        :xs="24"><span>显示 Logo</span></el-col>
                                <el-col :span="10"
                                        :xs="24">
                                    <el-switch v-model="sidebarLogo"
                                               style="margin-left: 30px;" />
                                </el-col>
                            </div>
                            <div class="drawer-item"
                                 style="line-height: 2.2;">
                                <el-col :span="10"
                                        :xs="24">
                                    <span>动态标题</span>
                                </el-col>
                                <el-col :span="10"
                                        :xs="24">
                                    <el-switch v-model="dynamicTitle"
                                               style="margin-left: 30px;" />
                                </el-col>
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="10"
                            :xs="24">
                        <div class="setting-drawer-content">
                            <div class="setting-drawer-title">
                                <h3 class="drawer-title">主题风格设置</h3>
                            </div>
                            <div class="setting-drawer-block-checbox">
                                <div class="setting-drawer-block-checbox-item"
                                     @click="handleTheme('theme-dark')">
                                    <img src="@/assets/images/dark.svg"
                                         alt="dark">
                                    <div v-if="sideTheme === 'theme-dark'"
                                         class="setting-drawer-block-checbox-selectIcon"
                                         style="display: block;">
                                        <i aria-label="图标: check"
                                           class="anticon anticon-check">
                                            <svg viewBox="64 64 896 896"
                                                 data-icon="check"
                                                 width="1em"
                                                 height="1em"
                                                 :fill="theme"
                                                 aria-hidden="true"
                                                 focusable="false"
                                                 class="">
                                                <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z" />
                                            </svg>
                                        </i>
                                    </div>
                                </div>
                                <div class="setting-drawer-block-checbox-item"
                                     @click="handleTheme('theme-light')">
                                    <img src="@/assets/images/light.svg"
                                         alt="light">
                                    <div v-if="sideTheme === 'theme-light'"
                                         class="setting-drawer-block-checbox-selectIcon"
                                         style="display: block;">
                                        <i aria-label="图标: check"
                                           class="anticon anticon-check">
                                            <svg viewBox="64 64 896 896"
                                                 data-icon="check"
                                                 width="1em"
                                                 height="1em"
                                                 :fill="theme"
                                                 aria-hidden="true"
                                                 focusable="false"
                                                 class="">
                                                <path d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z" />
                                            </svg>
                                        </i>
                                    </div>
                                </div>
                            </div>

                            <div class="drawer-item">
                                <span>主题颜色</span>
                                <theme-picker style="float: right;height: 26px;margin: -3px 8px 0 0;"
                                              @change="themeChange" />
                            </div>
                        </div>
                    </el-col>

                    <div>
                        <el-col :span="24"
                                class="mar-b"
                                :xs="24">
                            <el-col :offset="7"
                                    :span="6"
                                    :xs="24"
                                    class="buttom">
                                <el-button size="mini"
                                           type="primary"
                                           icon="el-icon-document-add"
                                           @click="saveSetting">保存配置</el-button>
                            </el-col>
                            <el-col :span="6"
                                    :xs="24"
                                    class="buttom">
                                <el-button size="mini"
                                           icon="el-icon-refresh"
                                           @click="resetSetting">重置配置</el-button>
                            </el-col>
                        </el-col>
                    </div>
                </el-card>
            </el-col>
        </el-row>
    </div>
</template>

<script>
import userAvatar from "@/views/system/user/profile/userAvatar";
import userInfo from "@/views/system/user/profile/userInfo";
import resetPwd from "@/views/system/user/profile/resetPwd";
import ThemePicker from '@/components/ThemePicker'
import { getUserProfile } from "@/api/system/user";

export default {
    name: "Profile",
    components: { userAvatar, userInfo, resetPwd, ThemePicker },
    data () {
        return {
            user: {},
            roleGroup: {},
            postGroup: {},
            activeTab: "userinfo",
            theme: this.$store.state.settings.theme,
            sideTheme: this.$store.state.settings.sideTheme
        };
    },
    created () {
        this.getUser();
        console.log("************" + process.env.VUE_APP_BASE_API)
    },
    computed: {
        visible: {
            get () {
                return this.$store.state.settings.showSettings
            }
        },
        fixedHeader: {
            get () {
                return this.$store.state.settings.fixedHeader
            },
            set (val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'fixedHeader',
                    value: val
                })
            }
        },
        topNav: {
            get () {
                return this.$store.state.settings.topNav
            },
            set (val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'topNav',
                    value: val
                })
                if (!val) {
                    this.$store.dispatch('app/toggleSideBarHide', false);
                    this.$store.commit("SET_SIDEBAR_ROUTERS", this.$store.state.permission.defaultRoutes);
                }
            }
        },
        tagsView: {
            get () {
                return this.$store.state.settings.tagsView
            },
            set (val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'tagsView',
                    value: val
                })
            }
        },
        sidebarLogo: {
            get () {
                return this.$store.state.settings.sidebarLogo
            },
            set (val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'sidebarLogo',
                    value: val
                })
            }
        },
        dynamicTitle: {
            get () {
                return this.$store.state.settings.dynamicTitle
            },
            set (val) {
                this.$store.dispatch('settings/changeSetting', {
                    key: 'dynamicTitle',
                    value: val
                })
            }
        },
    },
    methods: {
        getUser () {
            getUserProfile().then(response => {
                this.user = response.data;
                this.roleGroup = response.roleGroup;
                this.postGroup = response.postGroup;
            });
        },
        themeChange (val) {
            this.$store.dispatch('settings/changeSetting', {
                key: 'theme',
                value: val
            })
            this.theme = val;
        },
        handleTheme (val) {
            this.$store.dispatch('settings/changeSetting', {
                key: 'sideTheme',
                value: val
            })
            this.sideTheme = val;
        },
        saveSetting () {
            this.$modal.loading("正在保存到本地，请稍候...");
            this.$cache.local.set(
                "layout-setting",
                `{
            "topNav":${this.topNav},
            "tagsView":${this.tagsView},
            "fixedHeader":${this.fixedHeader},
            "sidebarLogo":${this.sidebarLogo},
            "dynamicTitle":${this.dynamicTitle},
            "sideTheme":"${this.sideTheme}",
            "theme":"${this.theme}"
          }`
            );
            setTimeout(this.$modal.closeLoading(), 1000)
        },
        resetSetting () {
            this.$modal.loading("正在清除设置缓存并刷新，请稍候...");
            this.$cache.local.remove("layout-setting")
            setTimeout("window.location.reload()", 1000)
        }
    }
};
</script>
<style lang="scss" scoped>
.buttom {
    padding: 76px 0 40px 0;
}
.mar-b {
    margin-bottom: 13px;
}
.setting-drawer-content {
    color: rgba(0, 0, 0, 0.65);
    padding: 0 20px 80px 0;
    font-size: 13px;
    .setting-drawer-title {
        margin-bottom: 12px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        line-height: 22px;
        font-weight: bold;
    }

    .setting-drawer-block-checbox {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: 10px;
        margin-bottom: 20px;

        .setting-drawer-block-checbox-item {
            position: relative;
            margin-right: 16px;
            border-radius: 2px;
            cursor: pointer;

            img {
                width: 48px;
                height: 48px;
            }

            .setting-drawer-block-checbox-selectIcon {
                position: absolute;
                top: 0;
                right: 0;
                width: 100%;
                height: 100%;
                padding-top: 15px;
                padding-left: 24px;
                color: #1890ff;
                font-weight: 700;
                font-size: 14px;
            }
        }
    }
}

.drawer-container {
    padding: 20px;
    font-size: 14px;
    line-height: 3;
    word-wrap: break-word;

    .drawer-title {
        margin-bottom: 12px;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        line-height: 22px;
    }

    .drawer-item {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        padding: 18px 0;
    }

    .drawer-switch {
        padding-left: 30px;
    }
}
</style>