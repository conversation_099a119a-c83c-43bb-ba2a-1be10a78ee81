import request from '@/utils/request'

// 查询物料分类物料映射列表
export function listMapping(query,selectVO) {
  return request({
    url: '/api/md/materialcategorymapping/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询物料分类物料映射详细
export function getMapping(mappingId) {
  return request({
    url: '/api/md/materialcategorymapping/' + mappingId,
    method: 'get'
  })
}

export function getSaveEntity(mappingId) {
  return request({
    url: '/api/md/materialcategorymapping/getSaveEntity/' + mappingId,
    method: 'get'
  })
}

// 新增物料分类物料映射
export function addMapping(data) {
  return request({
    url: '/api/md/materialcategorymapping',
    method: 'post',
    data: data
  })
}
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/materialcategorymapping/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 修改物料分类物料映射
export function updateMapping(data) {
  return request({
    url: '/api/md/materialcategorymapping',
    method: 'put',
    data: data
  })
}

// 删除物料分类物料映射
export function delMapping(mappingId) {
  return request({
    url: '/api/md/materialcategorymapping/' + mappingId,
    method: 'delete'
  })
}
