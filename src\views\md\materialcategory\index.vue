<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">

      <el-form-item label="分组">
        <el-select v-model="queryParams.categoryGroup" style="width: 100%;">
          <el-option v-for="dict in dict.type.material_category_group" :key="dict.value" :label="dict.label"
            :value="dict.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="编码" prop="categoryCode">
        <el-input v-model="queryParams.categoryCode" placeholder="请输入编码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="名称" prop="categoryName">
        <el-input v-model="queryParams.categoryName" placeholder="请输入名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" row-key="categoryId" :data="materialCategoryList" border
      @selection-change="handleSelectionChange" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <af-table-column type="selection" width="55" align="left" header-align="center" />
      <af-table-column label="编码" align="left" header-align="center" prop="categoryCode" />
      <af-table-column label="名称" align="left" header-align="center" prop="categoryName" />
      <af-table-column label="简称" align="left" header-align="center" prop="shortName" />
      <af-table-column label="等级" align="left" header-align="center" prop="categoryLevel" />
      <af-table-column label="状态" align="left" header-align="center" prop="categoryStatus" />

      <af-table-column label="上级" align="left" header-align="center" prop="parent.categoryName" />
      <af-table-column label="分组" align="left" header-align="center" prop="categoryGroup" />
      <af-table-column label="备注" align="left" header-align="center" prop="remark" />
      <el-table-column label="更新者" align="left" header-align="center" prop="updateBy" />
      <el-table-column label="更新时间" align="left" header-align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{
      parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}")
    }}</span>
        </template>
      </el-table-column>

      <af-table-column label="操作" align="left" header-align="center" width="250px"
        class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-top" @click="handleUp(scope.row)">上移</el-button>
          <el-button size="mini" type="text" icon="el-icon-bottom" @click="handleDown(scope.row)">下移</el-button>
        </template>
      </af-table-column>
    </el-table>

    <!-- 添加或修改物料分类对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="分组" prop="categoryGroup">
              <el-select v-model="form.categoryGroup" style="width: 100%;" @change="change">
                <el-option v-for="dict in dict.type.material_category_group" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>

          </el-col>
          <el-col :span="12">
            <el-form-item label="上级" prop="parentId">
              <treeselect v-model="form.parentId" :options="editmaterialCategoryList" :show-count="true"
                placeholder="请选择归属部门" />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="编码" prop="categoryCode">
              <el-input v-model="form.categoryCode" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="简称" prop="shortName">
              <el-input v-model="form.shortName" placeholder="请输入简称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="categoryName">
              <el-input v-model="form.categoryName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.categoryStatus">
                <el-radio v-for="dict in dict.type.record_status" :key="dict.value" :label="dict.value">{{ dict.label
                  }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMaterialCategory,
  getMaterialCategory,
  delMaterialCategory,
  addMaterialCategory,
  updateMaterialCategory,
  treeselectByGroup,
  moveUp,
  moveDown,
} from "@/api/md/materialCategory";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "MaterialCategory",
  dicts: ["record_status", 'material_category_group'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 物料分类表格数据
      materialCategoryList: [],
      editmaterialCategoryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        categoryCode: null,
        categoryName: null,
        updateTime: null,
        remark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        categoryName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        categoryCode: [
          { required: true, message: "编码不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {

    this.getList();
  },
  methods: {
    /** 查询物料分类列表 */
    getList() {
      if (this.queryParams.categoryGroup == "" || this.queryParams.categoryGroup == null) {
        this.$message({
          message: '清选择分组',
          type: 'warning'
        });
        this.loading = false;
        return;
      }
      this.loading = true;
      listMaterialCategory(this.queryParams).then((response) => {
        this.materialCategoryList = this.handleTree(
          response.data,
          "categoryId"
        );
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        categoryId: null,
        categoryCode: null,
        categoryName: null,
        shortName: null,
        categoryStatus: "生效",
        categoryGroup: null,
        parentId: null,
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.categoryId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.categoryGroup = this.queryParams.categoryGroup;
      this.change(this.form.categoryGroup);
      this.open = true;
      this.title = "添加物料分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const categoryId = row.categoryId || this.ids;


      getMaterialCategory(categoryId).then((response) => {
        treeselectByGroup(response.data.categoryGroup).then((response2) => {
          this.editmaterialCategoryList = response2.data;

          this.form = response.data;
          if (this.form.parentId == 0) {
            this.form.parentId = null;
          }
          this.open = true;
          this.title = "修改物料分类";
        });

      });

    },
    /** 上移 */
    handleUp(row) {
      const categoryId = row.categoryId || this.ids;
      this.$modal
        .confirm("确定上移?")
        .then(function () {
          return moveUp(categoryId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("上移成功");
        })
        .catch(() => { });
    },
    /** 下移 */
    handleDown(row) {
      const categoryId = row.categoryId || this.ids;
      this.$modal
        .confirm("确定下移?")
        .then(function () {
          return moveDown(categoryId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("下移成功");
        })
        .catch(() => { });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.categoryId != null) {
            updateMaterialCategory(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addMaterialCategory(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    change(val) {
      treeselectByGroup(val).then((response) => {
        this.form.parentId = null;
        this.editmaterialCategoryList = response.data;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const categoryIds = row.categoryId || this.ids;
      this.$modal
        .confirm('是否确认删除物料分类编？')
        .then(function () {
          return delMaterialCategory(categoryIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/api/md/materialcategory/export",
        {
          ...this.queryParams,
        },
        `物料分类${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
