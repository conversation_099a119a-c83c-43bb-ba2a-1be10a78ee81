<template>
    <div>
        <div style="margin: 10px;">
            <vxe-grid ref="grid1Ref" v-bind="grid1Options" @cell-click="cellClickEvent">
                <!-- <template #action="{ row }"> 
                    <vxe-button mode="text" status="primary" icon="vxe-icon-edit">操作</vxe-button>
                </template> -->
            </vxe-grid>
        </div>
        <div style="margin: 10px;">
            <vxe-grid ref="grid2Ref" v-bind="grid2Options">
            </vxe-grid>
        </div>
    </div>
</template>

<script>
import {
    selectStockInData,
    selectStockInDetailData,
} from "@/api/wms/stockin"


export default {
    name: 'StockIn',
    data() {
        // grid1数据
        const grid1Options = {
            columns: [
                { type: 'seq', width: 50 },
                // { field: 'inTime', title: '入厂时间', },
                { field: 'weightTypeName', title: '检斤类型', },
                { field: 'materialNumber', title: '物料编码', },
                { field: 'materialName', title: '物料名称', },
                { field: 'productTypeName', title: '物料类型', },
                { field: 'supplierName', title: '供货商', },
                { field: 'transportType', title: '运输方式', },
                // { field: 'stock', title: '料场', },
                // { field: 'across', title: '料条', },
                // { field: 'stack', title: '垛位', },
                // { field: 'stoker', title: '堆料机', },
                // { title: '操作', width: 100, fixed: 'right', slots: { default: 'action' } }
            ],
            data: [],

            border: true,
            stripe: true,
            align: 'center',
            height: 400,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            // keyboardConfig: {
            //     isArrow: true
            // },
            // mouseConfig: {
            //     selected: true
            // },
        }

        // grid2数据
        const grid2Options = {
            columns: [
                { field: 'seq', type: 'seq', width: 50 },
                { field: 'orderNumber', title: '订单', },
                { field: 'weightNumber', title: '检斤单', },
                { field: 'truckNumber', title: '车牌号', }, //
                { field: 'meterState', title: '计量状态', },
                { field: 'productTypeName', title: '产品类别', },
                { field: 'tareTime', title: '一检时间', width: 100 },
                { field: 'tareWeight', title: '皮重', },
                { field: 'grossTime', title: '二检时间', width: 100},
                { field: 'grossWeight', title: '毛重', },
                { field: 'netWeight', title: '净重', },
                { field: 'oppositeTheoreticalWeight', title: '对方重', },
            ],
            data: [],

            // showFooter: true,
            // footerData: [],

            border: true,
            stripe: true,
            align: 'center',
            height: 400,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            // mouseConfig: {
            //     selected: true
            // },
        }

        const sumObj = {
            seq: '合计',
            TRUCK_NUMBER: 6,
            NET_WEIGHT: 88.24
        }

        return {
            grid1Options,
            grid2Options,
            sumObj
        }
    },

    methods: {
        initGridData() {
            this.initGrid1Data()
            // this.initGrid2Data()
        },

        // 初始化grid1数据
        initGrid1Data() {
            this.grid1Options.loading = true
            selectStockInData(null).then(response => {
                let result = response.data
                this.grid1Options.data = result
                this.grid1Options.loading = false

                const $grid1 = this.$refs.grid1Ref
                $grid1.setCurrentRow(this.grid1Options.data[0])

                selectStockInDetailData(this.grid1Options.data[0].materialNumber).then(response => {
                    let result = response.data
                    this.grid2Options.data = result
                    this.grid2Options.loading = false
                })
            })

            // this.grid1Options.data = [
            //     {
            //         id: 10001, inTime: '2025-04-22', mateCode: '1010100033', mateName: '澳大利亚FMG超特粗粉', specCode: '规格型号', supplierName: '天津市辰瑞国际贸易有限公司',
            //         transportType: '汽运', stock: '原料库2#料场', across: '料条', stack: '垛位', stoker: '堆料机'
            //     },
            //     {
            //         id: 10002, inTime: '2025-04-22', mateCode: '1010100033', mateName: '澳大利亚FMG超特粗粉', specCode: '规格型号', supplierName: '天津市辰瑞国际贸易有限公司',
            //         transportType: '汽运', stock: '原料库2#料场', across: '料条', stack: '垛位', stoker: '堆料机'
            //     },
            // ]

        },
        // 初始化grid2数据
        initGrid2Data() {

            // this.grid2Options.data = [
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            //     {
            //         id: 10001,
            //         WEIGHT_NUMBER: 'QCHBGB19220250422001531',
            //         ORDER_NUMBER: 'CGDD-2503030004',
            //         TRUCK_NUMBER: '津A28277D',
            //         METER_STATE: '一检完成',
            //         PRODUCT_TYPE_NAME: '外矿',
            //         tare_time: '2025-04-22 00:35:45',
            //         TARE_WEIGHT: 44.1,
            //         GROSS_TIME: '2025-04-22 01:00:15',
            //         GROSS_WEIGHT: 27.5,
            //         NET_WEIGHT: 16.56,
            //         OPPOSITE_THEORETICAL_WEIGHT: 0
            //     },
            // ]

            // this.grid2Options.footerData = [
            //     this.sumObj
            // ]
        },

        cellClickEvent(row) {
            // console.log(row.row.materialNumber);
            this.grid2Options.loading = true
            selectStockInDetailData(row.row.materialNumber).then(response => {
                let result = response.data
                this.grid2Options.data = result
                this.grid2Options.loading = false
            })
        },
    },

    mounted() {
        this.initGridData();
    },

}
</script>