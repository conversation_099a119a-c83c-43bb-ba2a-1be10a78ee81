<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="编码" prop="prodCenterCode">
        <el-input v-model="queryParams.prodCenterCode" placeholder="请输入编码" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="prodCenterName">
        <el-input v-model="queryParams.prodCenterName" placeholder="请输入名称" clearable
                  @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
                   @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                   @click="handleDelete"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport">导出</el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" row-key="prodCenterId" :data="productcenterList" :border="true"
              @selection-change="handleSelectionChange"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="编码" align="center" prop="prodCenterCode"/>
      <el-table-column label="名称" align="center" prop="prodCenterName"/>
      <el-table-column label="等级" align="center" prop="prodCenterLevel"/>

      <el-table-column label="工序" align="center" prop="sysDictData.dictLabel"/>
      <el-table-column label="上级" align="center" prop="parent.prodCenterName"/>
      <el-table-column label="存储后缀" align="center" prop="storeSuffix"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status == '生效'" type="success">{{
              scope.row.status
            }}
          </el-tag>
          <el-tag v-if="scope.row.status == '失效'" type="danger">{{
              scope.row.status
            }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="更新者" align="center" prop="updateBy"/>
      <el-table-column label="更新时间" align="center" prop="updateTime">
        <template slot-scope="scope">
          <span>{{
              parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}')
            }}</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="250px"
                       class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAddRow(scope.row)">新增</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-top" @click="handleUp(scope.row)">上移</el-button>
          <el-button size="mini" type="text" icon="el-icon-bottom" @click="handleDown(scope.row)">下移</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改加工中心对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="编码" prop="prodCenterCode">
              <el-input v-model="form.prodCenterCode" placeholder="请输入编码"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="名称" prop="prodCenterName">
              <el-input v-model="form.prodCenterName" placeholder="请输入名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级" prop="parentId">
              <treeselect v-model="form.parentId" :options="editProductCenterList" :show-count="true"
                          placeholder="请选择归属部门"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="存储后缀" prop="storeSuffix">
              <el-select v-model="form.storeSuffix" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in suffixList"
                  :key="item.suffix"
                  :label="item.TABLE_COMMENT"
                  :value="item.suffix">
                  <span style="float: left">{{ item.TABLE_COMMENT }}</span>
                  <span style="float: right; color: #909399;">{{ item.suffix }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="工序" prop="processCode">
              <el-select v-model="form.processCode" placeholder="请选择工序">
                <el-option v-for="dict in dict.type.production_process" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="form.status" placeholder="请选择工序">
                <el-option v-for="dict in dict.type.effective_or_not" :key="dict.value" :label="dict.label"
                           :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listProductcenter,
  getProductcenter,
  delProductcenter,
  saveOrUpdate,
  treeselect,
  moveUp,
  moveDown,
  querysuffix
} from '@/api/md/productcenter'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Productcenter',
  dicts: ['production_process', 'effective_or_not'],
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 加工中心表格数据
      productcenterList: [],
      editProductCenterList: [],
      suffixList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        prodCenterCode: null,
        prodCenterName: null,
        remark: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        prodCenterCode: [
          { required: true, message: '编码不能为空', trigger: 'blur' }
        ],
        prodCenterName: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    querysuffix().then((response) => {
      this.suffixList = response.data
    })
    this.getList()
  },
  methods: {
    /** 查询加工中心列表 */
    getList() {
      this.loading = true
      listProductcenter(this.queryParams).then((response) => {
        this.productcenterList = this.handleTree(response.data, 'prodCenterId')
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        prodCenterId: null,
        prodCenterCode: null,
        prodCenterName: null,
        prodCenterLevel: null,
        status: '生效',
        processCode: null,
        parentId: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.prodCenterId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      treeselect().then((response) => {
        this.editProductCenterList = response.data
      })
      this.open = true
      this.title = '添加加工中心'
    },
    handleAddRow(row) {
      this.reset()
      const prodCenterId = row.prodCenterId || this.ids
      treeselect().then((response) => {
        this.editProductCenterList = response.data

        getProductcenter(prodCenterId).then((response) => {
          this.form.parentId = response.data.prodCenterId
          if (this.form.parentId == 0) {
            this.form.parentId = null
          }
          this.open = true
          this.title = '添加加工中心'
        })
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const prodCenterId = row.prodCenterId || this.ids
      treeselect().then((response) => {
        this.editProductCenterList = response.data

        getProductcenter(prodCenterId).then((response) => {
          this.form = response.data
          if (this.form.parentId == 0) {
            this.form.parentId = null
          }
          this.open = true
          this.title = '修改加工中心'
        })
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          saveOrUpdate(this.form).then((response) => {
            this.$modal.msgSuccess('保存成功')
            this.open = false
            this.getList()
          })
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const prodCenterIds = row.prodCenterId || this.ids
      this.$modal
        .confirm('确定是否删除加工中心')
        .then(function() {
          return delProductcenter(prodCenterIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {
        })
    },
    /** 上移 */
    handleUp(row) {
      const prodCenterIds = row.prodCenterId
      this.$modal
        .confirm('确定上移?')
        .then(function() {
          return moveUp(prodCenterIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('上移成功')
        })
        .catch(() => {
        })
    },
    /** 下移 */
    handleDown(row) {
      const prodCenterIds = row.prodCenterId
      this.$modal
        .confirm('确定下移?')
        .then(function() {
          return moveDown(prodCenterIds)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('下移成功')
        })
        .catch(() => {
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        '/api/md/productcenter/export',
        {
          ...this.queryParams
        },
        `加工中心_${new Date().getTime()}.xlsx`
      )
    },
  }
}
</script>
