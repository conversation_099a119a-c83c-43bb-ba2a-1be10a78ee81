<template>
  <!-- 出/入库  -->
  <el-dialog :title="title" width="500px" append-to-body :visible.sync="dialogVisible" :show-close="false" v-dialogDrag :close-on-click-modal="false"
             :modal="false">
    <el-form ref="form" :model="form" label-width="120px">
      <el-form-item :label="title+ '物料'" prop="mateName">
        <el-input v-model="mateName" type="text" placeholder="请输入内容" />
      </el-form-item>
      <el-form-item :label="title+ '重量'" prop="weight">
        <el-input-number v-model="form.weight" :precision="4" :step="1" :min="0" style="width: 100%;"></el-input-number>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: 'StockOut',
  props: {
    open: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    mateName: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      // 提交表单
      form: {
        storehouseCode: '',
        mateName: '',
        crossRegion: '',
        stackingPosition: '',
        weight: 0,
      },
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.open;
      },
      set(val) {
        this.$emit('update', val);
      }
    }
  },
  methods: {
    cancel() {
      this.reset()
      this.$emit('on-cancel');
    },
    reset() {
      this.form = {
        storehouseCode: '',
        mateName: '',
        crossRegion: '',
        stackingPosition: '',
        weight: 0,
      }
    },
    submitForm() {
      this.$emit('on-confirm');
      this.reset()
    },

  },
  created() {
  },
  mounted() {
  },
}
</script>