<template>
    <div style="margin: 10px;">
        <vxe-grid ref="gridRef" v-bind="grid1Options" @page-change="pageChangeEvent">
            <template #expand_content="{ row }">
                <div class="expand-wrapper">
                    <vxe-grid v-bind="grid2Options" :data="row.childList"></vxe-grid>
                </div>
            </template>

            <template #form>
                <vxe-form ref="searchFormRef" v-bind="searchFormOptions">
                    <template #action>
                        <el-button type="primary" icon="el-icon-search" size="mini" @click="searchEvent">搜索</el-button>
                        <el-button icon="el-icon-refresh" size="mini" @click="resetEvent">重置</el-button>
                    </template>
                </vxe-form>
            </template>

            <template #toolbarButtons>
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="addEvent">新增</el-button>
            </template>

            <template #active="{ row }">
                <el-button type="success" plain size="mini" @click="saveRow(row)">保存</el-button>
                <el-button type="danger" plain size="mini" @click="removeRow(row)">删除</el-button>
            </template>
        </vxe-grid>

        <vxe-modal resize destroy-on-close show-footer show-confirm-button show-cancel-button v-model="showEditPopup"
            :title="formTitle" :confirm-closable="false" :esc-closable="true" @confirm="confirmEvent"
            @cancel="cancelEvent" @close="closeEvent">
            <vxe-form ref="editFormRef" v-bind="editFormOptions"></vxe-form>
        </vxe-modal>
    </div>
</template>


<script>
import { initMaterialList } from "@/api/md/material";
import { stackPlanList, saveOrUpdate, deletePlan } from "@/api/wms/stackplan";
import { VxeUI } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'


const defaultData = {
    stackPlanId: null,
    storehouseCode: '',
    mateCodes: [],
    crossRegion: '',
    stackingPosition: '',
    startPosition: '',
    endPosition: '',
}
export default {
    name: 'StackPlan1',
    data() {
        const materialRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
            },
            options: []
        }
        const materialsRender = {
            name: 'VxeSelect',
            props: {
                filterable: true,
                clearable: true,
                multiple: true,
            },
            options: []
        }
        const stripRender = {
            name: 'VxeSelect',
            props: {
                clearable: true,
            },
            options: [
                { value: '1', label: '1' },
                { value: '2', label: '2' },
                { value: '3', label: '3' },
                { value: '4', label: '4' },
                { value: '5', label: '5' },
                { value: '6', label: '6' },
            ]
        }

        const grid1Options = {
            columns: [
                { field: 'seq', type: 'seq', width: 50 },
                { type: 'expand', width: 60, slots: { content: 'expand_content' } },
                { field: 'stackPlanId', visible: false },
                { field: 'storehouseCode', title: '原料场编码', visible: false },
                { field: 'storehouseName', title: '原料场名称', },
                { field: 'mateCodes', title: '物料名称', editRender: materialsRender },
                { field: 'crossRegion', title: '料条', },
                { field: 'stackingPosition', title: '垛位', },
                { field: 'stackName', title: '垛位名称', editRender: { name: 'VxeInput' } },
                { field: 'startPosition', title: '开始位置', editRender: { name: 'VxeInput', props: { type: 'integer' } } },
                { field: 'endPosition', title: '结束位置', editRender: { name: 'VxeInput', props: { type: 'integer' } } },
                { field: 'remark', title: '备注', editRender: { name: 'VxeInput' } },
                { field: 'active', title: '操作', fixed: 'right', slots: { default: 'active' } }
                // { field: 'createBy', title: '创建者', },
                // { field: 'createTime', title: '创建时间', },
                // { field: 'updateBy', title: '更新者', },
                // { field: 'updateTime', title: '更新时间', },
            ],
            data: [],

            border: true,
            strip: true,
            align: 'center',
            showOverflow: true,
            keepSource: true,
            loading: false,

            columnConfig: {
                resizable: true
            },
            rowConfig: {
                isHover: true,
                isCurrent: true,
            },
            pagerConfig: {
                total: 0,
                currentPage: 1,
                pageSize: 10
            },
            toolbarConfig: {
                // custom: true,
                zoom: true,
                slots: {
                    buttons: 'toolbarButtons'
                }
            },
            editConfig: {
                mode: 'row',
                trigger: 'dblclick',
                showStatus: true,
            },
            editRules: {
                mateCodes: [
                    { required: true }
                ],
                startPosition: [
                    { required: true }
                ],
                endPosition: [
                    { required: true }
                ],
                // stackName: [
                //     { required: true }
                // ],
            },
        }

        const grid2Options = {
            columns: [
                { field: 'stackPlanId', visible: false },
                { field: 'mateCode', title: '物料编码', },
                { field: 'mateName', title: '物料名称', },
            ],
            border: true,
            strip: true,
            align: 'center',
            loading: false,
        }

        const searchFormOptions = {
            data: {
                crossRegion: '',
                mateCode: '',
            },
            items: [
                { field: 'crossRegion', title: '料条', itemRender: stripRender },
                { field: 'mateCode', title: '物料', itemRender: materialRender },
                { slots: { default: 'action' } }
            ]
        }

        const editFormOptions = {
            data: {
                stackPlanId: null,
                storehouseCode: '',
                mateCodes: [],
                crossRegion: '',
                stackName: '',
                startPosition: '',
                endPosition: '',
            },
            items: [
                { field: 'mateCodes', title: '物料', span: 24, itemRender: materialsRender },
                { field: 'crossRegion', title: '料条', span: 24, itemRender: stripRender },
                { field: 'stackName', title: '垛位名称', span: 24, itemRender: { name: 'VxeInput' } },
                { field: 'startPosition', title: '开始位置(米)', span: 24, itemRender: { name: 'VxeNumberInput' } },
                { field: 'endPosition', title: '结束位置(米)', span: 24, itemRender: { name: 'VxeNumberInput' } },
                { field: 'remark', title: '备注', span: 24, itemRender: { name: 'VxeInput' } },
            ],
            rules: {
                mateCodes: [
                    { required: true, message: '必须填写' }
                ],
                crossRegion: [
                    { required: true, message: '必须填写' }
                ],
                startPosition: [
                    { required: true, message: '必须填写' }
                ],
                endPosition: [
                    { required: true, message: '必须填写' }
                ],
                // stackName: [
                //     { required: true, message: '必须填写' }
                // ],
            },
            titleWidth: 90,
            titleAlign: 'center'
        }

        return {
            grid1Options,
            grid2Options,

            searchFormOptions,
            editFormOptions,

            materialRender,
            materialsRender,
            stripRender,

            formTitle: '',
            showEditPopup: false,
        }
    },
    methods: {
        searchList() {
            this.grid1Options.pagerConfig.currentPage = 1
            this.handlePageData()
        },
        handlePageData() {
            this.grid1Options.loading = true
            let query = this.searchFormOptions.data
            query.storehouseCode = this.getStoreHouseCode()
            stackPlanList(query).then(response => {
                let data = response.data
                const { pageSize, currentPage } = this.grid1Options.pagerConfig
                this.grid1Options.pagerConfig.total = data.length
                this.grid1Options.data = data.slice((currentPage - 1) * pageSize, currentPage * pageSize)
                this.grid1Options.loading = false
            })
        },
        pageChangeEvent({ pageSize, currentPage }) {
            this.grid1Options.pagerConfig.currentPage = currentPage
            this.grid1Options.pagerConfig.pageSize = pageSize
            this.handlePageData()
        },

        searchEvent() {
            this.searchList()
        },
        resetEvent() {
            const $form = this.$refs.searchFormRef
            if ($form) {
                $form.reset()
                this.searchList()
            }
        },

        addEvent() {
            this.showEditPopup = true
            // const $grid = this.$refs.gridRef
            // if ($grid) {
            //     const records = $grid.getCheckboxRecords()
            //     if (records.length > 1) {
            //         VxeUI.modal.alert({
            //             content: '请选择一条记录...',
            //             escClosable: true
            //         })
            //         return
            //     }
            //     if (records.length === 1) {
            //         this.editFormOptions.data = Object.assign(XEUtils.clone(defaultData, true), records[0])
            //     }
            // }
        },

        async saveRow() {
            const $grid = this.$refs.gridRef
            if ($grid) {
                const errMap = await $grid.validate(true)
                if (errMap) {
                    VxeUI.modal.message({ status: 'error', content: '校验不通过！' })
                    return
                }

                const { updateRecords } = $grid.getRecordset()
                if (updateRecords.length === 1) {
                    saveOrUpdate(updateRecords[0]).then(response => {
                        this.searchList()
                        VxeUI.modal.message({
                            content: '修改成功',
                            status: 'success'
                        })
                    })
                }
            }
        },

        removeRow(row) {
            VxeUI.modal.confirm({
                content: '是否确定删除',
            }).then(type => {
                if (type === 'confirm') {
                    const $grid = this.$refs.gridRef
                    if ($grid) {
                        if (row.stackPlanId === null) {
                            $grid.remove(row)
                        } else {
                            deletePlan(row).then(response => {
                                this.searchList()
                                VxeUI.modal.message({
                                    content: '删除成功',
                                    status: 'success'
                                })
                            })
                        }
                    }
                }
            })
        },

        async confirmEvent() {
            const $form = this.$refs.editFormRef
            if ($form) {
                const errMap = await $form.validate()
                if (errMap) {
                    return
                }

                this.editFormOptions.data.storehouseCode = this.getStoreHouseCode()
                saveOrUpdate(this.editFormOptions.data).then(response => {
                    this.closeEvent()
                    this.searchList()
                    VxeUI.modal.message({
                        content: '新增成功',
                        status: 'success'
                    })
                })

            }
        },
        cancelEvent() {
            this.closeEvent()
        },
        closeEvent() {
            const $form = this.$refs.editFormRef
            if ($form) {
                $form.reset()
                this.showEditPopup = false
            }
        },

        initData() {
            initMaterialList().then(response => {
                let data = response.data
                let list = []
                for (let i = 0; i < data.length; i++) {
                    list.push({
                        value: `${data[i].materialNumber}`,
                        label: `${data[i].shortName ? data[i].shortName : data[i].materialName}` + `(${data[i].materialNumber})`
                    })
                }
                this.materialRender.options = list
                this.materialsRender.options = list
            })
        },

        getProdCenterCode() {
            return this.$route.query.prodCenterCode
        },

        getStoreHouseCode() {
            return this.$route.query.storehouseCode
        },
    },
    created() {
        this.initData()
    },
    mounted() {
        this.searchList()
    },
    beforeDestroy() { },
}
</script>


<style scoped>
.wrap {
    display: flex;
    justify-content: stretch;
}

.wrapGrid1 {
    width: 80%;
}

.wrapGrid2 {
    width: 20%;
}
</style>