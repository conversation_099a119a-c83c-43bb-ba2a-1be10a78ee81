import request from '@/utils/request'


//查询 库存
export function selectStockRealData(query) {
    return request({
        url: '/api/wms/stockreal/list',
        method: 'get',
        params: query,
    })
}

export function selectStockRealDataNew(query) {
    return request({
        url: '/api/wms/stockreal/listNew',
        method: 'get',
        params: query,
    })
}

export function selectStockRealDataByCode(query) {
    return request({
        url: '/api/wms/stockreal/listByCode',
        method: 'get',
        params: query,
    })
}

export function selectStockByCrossRegionAndStackingPosition(query) {
    return request({
        url: '/api/wms/stockreal/stockByCrossRegionAndStackingPosition',
        method: 'get',
        params: query,
    })
}


//新增 修改
export function saveOrUpdate(data) {
    return request({
        url: '/api/wms/stockreal/saveOrUpdate',
        method: 'post',
        data: data,
    })
}

export function updateStockWeightAndRemark(data) {
    return request({
        url: '/api/wms/stockreal/updateTWmsStockRealStockWeightAndRemark',
        method: 'post',
        data: data,
    })
}


export function stackChange(data) {
    return request({
        url: '/api/wms/stockreal/stackChange',
        method: 'post',
        data: data,
    })
}


export function checkStockWeight(data) {
    return request({
        url: '/api/wms/stockreal/checkStockWeight',
        method: 'post',
        data: data,
    })
}


export function deleteStockReal(data) {
    return request({
        url: '/api/wms/stockreal/del',
        method: 'delete',
        data: data,
    })
}


//查询 库存
export function selectStockReal(query) {
    return request({
        url: '/api/wms/stockreal/coalList',
        method: 'get',
        params: query,
    })
}