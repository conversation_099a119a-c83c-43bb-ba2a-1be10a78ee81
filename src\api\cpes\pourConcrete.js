import request from '@/utils/request'

// 查询烧结打灰记录列表
export function listConcrete(query) {
  return request({
    url: '/api/cpes/pourConcrete/list',
    method: 'get',
    params: query
  })
}

// 查询烧结打灰记录详细
export function getConcrete(pourConcreteId) {
  return request({
    url: '/api/cpes/pourConcrete/' + pourConcreteId,
    method: 'get'
  })
}

// 新增烧结打灰记录
export function addConcrete(data) {
  return request({
    url: '/api/cpes/pourConcrete',
    method: 'post',
    data: data
  })
}

// 修改烧结打灰记录
export function updateConcrete(data) {
  return request({
    url: '/api/cpes/pourConcrete',
    method: 'put',
    data: data
  })
}

// 删除烧结打灰记录
export function delConcrete(pourConcreteId) {
  return request({
    url: '/api/cpes/pourConcrete/' + pourConcreteId,
    method: 'delete'
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/cpes/pourConcrete/saveOrUpdate',
    method: 'post',
    data: data
  })
}

export function getFactoryName(prodCenterCode) {
  return request({
    url: '/api/cpes/pourConcrete/getFactoryName/' + prodCenterCode,
    method: 'get'
  })
}

export function getStorageNumber(prodCenterCode) {
  return request({
    url: '/api/cpes/pourConcrete/getStorageNumber/' + prodCenterCode,
    method: 'get'
  })
}

export function getOperType() {
  return request({
    url: '/api/cpes/pourConcrete/getOperType',
    method: 'get'
  })
}

