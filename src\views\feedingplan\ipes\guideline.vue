<template>
  <div class="guideline-container">
    <vxe-grid
      v-bind="gridOptions"
      class="guideline-table"
      @page-change="handlePageChange"
      ref="tableRef"
    >
      <!-- 搜索表单 -->
      <template #form>
        <vxe-form ref="searchFormRef" v-bind="formOptions">
          <template #action>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSearch">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="handleReset">重置</el-button>
          </template>
        </vxe-form>
      </template>
    </vxe-grid>
    
    <!-- 高炉选择器 -->
    <div class="furnace-selector">
      <el-select v-model="selectedFurnace" placeholder="请选择高炉" @change="updateAllCharts">
        <el-option
          v-for="furnace in furnaceList"
          :key="furnace"
          :label="furnace"
          :value="furnace"
        />
      </el-select>
    </div>
    
    <!-- 折线图容器 -->
    <div class="charts-wrapper">
      <div v-for="(chart, idx) in chartGroupData" :key="idx" class="chart-container">
        <div :ref="'chartRef' + idx" class="chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
// 替换数据源接口
import { getGuidelineData } from "@/api/feedingplan/guideline";
import * as echarts from 'echarts';

export default {
  name: 'GuidelineManagement',
  data() {
    return {
      // 表格配置
      gridOptions: {
        columns: [],
        data: [],
        border: true,
        stripe: true,
        align: 'center',
        height: '50%',
        loading: false,
        columnConfig: { resizable: true },
        rowConfig: { isHover: true, isCurrent: true },
        mouseConfig: { selected: true },
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50]
        },
        toolbarConfig: {
          custom: true,
          zoom: true,
       //   slots: { buttons: 'toolbar' }
        }
      },

      // 搜索表单配置 - 适配新数据字段
      formOptions: {
        data: {
          prodCenterCode: '', 
          workDate: new Date().toISOString().split('T')[0],
          name: ''
        },
        items: [
          {
            field: 'prodCenterCode',
            title: '加工中心',
            itemRender: {
              name: 'VxeSelect',
              options: [
                { label: '1#高炉', value: 'IPES01' },
                { label: '2#高炉', value: 'IPES02' }
              ],
              props: {
                placeholder: '请选择加工中心'
              }
            }
          },
          {
            field: 'workDate',
            title: '工作日期',
            itemRender: { name: 'VxeDatePicker', props: { type: 'date' } }
          },
          {
            field: 'name',
            title: '名称',
            itemRender: { name: 'VxeInput' }
          },
          { slots: { default: 'action' } }
        ]
      },
      
      // 高炉选择相关
      furnaceList: [],
      selectedFurnace: '',
      
      // 图表相关
      chartGroupData: [], // [{ title, dates, series }]
      chartInstances: [],
      
      // 原始数据缓存
      originalData: []
    };
  },

  mounted() {
    this.initGridColumns();
    this.loadTableData();
  },

  beforeDestroy() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
  },

  methods: {
    // 获取空表单对象
    getEmptyForm() {
      return {
        name: '',
        value: null,
        prodCenterName: '',
        prodCenterCode: '',
        workDate: this.getTodayDate(),
        passRate: null,
        workClsass: '',
      };
    },

    initGridColumns() {
      this.gridOptions.columns = [
        { type: 'seq', width: 50 },
        { field: 'prodCenterName', title: '生产中心' },
        { field: 'prodCenterCode', title: '生产中心编码' },
        { field: 'name', title: '指标名称' },
        { field: 'lowLimit', title: '下限值' },
        { field: 'lowComparator', title: '下限值比较符' },
        { field: 'upLimit', title: '上限值' },
        { field: 'upComparator', title: '上限值比较符' },
        { field: 'baseValue', title: '基准值' },
        { field: 'tolerance', title: '公差' },
        { field: 'workClsass', title: '班次' },
        { field: 'value', title: '班次均值' },
        { field: 'workDate', title: '工作日期' },
        {
          field: 'passRate', 
          title: '合格率',
          // 格式化合格率为百分比（保留两位小数）
          formatter: ({ cellValue }) => {
            if (cellValue === null || cellValue === undefined) {
              return '-'; 
            }
            return (cellValue * 100).toFixed(2) + '%';
          }
        },
        // { field: 'workGroup', title: '班组' },
      ];
    },

    // 初始化图表
    initChart() {
      this.$nextTick(() => {
        const chartDom = this.$refs.chartRef;
        if (chartDom) {
          this.chartInstance = echarts.init(chartDom);
          this.updateChart();
        }
      });
    },

    // 更新图表数据
    updateChart() {
      if (!this.chartInstance) return;

      const option = {
        title: {
          text: '指标趋势图',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['班次均值', '合格率'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.chartData.dates,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '班次均值',
            position: 'left'
          },
          {
            type: 'value',
            name: '合格率(%)',
            position: 'right',
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '班次均值',
            type: 'line',
            data: this.chartData.values,
            smooth: true,
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '合格率',
            type: 'line',
            yAxisIndex: 1,
            data: this.chartData.passRates,
            smooth: true,
            itemStyle: {
              color: '#91cc75'
            }
          }
        ]
      };

      this.chartInstance.setOption(option);
    },

    // 处理图表数据
    processChartData(data) {
      const chartData = {
        dates: [],
        values: [],
        passRates: []
      };

      // 按日期分组数据
      const groupedData = {};
      data.forEach(item => {
        const date = item.workDate;
        if (!groupedData[date]) {
          groupedData[date] = {
            values: [],
            passRates: []
          };
        }
        if (item.value !== null && item.value !== undefined) {
          groupedData[date].values.push(item.value);
        }
        if (item.passRate !== null && item.passRate !== undefined) {
          groupedData[date].passRates.push(item.passRate * 100);
        }
      });
    },

    // 加载表格数据 - 使用新接口
    async loadTableData() {
      try {
        this.gridOptions.loading = true;
        // 只用于表格的参数
        const tableParams = { ...this.formOptions.data };
        // 用于图表的参数（不带workDate）
        const chartParams = { ...this.formOptions.data };
        delete chartParams.workDate;

        // 表格数据
        const response = await getGuidelineData(tableParams);
        // 图表数据
        const chartResponse = await getGuidelineData(chartParams);

        // 表格分页
        const { currentPage, pageSize } = this.gridOptions.pagerConfig;
        const total = response.data?.length || 0;
        this.gridOptions.pagerConfig.total = total;
        this.gridOptions.data = this.handlePagination(response.data || [], currentPage, pageSize);

        // 图表数据处理
        this.prepareGroupChartData(chartResponse.data || []);
        this.$nextTick(() => {
          this.renderAllCharts();
        });
      } catch (error) {
        console.error('加载数据失败:', error);
        this.$message.error('加载数据失败，请重试');
      } finally {
        this.gridOptions.loading = false;
      }
    },

    // 处理分页逻辑
    handlePagination(data, currentPage, pageSize) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      return data.slice(startIndex, endIndex);
    },

    // 分页变化处理
    handlePageChange({ currentPage, pageSize }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.loadTableData();
    },

    // 搜索事件
    handleSearch() {
      this.gridOptions.pagerConfig.currentPage = 1;
      const code = this.formOptions.data.prodCenterCode;
      if (code) {
        const furnaceName = this.furnaceList.find(name => code.includes(name[0]));
        if (furnaceName) {
          this.selectedFurnace = furnaceName;
        }
      }
      this.loadTableData();
    },

    // 重置事件
    handleReset() {
      const $form = this.$refs.searchFormRef;
      this.formOptions.data = {
        prodCenterCode: '', // 保留
        workDate: new Date().toISOString().split('T')[0],
        name: ''
      }
      if ($form) {
        $form.reset();
        this.handleSearch();
      }
    },

    // 处理分组图表数据
    prepareGroupChartData(data) {
      // 缓存原始数据
      this.originalData = data;
      
      // 收集所有高炉
      const furnaceSet = new Set();
      data.forEach(item => {
        const furnace = item.prodCenterName || item.prodCenterCode || '未知高炉';
        furnaceSet.add(furnace);
      });
      this.furnaceList = Array.from(furnaceSet);

      const codeToName = {
        'IPES01': '1#高炉',
        'IPES02': '2#高炉'
      };
      const code = this.formOptions.data.prodCenterCode;
      if (code && codeToName[code]) {
        this.selectedFurnace = codeToName[code];
      } else if (!this.selectedFurnace && this.furnaceList.length > 0) {
        this.selectedFurnace = this.furnaceList[0];
      }
      
      // 生成图表数据
      this.generateChartData();
    },

    // 生成图表数据
    generateChartData() {
      // 计算最近7天日期
      const today = new Date();
      const dateList = [];
      for (let i = 6; i >= 0; i--) {
        const d = new Date(today);
        d.setDate(today.getDate() - i);
        dateList.push(d.toISOString().split('T')[0]);
      }

      // 只处理当前高炉的数据
      const filteredData = this.originalData.filter(item => {
        const furnace = item.prodCenterName || item.prodCenterCode || '未知高炉';
        return furnace === this.selectedFurnace;
      });

      // 动态收集该高炉下所有实际出现过的指标
      const indicatorMap = {};
      filteredData.forEach(item => {
        const indicator = item.name || '未知指标';
        if (!indicatorMap[indicator]) {
          indicatorMap[indicator] = {
            '早': {},
            '中': {},
            '夜': {}
          };
        }
        const shift = item.workClsass;
        if (['早', '中', '夜'].includes(shift)) {
          indicatorMap[indicator][shift][item.workDate] = item.value;
        }
      });

      // 动态生成每个指标的折线图数据
      const chartGroupData = [];
      Object.keys(indicatorMap).forEach(indicator => {
        const shiftData = indicatorMap[indicator];
        const shiftList = ['早', '中', '夜'];
        const series = shiftList.map(shift => ({
          name: shift,
          type: 'line',
          data: dateList.map(date => shiftData[shift][date] ?? null),
          smooth: true,
          itemStyle: {
            color: this.getShiftColor(shift)
          }
        }));

        chartGroupData.push({
          title: `${indicator}`,
          dates: dateList,
          series
        });
      });

      this.chartGroupData = chartGroupData;
    },

    // 获取班次颜色
    getShiftColor(shift) {
      const colors = {
        '早': '#5470c6',
        '中': '#91cc75', 
        '夜': '#fac858'
      };
      return colors[shift] || '#5470c6';
    },

    // 更新所有图表
    updateAllCharts() {
      this.generateChartData();
      this.$nextTick(() => {
        this.renderAllCharts();
      });
    },

    // 渲染所有图表
    renderAllCharts() {
      // 销毁旧实例
      this.chartInstances.forEach(ins => ins && ins.dispose());
      this.chartInstances = [];

      this.chartGroupData.forEach((chart, idx) => {
        const refName = 'chartRef' + idx;
        const dom = this.$refs[refName];
        const chartDom = Array.isArray(dom) ? dom[0] : dom;
        
        if (chartDom) {
          const instance = echarts.init(chartDom);
          const option = {
            title: {
              text: `${this.selectedFurnace} - ${chart.title}`,
              left: 'center',
              textStyle: {
                fontSize: 16,
                fontWeight: 'bold'
              },
              subtext: chart.series.every(s => s.data.every(v => v == null)) ? '暂无数据' : ''
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross'
              }
            },
            legend: {
              data: ['早', '中', '夜'],
              top: 40
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '15%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              data: chart.dates,
              axisLabel: {
                rotate: 45
              }
            },
            yAxis: {
              type: 'value',
              name: '值'
            },
            series: chart.series
          };
          
          instance.setOption(option);
          this.chartInstances.push(instance);
        }
      });
    }
  }
};
</script>

<style scoped>
.guideline-container {
  height: 100vh;
  min-height: 400px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.guideline-table {
  height: 40%;
  flex-shrink: 0;
}

.furnace-selector {
  padding: 15px 20px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.charts-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.chart-container {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 20px;
  min-height: 400px;
}

.chart {
  width: 100%;
  height: 350px;
}
</style>