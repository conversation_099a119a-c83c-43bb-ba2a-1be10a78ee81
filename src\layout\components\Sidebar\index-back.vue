<!-- 菜单搜索功能、BUG较多暂时不对现场开放 -->
<template>
    <div :class="{ 'has-logo': showLogo }"
        :style="{ backgroundColor: settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }">
        <logo v-if="showLogo" :collapse="isCollapse" />
        <el-input size="small" v-model="filterText" @keyup.enter.native="getFilterText"
            placeholder="输入内容-回车生效"></el-input>
        <el-scrollbar :class="settings.sideTheme" wrap-class="scrollbar-wrapper">

            <el-menu :default-active="activeMenu" :collapse="isCollapse"
                :background-color="settings.sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground"
                :text-color="settings.sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
                :unique-opened="true" :active-text-color="settings.theme" :collapse-transition="false" mode="vertical">
                <sidebar-item v-for="(route, index) in filteredMenu" :key="route.path + index" :item="route"
                    :base-path="route.path" />
            </el-menu>
        </el-scrollbar>
    </div>
</template>

<script>
import { mapGetters, mapState } from "vuex";
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";

export default {
    components: { SidebarItem, Logo },
    data() {
        return {
            filterText: '',
            filteredMenu: [],
        }
    },
    mounted() {
        this.filteredMenu = this.permission_routes
    },
    methods: {
        getFilterText() {
            let menus = this.filterMenu(this.permission_routes, this.filterText)
            this.filteredMenu = menus

        },
        filterMenu(menu, searchText) {
        
            return menu.map(item => {
                let newItem = { ...item }; // Create a shallow copy of the item
                if (newItem.children && newItem.children.length) {
                    newItem.children = this.filterMenu(newItem.children, searchText);
                }
                return newItem;
            }).filter(item => {
                if (item.meta) {
                    const labelContains = item.meta.title && item.meta.title.toLowerCase().includes(searchText);
                    return labelContains || (item.children && item.children.length > 0);
                } else {
                    return false
                }

            });
        },
    },
    computed: {
        ...mapState(["settings"]),
        ...mapGetters(["permission_routes", "sidebar"]),
        activeMenu() {
            const route = this.$route;
            const { meta, path } = route;
            // if set path, the sidebar will highlight the path you set
            if (meta.activeMenu) {
                return meta.activeMenu;
            }
            console.log(path, 'path')
            return path;
        },
        showLogo() {
            return this.$store.state.settings.sidebarLogo;
        },
        variables() {
            return variables;
        },
        isCollapse() {
            return !this.sidebar.opened;
        }
    }
};
</script>
<style scoped>
/deep/ .el-input__inner {
    background-color: rgb(56, 76, 101);
    border-color: rgb(56, 76, 101);
    color: white;
}
</style>