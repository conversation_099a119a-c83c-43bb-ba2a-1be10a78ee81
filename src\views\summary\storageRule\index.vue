<template>
  <div class="app-container">
    <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">

      <el-form-item label="业务类型" prop="operType">
        <el-select v-model="queryParams.operType" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="报表单元" prop="reportUnitCode">
        <BReportUnitSelect ref="reportUnitRef" :prod-center-code="queryParams.prodCenterCode"
                           v-model="queryParams.reportUnitCode"
        />
      </el-form-item>
      <el-form-item label="包含班组" prop="isWorkClass">
        <el-select v-model="queryParams.isWorkClass" clearable placeholder="包含班组" style="width: 100%;">
          <el-option v-for="dict in isWorkClassList" :key="dict.key" :label="dict.value" :value="dict.key"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="规则" prop="rule">
        <el-input v-model="queryParams.rule" placeholder="请输入 规则"/>
      </el-form-item>
      <el-form-item label="结果信息" prop="resultCode">
        <el-input v-model="queryParams.resultCode" placeholder="请输入 规则"/>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="el-icon-plus"
          plain
          size="mini"
          type="primary"
          @click="handleAdd"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-edit"
          plain
          size="mini"
          type="success"
          @click="handleUpdate"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="el-icon-delete"
          plain
          size="mini"
          type="danger"
          @click="handleDelete"
        >删除
        </el-button>
      </el-col>

      <right-toolbar :showSearch.sync="showSearch" @queryTable="queryList"></right-toolbar>
    </el-row>
    <el-row>
      <el-col :span="6">
        <div class="prodTable">
          <BProdCenterTree ref="prodTree" :height="tableHeight" @currentChange="handleCurrentChange"/>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="tableInfo">
          <vxe-table
            ref="tableMainRef"
            :data="mainTableConfig.tableData"
            :row-config="{isHover: true}"
            :column-config="{resizable: true}"
            border
            @checkbox-change="mainTableCheckboxChange"
            @checkbox-all="mainTableCheckboxChangeAll"
            header-align="center"
            :height="tableHeight-55"
            stripe
          >
            <vxe-column align="center" fixed="left" type="checkbox" width="60"></vxe-column>
            <vxe-column field="rulesId" title="主键" width="auto"></vxe-column>
            <vxe-column field="operType" title="业务类型" width="auto"></vxe-column>
            <vxe-column field="prodCenterCode" title="加工中心编码" width="auto"></vxe-column>
            <vxe-column field="prodCenterName" title="加工中心名称" width="auto"></vxe-column>
            <vxe-column field="reportUnitCode" title="报表单元编码" width="auto"></vxe-column>
            <vxe-column field="reportUnitName" title="报表单元名称" width="auto"></vxe-column>
            <vxe-column field="isWorkClass" title="是否包含班组" width="auto"></vxe-column>
            <vxe-column field="masterGroupRoule" title="分组规则" width="auto"></vxe-column>
            <vxe-column field="resultRule1" title="存储规则1" width="auto"></vxe-column>
            <vxe-column field="resultCode1" title="结果编号1" width="auto"></vxe-column>
            <vxe-column field="resultName1" title="结果名称1" width="auto"></vxe-column>
            <vxe-column field="resultRule2" title="存储规则2" width="auto"></vxe-column>
            <vxe-column field="resultCode2" title="结果编号2" width="auto"></vxe-column>
            <vxe-column field="resultName2" title="结果名称2" width="auto"></vxe-column>
            <vxe-column field="resultRule3" title="存储规则3" width="auto"></vxe-column>
            <vxe-column field="resultCode3" title="结果编号3" width="auto"></vxe-column>
            <vxe-column field="resultName3" title="结果名称3" width="auto"></vxe-column>
            <vxe-column field="finalRoule1" title="值1规则" width="auto"></vxe-column>
            <vxe-column field="finalRoule2" title="值2规则" width="auto"></vxe-column>
            <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
            <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
            <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
            <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>

          </vxe-table>
          <vxe-pager
            :current-page.sync="mainTableConfig.pageConfig.pageNum"
            :page-size.sync="mainTableConfig.pageConfig.pageSize"
            :total="mainTableConfig.pageConfig.total"
            @page-change="pageChange"
          >
          </vxe-pager>
        </div>


      </el-col>
    </el-row>


    <!-- 添加或修改汇总配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" append-to-body width="900px">
      <el-form ref="form" :model="form" :rules="rules" label-width="auto">
        <el-row>
          <el-col :span="12">
            <el-form-item label="加工中心" prop="prodCenterCode">
              <BProdCenterTreeSelect ref="prodCenterRef"
                                     v-model="form.prodCenterCode"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报表单元" prop="reportUnitCode">
              <BReportUnitSelect ref="reportUnitRef" :prod-center-code="form.prodCenterCode"
                                 v-model="form.reportUnitCode"
              />
            </el-form-item>
          </el-col>

        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="包含班组" prop="isWorkClass">
              <el-select v-model="form.isWorkClass" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in isWorkClassList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">

            <el-form-item label="分组规则" prop="masterGroupRoule">
              <el-select v-model="form.masterGroupRoule" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleTypeList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>

          </el-col>

        </el-row>


        <el-row>
          <el-col :span="8">
            <el-form-item label="存储规则1" prop="resultRule1">
              <el-select v-model="form.resultRule1" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleTypeList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号1" prop="resultCode1">
              <el-input v-model="form.resultCode1" :disabled="form.resultRule1 != '固定值'"
                        placeholder="请输入结果编号1"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称1" prop="resultName1">
              <el-input v-model="form.resultName1" :disabled="form.resultRule1 != '固定值'"
                        placeholder="请输入结果名称1"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="存储规则2" prop="resultRule2">
              <el-select v-model="form.resultRule2" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleTypeList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号2" prop="resultCode2">
              <el-input v-model="form.resultCode2" :disabled="form.resultRule2 != '固定值'"
                        placeholder="请输入结果编号2"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称2" prop="resultName2">
              <el-input v-model="form.resultName2" :disabled="form.resultRule2 != '固定值'"
                        placeholder="请输入结果名称2"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="存储规则3" prop="resultRule3">
              <el-select v-model="form.resultRule3" clearable placeholder="包含班组" style="width: 100%;">
                <el-option v-for="dict in RuleTypeList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果编号3" prop="resultCode3">
              <el-input v-model="form.resultCode3" :disabled="form.resultRule3 != '固定值'"
                        placeholder="请输入结果编号3"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="结果名称3" prop="resultName3">
              <el-input v-model="form.resultName3" :disabled="form.resultRule3 != '固定值'"
                        placeholder="请输入结果名称3"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="值1规则" prop="finalRoule1">
              <el-select v-model="form.finalRoule1" clearable placeholder="存储规则2" style="width: 100%;">
                <el-option v-for="dict in RuleValueList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="值2规则" prop="finalRoule2">
              <el-select v-model="form.finalRoule2" clearable placeholder="存储规则2" style="width: 100%;">
                <el-option v-for="dict in RuleValueList" :key="dict.key" :label="dict.value" :value="dict.key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import BCollPointDialog from '@/components/BCollPointDialog/index.vue'
import BProdCenterTree from '@/components/BProdCenterTree/index.vue'
import BProdCenterTreeSelect from '@/components/BProdCenterTreeSelect/index.vue'
import BReportUnitSelect from '@/components/BReportUnitSelect/index.vue'
import BWorkModeSelect from '@/components/BWorkModeSelect/index.vue'
import {
  getRuleType,
  getRuleValue,
  getWorkClass,
  listRule,
  saveOrUpdate,
  getRule,
  delRule
} from '@/api/summary/storageRule'

export default {
  name: 'SummaryMaster',
  components: { BReportUnitSelect, BProdCenterTreeSelect, BProdCenterTree, BCollPointDialog, BWorkModeSelect },
  data() {
    return {
      options: [{
        value: '录入',
        label: '录入'
      }, {
        value: '数采',
        label: '数采'
      }, {
        value: '计算',
        label: '计算'
      }],
      isOpenPoint: false,

      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 汇总配置表格数据
      configList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      isWorkClassList: [],
      RuleTypeList: [],
      RuleValueList: [],
      editDetailTypeList: [],
      editAnalysisMethodList: [],
      editResultDetailConfigList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        reportUnitCode: null,
        operType: null,
        isWorkClass: null,
        rule: null,
        resultCode: null
      },
      // 表单参数
      form: {
        detailList: []
      },
      // 表单校验
      rules: {
        prodCenterCode: [
          {
            required: true, message: '加工中心 不能为空', trigger: 'blur'
          }
        ],
        reportUnitCode: [
          {
            required: true, message: '报表单元 不能为空', trigger: 'blur'
          }
        ],
        isWorkClass: [
          {
            required: true, message: '包含班组 不能为空', trigger: 'blur'
          }
        ],
        masterGroupRoule: [
          {
            required: true, message: '分组规则 不能为空', trigger: 'blur'
          }
        ],
        resultRule1: [
          {
            required: true, message: '存储规则1 不能为空', trigger: 'blur'
          }
        ]
        ,
        resultRule2: [
          {
            required: true, message: '存储规则2 不能为空', trigger: 'blur'
          }
        ],
        resultRule3: [
          {
            required: true, message: '存储规则3 不能为空', trigger: 'blur'
          }
        ],
        finalRoule1: [
          {
            required: true, message: '值1规则 不能为空', trigger: 'blur'
          }
        ],
        finalRoule2: [
          {
            required: true, message: '值2规则 不能为空', trigger: 'blur'
          }
        ]

      },
      tableHeight: 300,
      currentProdCenter: null,
      mainTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      }

    }
  },
  created() {
    getWorkClass().then((response) => {
      this.isWorkClassList = response.data
      console.log(this.isWorkClassList)
    })
    getRuleType().then((response) => {
      this.RuleTypeList = response.data
    })
    getRuleValue().then((response) => {
      this.RuleValueList = response.data
    })

  }
  ,
  methods: {
    pageChange({ pageSize, currentPage }) {
      this.mainTableConfig.pageConfig.pageNum = currentPage
      this.mainTableConfig.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTableConfig.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTableConfig.pageConfig.pageSize
      this.queryList()
    },
    queryList() {
      this.mainTableConfig.tableData = []
      if (this.currentProdCenter != null) {
        this.queryParams.prodCenterCode = this.currentProdCenter.prodCenterCode
      }
      listRule(this.queryParams).then(response => {
        this.mainTableConfig.tableData = response.rows
        this.mainTableConfig.pageConfig.total = response.total
      })
    },

    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    }
    ,
    // 表单重置
    reset() {
      this.form = {
        rulesId: null,
        operType: null,
        prodCenterCode: null,
        reportUnitCode: null,
        isWorkClass: null,
        masterGroupRoule: null,
        resultRule1: null,
        resultCode1: null,
        resultName1: null,
        resultRule2: null,
        resultCode2: null,
        resultName2: null,
        resultRule3: null,
        resultCode3: null,
        resultName3: null,
        finalRoule1: null,
        finalRoule2: null
      }
      this.resetForm('form')
    }
    ,
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryList()
    }
    ,
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
    ,

    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.form.isWorkClass = '不包含'
      this.form.masterGroupRoule = '无值'
      this.form.resultRule1 = '无值'
      this.form.resultRule2 = '无值'
      this.form.resultRule3 = '无值'
      this.form.finalRoule1 = '无值'
      this.form.finalRoule2 = '无值'
      this.open = true
      this.title = '添加存储规则'
    }
    ,
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message.warning('请选择一条数据进行修改！')
        return
      }
      if (selectedRows.length != 1) {
        this.$message.warning('同时只能对一掉数据进行修改')
        return
      }
      getRule(selectedRows[0].rulesId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改存储规则'
      })
    }
    ,
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.form.operType = '录入'
          saveOrUpdate(this.form).then(response => {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.open = false
            this.queryList()
          }).catch(error => {

          })
        }
      })
    }
    ,
    /** 删除按钮操作 */
    handleDelete(row) {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      if (selectedRows == null || selectedRows.length == 0) {
        this.$message({
          message: '请选择要删除的数据',
          type: 'warning'
        })
        return
      }
      const configIds = selectedRows.map(item => item.rulesId)
      this.$modal
        .confirm('是否确认删除？')
        .then(function() {
          return delRule(configIds)
        })
        .then(() => {
          this.queryList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {

        })
    }
    ,
    handleCurrentChange(selection) {
      this.currentProdCenter = selection
      this.form.detailList = selection.prodCenterCode
      this.queryList()
    },
    mainTableCheckboxChange() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    },
    mainTableCheckboxChangeAll() {
      const selectedRows = this.$refs.tableMainRef.getCheckboxRecords()
      this.mainTable.single = selectedRows.length !== 1
      this.mainTable.multiple = !selectedRows.length
    }
  },

  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10
    })
  }
}

</script>
