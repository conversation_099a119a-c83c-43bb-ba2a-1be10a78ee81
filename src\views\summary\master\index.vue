<template>
  <div class="app-container">
    <el-form ref="queryForm" :inline="true" :model="queryParams" label-width="auto" size="small">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="报表单元编码" prop="reportUnitCode">
        <BReportUnitSelect ref="reportUnitRef" v-model="queryParams.reportUnitCode"
                           :prod-center-code="queryParams.prodCenterCode"
        />
      </el-form-item>
      <el-form-item label="搜索关键字" prop="searchWorld">
        <el-input
          v-model="queryParams.searchWorld"
          clearable
          placeholder="搜索关键字"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>


    <el-row>
      <el-col :span="6">
        <div class="prodTable">
          <BProdCenterTree ref="prodTree" :height="productcenterHeight" @currentChange="handleCurrentChange"/>
        </div>

      </el-col>
      <el-col :span="18">
        <vxe-table
          ref="tableMainRef"
          :column-config="{resizable: true}"
          :data="mainTableConfig.tableData"
          :row-config="{isHover: true}"
          border
          header-align="center"
          height="400"
          stripe
          style="margin-left: 10px"
          @cell-click="cellClickEvent"
        >
          <vxe-column field="workDate" title="工作日期" width="auto"></vxe-column>
          <vxe-column field="prodCenterCode" title="加工中心编码" width="auto"></vxe-column>
          <vxe-column field="prodCenterName" title="加工中心名称" width="auto"></vxe-column>
          <vxe-column field="reportUnitCode" title="报表单元编码" width="auto"></vxe-column>
          <vxe-column field="reportUnitName" title="报表单元名称" width="auto"></vxe-column>
          <vxe-column field="workClsass" title="时间班组" width="auto"></vxe-column>
          <vxe-column field="workGroup" title="管理班组" width="auto"></vxe-column>
          <vxe-column field="masterGroup" title="分组" width="auto"></vxe-column>
          <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
          <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
          <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
          <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>
        </vxe-table>
        <vxe-pager
          :current-page.sync="mainTableConfig.pageConfig.pageNum"
          :page-size.sync="mainTableConfig.pageConfig.pageSize"
          :total="mainTableConfig.pageConfig.total"
          @page-change="pageChange"
        >
        </vxe-pager>
        <div class="detial_table">
          <vxe-table
            ref="tableDetailRef"
            :column-config="{resizable: true}"
            :data="detailTableConfig.tableData"
            :height="detilHeight"
            :row-config="{isHover: true}"
            border
            header-align="center"
            stripe
            style="margin-left: 10px; margin-top: 10px"
          >
            <vxe-column field="resultCode1" title="结果编号1" width="auto"></vxe-column>
            <vxe-column field="resultName1" title="结果名称1" width="auto"></vxe-column>

            <vxe-column field="resultCode2" title="结果编号2" width="auto"></vxe-column>
            <vxe-column field="resultName2" title="结果名称2" width="auto"></vxe-column>

            <vxe-column field="resultCode3" title="结果编号3" width="auto"></vxe-column>
            <vxe-column field="resultName3" title="结果名称3" width="auto"></vxe-column>

            <vxe-column field="colValue1" title="采集值1" width="auto"></vxe-column>
            <vxe-column field="inputValue1" title="录入值1" width="auto"></vxe-column>
            <vxe-column field="finalValue1" title="结果值1" width="auto"></vxe-column>
            <vxe-column field="valueType1" title="数据类型1" width="auto"></vxe-column>

            <vxe-column field="colValue2" title="采集值2" width="auto"></vxe-column>
            <vxe-column field="inputValue2" title="录入值2" width="auto"></vxe-column>
            <vxe-column field="finalValue2" title="结果值2" width="auto"></vxe-column>
            <vxe-column field="valueType2" title="数据类型2" width="auto"></vxe-column>

            <vxe-column field="resultGroup" title="结果组" width="auto"></vxe-column>
            <vxe-column field="operSouce" title="业务来源" width="auto"></vxe-column>

            <vxe-column field="tags" title="标签" width="auto"></vxe-column>
            <vxe-column field="sort" title="排序" width="auto"></vxe-column>
            <vxe-column field="remark" title="备注" width="auto"></vxe-column>

            <vxe-column field="createBy" title="创建者" width="auto"></vxe-column>
            <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
            <vxe-column field="updateBy" title="更新者" width="auto"></vxe-column>
            <vxe-column field="updateTime" title="更新时间" width="auto"></vxe-column>


          </vxe-table>
        </div>
      </el-col>
    </el-row>


  </div>
</template>

<script>
import {
  list, detailList
} from '@/api/summary/master'
import dayjs from 'dayjs'

import BProdCenterTree from '@/components/BProdCenterTree/index.vue'
import BReportUnitSelect from '@/components/BReportUnitSelect/index.vue'

export default {
  name: 'SummaryConfig',
  components: { BProdCenterTree, BReportUnitSelect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        prodCenterCode: null,
        reportUnitCode: null,
        searchWorld: null,
        dtStart: null,
        dtEnd: null
      },
      // 表单校验
      detilHeight: 300,
      productcenterHeight: 500,
      mainTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      detailTableConfig: {
        tableData: [],
        selectVO: '',
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }

      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).add(-1, 'day').startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))

  }
  ,
  methods: {
    pageChange({ pageSize, currentPage }) {
      this.mainTableConfig.pageConfig.pageNum = currentPage
      this.mainTableConfig.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTableConfig.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTableConfig.pageConfig.pageSize
      this.queryList()
    },

    /** 查询汇总配置列表 */
    getList(selectVO) {
      this.loading = true
      if (selectVO) {
        this.selectVO = selectVO
      }
      this.queryList()
    }
    ,
    queryList() {
      this.mainTableConfig.tableData = []
      this.detailTableConfig.tableData = []
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      list(this.queryParams, this.selectVO).then(response => {
        this.mainTableConfig.tableData = response.rows
        this.mainTableConfig.pageConfig.total = response.total
        if (this.mainTableConfig.tableData != null && this.mainTableConfig.tableData.length > 0) {
          const item = this.mainTableConfig.tableData[0]
          this.queryDetail(item.masterId, item.prodCenterCode)
        }

      })
    },
    queryDetail(id, prodCenterCode) {
      this.detailTableConfig.tableData = []
      const queryPar = { "masterId": id, "prodCenterCode": prodCenterCode }
      detailList(queryPar).then(response => {
        this.detailTableConfig.tableData = response.data
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.mainTableConfig.pageConfig.pageNum = 1
      this.getList()
    }
    ,
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    }
    ,
    handleCurrentChange(selection) {
      this.queryParams.prodCenterCode = selection.prodCenterCode
      this.getList(null)
    },
    cellClickEvent({ row, column }) {
      console.log(row)
      this.queryDetail(row.masterId,row.prodCenterCode)
    }
  },

  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('prodTable')[0].getBoundingClientRect().top
      this.productcenterHeight = document.body.clientHeight - topValue - 10

      topValue = document.getElementsByClassName('detial_table')[0].getBoundingClientRect().top
      this.detilHeight = document.body.clientHeight - topValue - 10
    })
  }
}

</script>
