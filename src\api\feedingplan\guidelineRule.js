import request from '@/utils/request'

// 获取计划数据模型
export function getRuleList(query) {
  return request({
    url: '/public/guideline/ruleList',
    method: 'get',
    params: query
  })
}

export function insertRuleData(data) {
  return request({
    url: '/public/guideline/addRule',
    method: 'post',
    data: data
  });
}


export function updateRuleData(data) {
  return request({
    url: '/public/guideline/editRule',
    method: 'put',
    data: data
  });
}


export function deleteRuleData(ids) {
  const idArray = Array.isArray(ids) ? ids : [ids]
  const encodedIds = idArray
    .map(id => encodeURIComponent(id))
    .join(',')
  return request({
    url: `/public/guideline/deleteRule/${encodedIds}`,
    method: 'post',
  });
}
