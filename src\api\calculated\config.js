import request from '@/utils/request'

// 查询汇总配置列表
export function listConfig(query,selectVO) {
  return request({
    url: '/api/calculatedCollectConfig/config/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 结果配置
export function resultDetailConfig() {
  return request({
    url: '/api/calculatedCollectConfig/config/calculatedResultDetailConfig',
    method: 'get'
  })
}

// 配置编号
export function createConfigCode(data) {
  return request({
    url: '/api/calculatedCollectConfig/config/calculatedCreateConfigCode',
    method: 'post',
    data: data
  })
}

// 分析方式
export function analysisMethod() {
  return request({
    url: '/api/calculatedCollectConfig/config/calculatedAnalysisMethod',
    method: 'get'
  })
}
// 汇总配置
export function summaryPropertyMethod() {
  return request({
    url: '/api/calculatedCollectConfig/config/summaryPropertyMethod',
    method: 'get'
  })
}

// 明细性质
export function detailTypeMethod() {
  return request({
    url: '/api/calculatedCollectConfig/config/detailTypeMethod',
    method: 'get'
  })
}

// 汇总配置提交
export function saveOrUpdate(data) {
  return request({
    url: '/api/calculatedCollectConfig/config/saveOrUpdate',
    method: 'post',
    data: data
  })
}


// 查询汇总配置详细
export function getConfig(configId) {
  return request({
    url: '/api/calculatedCollectConfig/config/' + configId,
    method: 'get'
  })
}



// 查询依赖配置详细
export function getByConfigCode(data) {
  return request({
    url: '/api/calculatedCollectConfig/config/getByConfigCode',
    method: 'post',
    data: data
  })
}

// 删除汇总配置
export function delConfig(configId) {
  return request({
    url: '/api/calculatedCollectConfig/config/' + configId,
    method: 'delete'
  })
}

// 查询配置编码
export function formRelianceOptionMethod(data) {
  return request({
    url: '/api/calculatedCollectConfig/config/formRelianceOptionMethod',
    method: 'post',
    data: data
  })
}

// 配置编码 插入依赖表
export function relianceDataMethod(data) {
  return request({
    url: '/api/reliance/add',
    method: 'post',
    data: data
  })
}

export function syncCalculatedStoreRule(data) {
  return request({
    url: '/api/calculatedCollectConfig/config/syncCalculatedStoreRule',
    method: 'get',
    data: data
  })
}
