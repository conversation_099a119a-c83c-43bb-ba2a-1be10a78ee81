import request from "@/utils/request";

// 查询物料管理列表
export function listMaterial(query) {
  return request({
    url: "/api/md/material/list",
    method: "get",
    params: query,
  });
}

// 物料扩展表使用
export function listMaterialNew(query) {
  return request({
    url: "/api/md/material/listNew",
    method: "get",
    params: query,
  });
}

export function queryFormManger(query) {
  return request({
    url: "/api/md/material/queryFormManger",
    method: "get",
    params: query,
  });
}

export function chooseMaterial(query) {
  return request({
    url: "/api/md/material/chooseMaterial",
    method: "get",
    params: query,
  })
}

// 查询物料管理详细
export function getMaterial(materialId) {
  return request({
    url: "/api/md/material/" + materialId,
    method: "get",
  });
}

export function searchByCode(matecode) {
  return request({
    url: "/api/md/material/searchByCode/" + matecode,
    method: "get",
  });
}

// 查询物料管理详细
export function getSaveEntity(materialId) {
  return request({
    url: "/api/md/material/getSaveEntity/" + materialId,
    method: "get",
  });
}

// 新增物料管理
export function saveOrUpdate(data) {
  return request({
    url: "/api/md/material/saveOrUpdate",
    method: "post",
    data: data,
  });
}

// 删除物料管理
export function delMaterial(materialId) {
  return request({
    url: "/api/md/material/" + materialId,
    method: "delete",
  });
}
// 查询生效的所有物料名称详细
export function treeMaterial() {
  return request({
    url: "/api/md/material/treeMaterial",
    method: "get",
  });
}

export function queryAllUsed() {
  return request({
    url: "/api/md/material/queryAllUsed",
    method: "get",
  });
}

//查询根据物料名称查询物料编码
export function searchByMaterialName(name) {
  const query = { materialName: name };
  return request({
    url: '/api/md/material/searchByMaterialName',
    method: 'get',
    params: query
  })
}

export function searchByTags(tags) {
  return request({
    url: '/api/md/material/searchByTags/' + tags,
    method: 'get',
  })
}

// 查询生效的所有物料名称详细
export function getUsedMaterName() {
  return request({
    url: "/api/md/material/getUsedMaterName",
    method: "get",
  });
}


export function initMaterialList() {
  return request({
    url: "/api/md/material/initMaterial",
    method: "get",
  });
}