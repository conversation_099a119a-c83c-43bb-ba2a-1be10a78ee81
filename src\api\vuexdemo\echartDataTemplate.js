
// echart图表 数据模板
export function echartsComm(query) {
  query = [
    // 柱形图
    {
      businessCode: "LT001",
      description: "炼铁铁水检化验",
      echarType:"柱形图",
      X_bar_axis: [
        {
          name: "时间",
          tableField: "date"
        }
      ],
      Y_bar_axis: [{
        name: "年龄",
        tableField: "age",
        fileIndex: []
      },
       ],
    },
    // 曲线图
    {
      businessCode: "SJ001",
      description: "烧结矿检化验",
      echarType:"曲线图",
      X_line_axis: [
        {
          name: "时间",
          tableField: "date"
        }
      ],
      Y_line_axis: [
        {
          name: "年龄",
          tableField: "age",
          color:"#2f4554",
          fileIndex: [],
        },
        {
          name: "碳",
          tableField: "C",
          fileIndex2: []
        },
      ],
    }
  ]
  return query;
}


