<template>
  <el-select v-model="selectValue" placeholder="请选择包含内容" style="width: 100%;" @change="selectChange" clearable>
    <el-option v-for="dict in editReportList" :key="dict.reportUnitCode" :label="dict.reportUnitName"
               :value="dict.reportUnitCode"
    >
      <span>{{ dict.reportUnitCode }}</span>-<span>{{ dict.reportUnitName }}</span>
    </el-option>
  </el-select>
</template>
<script>
import { getByProdCenterCode } from '@/api/md/reportunit'

export default {
  name: 'BReportUnitSelect',
  props: {
    prodCenterCode: {
      type: String,
      default: ''
    },
    value: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data() {
    return {
      editReportList: []
    }
  },
  methods: {
    selectChange(val) {
      this.$emit('change', val)
    },
    clearSelect(){
      this.selectValue=null;
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    prodCenterCode: {
      immediate: true,
      deep: true,
      handler(val) {
        if(val!=null && val!==''){
          getByProdCenterCode(val).then(res => {
            this.editReportList = res.data
          })
        }
      }
    }
  }
}
</script>
