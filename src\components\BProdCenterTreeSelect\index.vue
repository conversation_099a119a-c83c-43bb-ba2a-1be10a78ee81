<template>
  <treeselect :clearable="false" v-model="selectValue" @select="prodCenterTreeChange"
              :options="editProductCenterList" :show-count="true" placeholder="请选择加工中心"
  />
</template>
<script>
import { treeselectCode,getProductcenter,queryByCode } from '@/api/md/productcenter'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'BProdCenterTreeSelect',
  components: {Treeselect },
  props: {
    value: {
      type: [String], // 根据实际情况定义类型
      default: ''
    }
  },
  data() {
    return {
      editProductCenterList: [],
      currentProdCenter: null,
    }
  },
  computed: {
    selectValue:{
      get(){
        return this.value
      },
      set(value){
        this.$emit('input',value)
      }
    }
  },
  created() {
    treeselectCode().then(res => {
      this.editProductCenterList=res.data
    })
  },
  methods: {
    prodCenterTreeChange(selection) {
      queryByCode(selection.id).then(response => {
        this.$emit('currentChange', response.data)
      })
    },
  },
}
</script>
