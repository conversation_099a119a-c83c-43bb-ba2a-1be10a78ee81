<template>
  <el-select v-model="selectValue"  clearable style="width: 100%;">
    <el-option v-for="dict in workModeIdList" :key="dict.id" :label="dict.workModeName"
               :value="dict.id"
    ></el-option>
  </el-select>
</template>
<script>
import { queryAllUsed } from '@/api/system/work.js'

export default {
  name: 'BWorkModeSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    }
  },
  data() {
    return {
      workModeIdList: []
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    prodCenterCode: {
      immediate: true,
      deep: true,
      handler(val) {
        queryAllUsed().then((response2) => {
          this.workModeIdList = response2.data
        })      }
    }
  }
}
</script>
