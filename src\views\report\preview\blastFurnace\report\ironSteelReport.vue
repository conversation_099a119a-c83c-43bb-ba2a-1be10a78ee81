<template>
  <div style="width: 100%; height: 100%; position: absolute; top: 0;left: 0;">
    <iframe :src="bingUrl" scrolling="auoto" style="width: 100%; height: 100%; position: absolute; top: 0;left: 0;">
    </iframe>
  </div>
</template>

<script>
    export default {
      name: "ironSteelReport",
      props: ['url'],
      data() {
        return {
          bingUrl: '',
        }
      },
      created() {
        var usedURL;
        if (this.url == undefined) {
          usedURL = this.$route.query.url
        } else {
          usedURL = this.url;
        }
        console.log("usedURL:",usedURL)
        // if (usedURL.includes('*********')) {
        //   let currentDomain = window.location.hostname;
        //   let port = window.location.port;
        //   var str = currentDomain;
        //   if (port != null) {
        //     str = str + ":" + port
        //   }
        //   usedURL = usedURL.replace("*********",str);
        //   console.log(currentDomain);
        //   console.log(usedURL);
        //   console.log("字符串包含子字符串");
        // }
        this.bingUrl = usedURL;
      },
    }

</script>

<style scoped>

</style>
