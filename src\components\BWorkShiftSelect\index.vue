<template>
  <el-select v-model="selectValue" clearable style="width: 100%;" @change="handleSelectChange">
    <el-option v-for="dict in workModeIdList" :key="dict.workShiftName" :label="dict.workShiftName"
               :value="dict.workShiftName"
    ></el-option>
  </el-select>
</template>
<script>
import { queryWorkShift } from '@/api/system/work.js'

export default {
  name: 'BWorkShiftSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    modelName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      workModeIdList: []
    }
  },
  methods: {
    handleSelectChange(val) {
      this.$emit('change', val)
    }
  },
  computed: {
    selectValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
    modelName: {
      immediate: true,
      deep: true,
      handler(val) {
        queryWorkShift(val).then((response2) => {
          this.workModeIdList = response2.data
        })
      }
    }
  }
}
</script>
