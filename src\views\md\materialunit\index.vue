<template>
  <div class="app-container">
    <el-row :gutter="10">
      <el-col :span="4">
        <el-card >
          <div slot="header" class="clearfix">
            <span>单位分组</span>
          </div>
          <el-table ref="groupTable" :data="material_unit_grop" @current-change="handleCurrentChange"
            highlight-current-row>
            <af-table-column label="分组名称" align="center"  prop="dictLabel" />
          </el-table>
        </el-card>
      </el-col>

      <el-col :span="20">
        <el-card >
          <div slot="header" class="clearfix">
            <span>单位</span>
          </div>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                >新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                >修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
                >删除</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
               >导出</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <el-table v-loading="loading" :data="unitList" @selection-change="handleSelectionChange">
            <af-table-column type="selection" width="55" align="left" header-align="center" />

            <af-table-column label="名称" align="left" header-align="center" prop="unitName" />
            <af-table-column label="编码" align="left" header-align="center" prop="unitNumber" />
            <af-table-column label="分组" align="left" header-align="center" prop="unitGropCode" />

            <af-table-column label="换算系数" align="left" header-align="center" prop="conversionFactor" />
            <af-table-column label="是否基本单位" align="left" header-align="center" prop="isBaseunit">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.isBaseunit == '是'" type="success">{{
                  scope.row.isBaseunit
                }}</el-tag>
                <el-tag v-if="scope.row.isBaseunit == '否'" type="danger">{{
                  scope.row.isBaseunit
                }}</el-tag>
              </template>
            </af-table-column>

            <af-table-column label="状态" align="left" header-align="center" prop="unitStatus">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.unitStatus == '生效'" type="success">{{
                  scope.row.unitStatus
                }}</el-tag>
                <el-tag v-if="scope.row.unitStatus == '失效'" type="danger">{{
                  scope.row.unitStatus
                }}</el-tag>
              </template>
            </af-table-column>
            <af-table-column label="描述" align="left" header-align="center" prop="remark" />

            <af-table-column label="更新者" align="left" header-align="center" prop="updateBy" />
            <af-table-column label="更新时间" align="left" header-align="center" prop="updateTime" width="180">
              <template slot-scope="scope">
                <span>{{
                  parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{m}:{s}")
                }}</span>
              </template>
            </af-table-column>
            <af-table-column label="操作" align="left" header-align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  >修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  >删除</el-button>
              </template>
            </af-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加或修改物料-单位对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="名称" prop="unitName">
              <el-input v-model="form.unitName" placeholder="请输入名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="编码" prop="unitNumber">
              <el-input v-model="form.unitNumber" placeholder="请输入编码" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="分组" prop="unitGropCode">
              <el-select v-model="form.unitGropCode" placeholder="请选择分组">
                <el-option v-for="dict in material_unit_grop" :key="dict.dictValue" :label="dict.dictLabel"
                  :value="dict.dictValue"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="unitStatus">
              <el-select v-model="form.unitStatus" placeholder="请选择状态">
                <el-option v-for="dict in dict.type.effective_or_not" :key="dict.value" :label="dict.label"
                  :value="dict.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="基本单位" prop="isBaseunit">
              <el-select v-model="form.isBaseunit" placeholder="请选择是否基本单位">
                <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.label" :label="dict.label"
                  :value="dict.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="换算系数" prop="conversionFactor">
              <el-input-number style="width: 180px" v-model="form.conversionFactor" :precision="5" :step="0.1" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listUnit,
  getUnit,
  delUnit,
  addUnit,
  updateUnit,
} from "@/api/md/materialUnit";

import { getDicts } from "@/api/system/dict/data";

export default {
  name: "Unit",
  dicts: ["effective_or_not", "sys_yes_no"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 物料-单位表格数据
      unitList: [],
      material_unit_grop: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //当前选中分组
      currentGroup: {},
      // 查询参数
      queryParams: {
        unitGropCode: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        unitName: [
          { required: true, message: "名称不能为空", trigger: "blur" },
        ],
        unitNumber: [
          { required: true, message: "编码不能为空", trigger: "blur" },
        ],
        unitGropCode: [
          { required: true, message: "分组不能为空", trigger: "change" },
        ],
        unitStatus: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
      },
    };
  },
  created() {
    getDicts("material_unit_grop").then((response) => {
      this.material_unit_grop = response.data;
      this.$refs.groupTable.setCurrentRow(this.material_unit_grop[0]);
      this.getList();
    });
  },
  methods: {
    handleCurrentChange(val) {
      this.currentGroup = val;
      this.getList();
    },
    /** 查询物料-单位列表 */
    getList() {
      this.loading = true;
      if (this.currentGroup != undefined) {
        this.queryParams.unitGropCode = this.currentGroup.dictValue;
      }
      listUnit(this.queryParams).then((response) => {
        this.unitList = response.data;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        unitId: null,
        unitName: null,
        unitNumber: null,
        unitGropCode: null,
        unitStatus: "生效",
        conversionFactor: null,
        isBaseunit: "否",
        remark: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.unitId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加物料-单位";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const unitId = row.unitId || this.ids;
      getUnit(unitId).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改物料-单位";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.unitId != null) {
            updateUnit(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUnit(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const unitIds = row.unitId || this.ids;
      this.$modal
        .confirm('是否确认删除物料？')
        .then(function () {
          return delUnit(unitIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "/api/md/materialunit/export",
        {
          ...this.queryParams,
        },
        `物料单位_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
