import request from '@/utils/request'

// 查询操作日志记录列表
export function listIron(query) {
  return request({
    url: '/api/blastFurnace/iron/list',
    method: 'get',
    params: query
  })
}

// 提交按钮
export function submitData(data) {
  return request({
    url: '/api/blastFurnace/iron/add',
    method: 'post',
    data: data
  })
}

// 根据选中 铁次号的 id 查询罐重
export function selectIronNumberCanWeight(data) {
  return request({
    url: '/api/blastFurnace/iron/selectIronNumberCanWeight',
    method: 'post',
    data: data
  })
}

// 根据选中 铁次号的 id 查询物理热
export function selectIronNumberPhtsicalHeat(data) {
  return request({
    url: '/api/blastFurnace/iron/selectIronNumberPhtsicalHeat',
    method: 'post',
    data: data
  })
}

// 根据选中 铁次号的 id 查询 铁水成分
export function selectIronNumberLiQuidIron(data) {
  return request({
    url: '/api/blastFurnace/iron/selectIronNumberLiQuidIron',
    method: 'post',
    data: data
  })
}

// 根据选中 铁次号的 id 查询 炉渣成分
export function selectIronNumberSlag(data) {
  return request({
    url: '/api/blastFurnace/iron/selectIronNumberSlag',
    method: 'post',
    data: data
  })
}

// 高炉铁次号数据 编辑
export function ironEdit(data) {
  return request({
    url: '/api/blastFurnace/iron/ironEdit',
    method: 'put',
    data: data
  })
}

// 高炉罐号数据 编辑
export function canEdit(data) {
  return request({
    url: '/api/blastFurnace/iron/canEdit',
    method: 'put',
    data: data
  })
}

// 铁次罐号数据 提交按钮
export function insertIronWeight(data) {
  return request({
    url: '/api/blastFurnace/iron/insertIronWeight',
    method: 'post',
    data: data
  })
}
