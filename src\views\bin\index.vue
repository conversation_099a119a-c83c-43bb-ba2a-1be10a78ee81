<template>
    <div>
        <el-form style="border: 1px solid #C4E1C5; padding: 20px;" label-width="50px" :inline="true">
            <el-row>
                <el-form-item label="物料">
                    <el-input></el-input>
                </el-form-item>
            </el-row>

            <el-row type="flex" justify="center">
                <el-form-item>
                    <el-button type="primary" icon="el-icon-search" size="mini">搜索</el-button>
                </el-form-item>
            </el-row>
        </el-form>

        <vxe-grid v-bind="gridOptions"></vxe-grid>

    </div>
</template>

<script>

export default {
    name: 'index',

    data() {
        return {

            gridOptions: {
                emptyText: '没有更多数据了！',
                height: 778,
                border: true,
                align: 'center',
                columns: [
                    { type: 'seq', width: 50 },
                    { field: 'warehouse', title: '原料库' },
                    { field: 'mateCode', title: '物料编码' },
                    { field: 'mateName', title: '物料名称' },
                    { field: 'line', title: '料条' },
                    { field: 'stack', title: '垛位' },
                    { field: 'startP', title: '起点(米)' },
                    { field: 'endP', title: '终点(米)' },
                ],
                data: [
                    { id: 1, warehouse: '1#原料库', line: 'A', stack: '1', startP: 20, endP: 100, mateCode: 'wl1001', mateName: 'xxx物料' },
                    { id: 1, warehouse: '1#原料库', line: 'A', stack: '1', startP: 20, endP: 100, mateCode: 'wl1001', mateName: 'xxx物料' },
                    { id: 1, warehouse: '1#原料库', line: 'A', stack: '1', startP: 20, endP: 100, mateCode: 'wl1001', mateName: 'xxx物料' },
                    { id: 1, warehouse: '1#原料库', line: 'A', stack: '1', startP: 20, endP: 100, mateCode: 'wl1001', mateName: 'xxx物料' },
                ]
            },
        }
    }
}
</script>

<style scoped>
.el-input,
.el-select,
.el-cascader {
    width: 100% !important;
}
</style>
