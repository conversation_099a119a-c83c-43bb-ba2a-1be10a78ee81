<template>
    <div class="app-container" style="padding:10px;">
        <el-form class="queryForm" :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label-width="80px" label="创建时间">
                <el-date-picker v-model="dateRange" type="datetimerange" range-separator="至" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
            </el-form-item>
            <el-form-item label="加工单名称" label-width="90px" prop="orderName">
                <el-input v-model="queryParams.orderName" placeholder="请输加工单名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="物料名称" prop="mateName">
                <el-input v-model="queryParams.mateName" placeholder="请输入物料名称" clearable
                    @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>

            </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增加工单
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-edit" size="mini" @click="handleCancle">撤销加工单
                </el-button>
            </el-col>


            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <div class="mes_table">
            <dlTable size="small" highlight-current-row refName="dlTable" :stripe="true" :border="true" :height="400"
                :columns="columns" :pageConfig="pageConfig" :tableData="tableData" :basicConfig="basicConfig"
                @handleOrder="getList" @handleFilter="getList" @size-change="sizeChange" @row-click="chooseRowClick"
                @page-current-change="numChange">
            </dlTable>
        </div>

        <el-tabs type="border-card" style="margin-top: 10px;">
            <el-tab-pane label="原料">
                <div class="mes_new_table">
                    <el-table size="mini" :data="rawTableList" border height="200" style="width: 100%;">
                        <af-table-column label="仓库编码" prop="storehouseCode" />
                        <af-table-column label="仓库名称" prop="storehouseName" />
                        <af-table-column label="物料编码" prop="mateCode" />
                        <af-table-column label="物料名称" prop="mateName" width="240" />
                        <af-table-column label="跨区" prop="crossRegion" />
                        <af-table-column label="垛位" prop="stackingPosition" />
                        <af-table-column label="船号" prop="stockLot" width="200" />
                        <af-table-column label="重量" prop="weight" />
                        <af-table-column label="计量单位" prop="unitName" />
                    </el-table>
                </div>
            </el-tab-pane>
            <el-tab-pane label="成品">
                <div class="mes_new_table">
                    <el-table size="mini" :data="finishTableList" border height="200" style="width: 100%;">
                        <af-table-column label="仓库编码" prop="storehouseCode" />
                        <af-table-column label="仓库名称" prop="storehouseName" />
                        <af-table-column label="物料编码" prop="mateCode" />
                        <af-table-column label="物料名称" prop="mateName" width="240" />
                        <af-table-column label="跨区" prop="crossRegion" />
                        <af-table-column label="垛位" prop="stackingPosition" />
                        <af-table-column label="船号" prop="stockLot" width="200" />
                        <af-table-column label="重量" prop="weight" />
                        <af-table-column label="计量单位" prop="unitName" />
                    </el-table>
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 添加或修改仓储-加工单对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="1500px" append-to-body>
            <el-form ref="form" :model="form" :rules="rules" label-width="90px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="加工单名称" prop="orderName">
                            <el-input v-model="form.orderName" placeholder="请输入加工单名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="备注" prop="remark">
                            <el-input v-model="form.remark" placeholder="请输入备注" />
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-card>
                    <div slot="header" class="clearfix">
                        <span>原料</span>
                        <el-button-group style="float: right;">
                            <el-button type="primary" icon="el-icon-plus" @click="raw_AddClick"
                                size="mini">新增</el-button>
                            <el-button type="danger" icon="el-icon-delete" @click="raw_celar" size="mini">清空</el-button>
                        </el-button-group>
                    </div>

                    <el-table :data="form.rawList" height="200" :border="true">

                        <af-table-column label="物料" prop="mateCode">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.mateCode" filterable placeholder="请选择">
                                    <el-option v-for="item in materialList" :key="item.materialNumber"
                                        :label="item.materialName" :value="item.materialNumber">
                                        <span style="float: left">{{ item.materialName }}-{{ item.materialNumber
                                            }}</span>
                                        <span v-if="item.tMdMaterialCategory != null"
                                            style="float: right; color: #8492a6; font-size: 13px">{{
                                                item.tMdMaterialCategory.categoryName
                                            }}</span>
                                    </el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <af-table-column label="垮区" prop="crossRegion">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.crossRegion" placeholder="请选择垮区"
                                    @change="raw_change($event, scope.row)">
                                    <el-option v-for="dict in scope.row.crossRegionlist" :key="dict" :label="dict"
                                        :value="dict"></el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <af-table-column label="垛位" prop="stackingPosition">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.stackingPosition" placeholder="请选择采集值">
                                    <el-option v-for="dict in scope.row.stackingPositionList" :key="dict" :label="dict"
                                        :value="dict"></el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <!-- <af-table-column label="船号" prop="stockLot">
                            <template slot-scope="scope">
                                <el-select allow-create v-model="scope.row.stockLot" clearable filterable
                                    placeholder="请选择" style="width: 100%;">
                                    <el-option v-for="item in shipNumberList" :key="item" :label="item" :value="item">
                                    </el-option>
                                </el-select>

                            </template>
                        </af-table-column> -->
                        <af-table-column label="重量" prop="weight">
                            <template slot-scope="scope">
                                <el-input-number v-model="scope.row.weight" :precision="4" :min="0"
                                    :step="0.1"></el-input-number>
                            </template>
                        </af-table-column>
                    </el-table>
                </el-card>

                <el-card style="margin-top: 10px;">
                    <div slot="header" class="clearfix">
                        <span>成品</span>
                        <el-button-group style="float: right;">
                            <el-button type="primary" icon="el-icon-plus" @click="finish_AddClick"
                                size="mini">新增</el-button>
                            <el-button type="danger" icon="el-icon-delete" @click="finish_celar"
                                size="mini">清空</el-button>
                        </el-button-group>
                    </div>

                    <el-table :data="form.finishList" height="200" :border="true">
                        <af-table-column label="物料" prop="mateCode">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.mateCode" filterable placeholder="请选择">
                                    <el-option v-for="item in materialList" :key="item.materialNumber"
                                        :label="item.materialName" :value="item.materialNumber">
                                        <span style="float: left">{{ item.materialName }}-{{ item.materialNumber
                                            }}</span>
                                        <span v-if="item.tMdMaterialCategory != null"
                                            style="float: right; color: #8492a6; font-size: 13px">{{
                                                item.tMdMaterialCategory.categoryName
                                            }}</span>
                                    </el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <af-table-column label="垮区" prop="crossRegion">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.crossRegion" placeholder="请选择垮区"
                                    @change="finish_change($event, scope.row)">
                                    <el-option v-for="dict in scope.row.crossRegionlist" :key="dict" :label="dict"
                                        :value="dict"></el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <af-table-column label="垛位" prop="stackingPosition">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.stackingPosition" placeholder="请选择采集值">
                                    <el-option v-for="dict in scope.row.stackingPositionList" :key="dict" :label="dict"
                                        :value="dict"></el-option>
                                </el-select>
                            </template>
                        </af-table-column>
                        <!-- <af-table-column label="船号" prop="stockLot">
                            <template slot-scope="scope">
                                <el-select allow-create v-model="scope.row.stockLot" clearable filterable
                                    placeholder="请选择" style="width: 100%;">
                                    <el-option v-for="item in shipNumberList" :key="item" :label="item" :value="item">
                                    </el-option>
                                </el-select>
                            </template>
                        </af-table-column> -->
                        <af-table-column label="重量" prop="weight">
                            <template slot-scope="scope">
                                <el-input-number v-model="scope.row.weight" :precision="4" :min="0"
                                    :step="0.1"></el-input-number>
                            </template>
                        </af-table-column>
                    </el-table>
                </el-card>

            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>

import {
    listManger,
    getCrossRegion,
    getPostion,
    confrimOrder,
    cancleOrder,
    queryFinish,
    queryRaw,
    getOrder

} from "@/api/wms/processingOrder";
import { queryAllUsed } from "@/api/md/material";
import dayjs from "dayjs";

export default {
    name: "processingOrder",
    data() {
        return {
            // 遮罩层
            loading: true,
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 仓储-加工单表格数据
            orderList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            processingOrderId: null,
            // 日期范围
            dateRange: [],
            materialList: [],
            shipNumberList: [],
            rawTableList: [],
            finishTableList: [],
            // 查询参数
            queryParams: {
                pageNum: 1,
                pageSize: 20,
                orderName: null,
                storehouseCode: null,
                mateName: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {

            },
            basicConfig: {
                index: false, // 是否启用序号列
                needPage: true, // 是否展示分页
                indexName: null, // 序号列名(默认为：序号)
                selectionType: false, // 是否启用多选框
                indexWidth: null, // 序号列宽(默认为：50)
                indexFixed: null, // 序号列定位(默认为：left)
                settingType: true, // 是否展示表格配置按钮
                headerSortSaveType: false // 表头排序是否保存在localStorage中
            },
            pageConfig: {
                pageNum: 1, // 页码
                pageSize: 20, // 每页显示条目个数
                total: 0, // 总数
                background: true, // 是否展示分页器背景色
                pageSizes: [10, 20, 50, 100]// 分页器分页待选项
            },
            columns: [
                {
                    label: '加工中心编码', // 表头描述
                    fieldIndex: 'prodCenterCode', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '加工中心名称', // 表头描述
                    fieldIndex: 'prodCenterName', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '仓库编码', // 表头描述
                    fieldIndex: 'storehouseCode', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '仓库名称', // 表头描述
                    fieldIndex: 'storehouseName', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '加工单编码', // 表头描述
                    fieldIndex: 'orderCode', // 表格显示内容绑定值
                    width: 150,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '加工单名称', // 表头描述
                    fieldIndex: 'orderName', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '加工单状态', // 表头描述
                    fieldIndex: 'orderStatus', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '原料总量', // 表头描述
                    fieldIndex: 'rawWeight', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '原料物料', // 表头描述
                    fieldIndex: 'rawMate', // 表格显示内容绑定值
                    width: 150,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                    showOverFlowTooltip: true,
                },
                {
                    label: '成品总量', // 表头描述
                    fieldIndex: 'finishedWeight', // 表格显示内容绑定值
                    width: 150,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '成品物料', // 表头描述
                    fieldIndex: 'finishedMate', // 表格显示内容绑定值
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    showOverFlowTooltip: true,
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '备注', // 表头描述
                    fieldIndex: 'remark', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '创建者', // 表头描述
                    fieldIndex: 'createBy', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '创建时间', // 表头描述
                    fieldIndex: 'createTime', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '更新者', // 表头描述
                    fieldIndex: 'updateBy', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                },
                {
                    label: '更新时间', // 表头描述
                    fieldIndex: 'updateTime', // 表格显示内容绑定值
                    width: 130,
                    sortable: true, // 此属性可以设置排序
                    filterable: true, // 是否筛选 默认为false
                    searchField: null,// 请求后台所用字段,默认使用fieldIndex驼峰转下划线
                    concise: true, // 是否显示筛选icon 和排序 icon
                }

            ],
            tableData: [],
            selectVO: '',
            dltableHeight: 600,
        };
    },
    created() {
        queryAllUsed().then((response) => {
            this.materialList = response.data;
        })

        this.dateRange.push(dayjs(new Date()).add(-1, "day").startOf('date'));
        this.dateRange.push(dayjs(new Date()).endOf('date'));
        this.getList(null);
    },
    mounted() {
        this.$nextTick(() => {
            /**mes_new_table 到顶部的高炉 */
            let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
            this.dltableHeight = document.body.clientHeight - topValue - 50;
        })
    },
    methods: {
        /** pageNum事件 */
        numChange(pageNum, selectVO) {
            this.pageConfig.pageNum = pageNum;
            this.queryParams.pageNum = pageNum;
            this.selectVO = selectVO;
            this.getList(selectVO);
        },
        /** pageSize事件 */
        sizeChange(pageSize, selectVO) {
            this.pageConfig.pageSize = pageSize;
            this.queryParams.pageSize = pageSize;
            this.selectVO = selectVO;
            this.getList(selectVO);
        },
        /** 查询仓储-加工单列表 */
        getList(selectVO) {
            this.loading = true;
            if (selectVO) {
                this.selectVO = selectVO;
            }
            this.queryList();
        },
        queryList() {
            if (this.dateRange.length == 2) {
                this.queryParams.dtStart = dayjs(this.dateRange[0]).format("YYYY-MM-DD 00:00:00");
                this.queryParams.dtEnd = dayjs(this.dateRange[1]).format("YYYY-MM-DD 23:59:59");
            }
            this.queryParams.storehouseCode = this.getHouseCode();
            this.rawTableList = [];
            this.finishTableList = [];
            // listManger(this.queryParams, this.selectVO).then(response => {
            //     this.tableData = response.rows
            //     this.pageConfig.total = response.total;
            //     this.loading = false;
            //     if (this.tableData.length != 0) {
            //         this.chooseRowClick(this.tableData[0], null, null);
            //     }

            // });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.form = {
                storehouseCode: null,
                orderName: null,
                remark: null,
                rawList: [],
                finishList: [],
            };
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.pageNum = 1;
            this.getList();
        },

        chooseRowClick(row, column, event) {
            this.processingOrderId = row.processingOrderId;
            var processingOrderId = row.processingOrderId;
            queryRaw(processingOrderId).then((response) => {
                this.rawTableList = response.data;
            });
            queryFinish(processingOrderId).then((response) => {
                this.finishTableList = response.data;
            });
        },
        /** 新增按钮操作 */
        handleAdd() {
            this.reset();
            this.open = true;
            this.title = "新增-加工单";
        },
        /** 修改按钮操作 */
        handleCancle(row) {
            if (this.processingOrderId == null) {
                this.$message({
                    message: '没有选中单据，无法进行撤销',
                    type: 'warning'
                });
                return;
            }
            var id = this.processingOrderId;
            getOrder(id).then((response) => {

                this.$modal
                    .confirm(
                        '确定撤销加工单吗?'
                    )
                    .then(function () {
                        return cancleOrder(id);
                    })
                    .then((response) => {
                        this.getList();
                        this.$modal.msgSuccess("删除成功");
                    })
                    .catch(() => { });
            });

        },
        /** 提交按钮 */
        submitForm() {
            this.$refs["form"].validate(valid => {
                if (valid) {
                    this.form.storehouseCode = this.getHouseCode();
                    confrimOrder(this.form).then((response) => {
                        this.$modal.msgSuccess("保存成功");
                        this.open = false;
                        this.queryList();
                    });
                }
            });
        },
        getHouseCode() {
            return this.$route.query.storehouseCode;
        },
        raw_AddClick() {
            var item = {
                storehouseCode: null,
                crossRegion: null,
                crossRegionlist: [],
                stackingPosition: null,
                stackingPositionList: [],
                mateCode: null,
                stockLot: null,
                weight: 0
            };
            item.storehouseCode = this.getHouseCode();

            getCrossRegion(this.getHouseCode()).then((response) => {
                item.crossRegionlist = response.data;
                if (item.crossRegionlist.length != 0) {
                    item.crossRegion = item.crossRegionlist[0];

                    getPostion(this.getHouseCode(), item.crossRegion).then((response2) => {
                        item.stackingPositionList = response2.data;
                        if (item.stackingPositionList.length != 0) {
                            item.stackingPosition = item.stackingPositionList[0];
                        }
                        this.form.rawList.push(item);
                    });
                } else {
                    this.form.rawList.push(item);
                }
            });

        },
        raw_change(e, row) {
            row.stackingPositionList = [];
            getPostion(this.getHouseCode(), e).then((response2) => {
                row.stackingPositionList = response2.data;
                if (row.stackingPositionList.length != 0) {
                    row.stackingPosition = row.stackingPositionList[0];
                }
            });
        },
        raw_celar() {
            this.form.rawList = [];
        },
        finish_AddClick() {
            var item = {
                storehouseCode: null,
                crossRegion: null,
                crossRegionlist: [],
                stackingPosition: null,
                stackingPositionList: [],
                mateCode: null,
                stockLot: null,
                weight: 0
            };
            item.storehouseCode = this.getHouseCode();

            getCrossRegion(this.getHouseCode()).then((response) => {
                item.crossRegionlist = response.data;
                if (item.crossRegionlist.length != 0) {
                    item.crossRegion = item.crossRegionlist[0];

                    getPostion(this.getHouseCode(), item.crossRegion).then((response2) => {
                        item.stackingPositionList = response2.data;
                        if (item.stackingPositionList.length != 0) {
                            item.stackingPosition = item.stackingPositionList[0];
                        }
                        this.form.finishList.push(item);
                    });
                } else {
                    this.form.finishList.push(item);
                }
            });

        },
        finish_change(e, row) {
            row.stackingPositionList = [];
            getPostion(this.getHouseCode(), e).then((response2) => {
                row.stackingPositionList = response2.data;
                if (row.stackingPositionList.length != 0) {
                    row.stackingPosition = row.stackingPositionList[0];
                }

            });
        },
        finish_celar() {
            this.form.finishList = [];
        },
    }
};
</script>
