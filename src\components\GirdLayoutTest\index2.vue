<template>
  <div class="grid_box">
    <div class="grid_title" :style="{ 'writing-mode': mainTitle == '混匀' ? 'vertical-rl' : 'normal'  }">{{ mainTitle }}</div>
    <div class="grid_left" ref="container">
      <vdr v-for="(item, index) in layoutData" :key="item.i" :w="item.w" :h="80" :x="getRightBasedX(item)" :y="item.y" :parent="true" :debug="false"
            :style="{'border-color': item.STOCK_WEIGHT ? (item.STOCK_WEIGHT == 0 ? '#40e4ff' : (item.STOCK_WEIGHT > 0 ?  '#1effc6' : (item.STOCK_WEIGHT < 0 ? '#E75A63' : '#40e4ff'))) : '#40e4ff' }"
           :snap="true" :snapTolerance="2" :grid="[1,1]" :isConflictCheck="false" :prevent-deactivation="false" :resizable="true" :min-width="0"
           :min-height="50" :scale-ratio="0.8" @resizestop="(left, top, width, height) => onResize(item, left, width)" class-name-active="active-class"
           :draggable="false" :active="false" :handles="['ml','mr']" axis="x" @contextmenu.native.prevent="onRightClick(index, $event,item)"
           @mouseenter.native="showInfo(item)" @mouseleave.native="hideInfo">
        <div class="content">{{ item.stack_name }}</div>
        <div class="info">
          x: {{ item.x }} | w: {{ item.w }}
        </div>
      </vdr>
    </div>
    <div class="grid_right" :style="{ width: rightPartVisible ? '320px' : '0', padding: rightPartVisible ? '0 10px 0 10px' : '0' }">
      <div class="data_list scrollbar_box">
        <div class="list_item" v-for="item in layoutData" :key="item.i">
          <div class="item_title" v-html="item.stack_name"></div>
          <div class="item_Num"><el-input-number v-model="item.x" size="mini" controls-position="right" /> - </div>
          <div class="item_Num"><el-input-number v-model="item.end_position" size="mini" controls-position="right" /></div>
          <div class="item_Num"><el-input v-model="item.STOCK_WEIGHT" size="mini" style="width: 35px;" />T</div>
        </div>
      </div>
    </div>
    <div v-show="contextMenu.visible" class="context-menu" :style="{
        left: contextMenu.x + 'px',
        top: contextMenu.y + 'px'
      }">
      <div class="menu-item" @click="handleAction('入库')">入库</div>
      <div class="menu-item" @click="handleAction('出库')">出库</div>
      <div class="menu-item" @click="handleAction('盘点数据')">盘点数据</div>
      <div class="menu-item">倒垛</div>
    </div>
    <StockOut :title="titleData" :open="isTrue" :mateName="mateName" @on-cancel="handlelDialogCancel" @on-confirm="handlelDialogConfirm"></StockOut>
  </div>
</template>
<script>
import vdr from 'vue-draggable-resizable-gorkys'
import 'vue-draggable-resizable-gorkys/dist/VueDraggableResizable.css'
import StockOut from '@/components/GirdLayoutTest/StockOut';

export default {
  name: 'GirdLayoutTest',
  props: {
    layoutData: {
      type: Array,
      default: '',
    },
    mainTitle: {
      type: String,
      default: "",
    },
    rightPartVisible: {
      type: Boolean,
      default: ''
    }
  },
  components: {
    vdr,
    StockOut
  },
  directives: {},
  data() {
    return {
      contextMenu: {
        visible: false,
        x: 0,
        y: 0
      },
      hoverItem: null,
      titleData: '',
      isTrue: false,
      mateName: '',
      containerWidthPlan: null,
    }
  },
  computed: {
  },
  mounted() {
    this.updateContainerWidth()
    window.addEventListener('resize', this.updateContainerWidth)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateContainerWidth)
    document.removeEventListener('click', this.closeContextMenu)
  },
  methods: {
    showInfo(item) {
      this.hoverItem = item;
    },
    hideInfo() {
      this.hoverItem = null;
    },
    onResize(item, left, width) {
      item.w = width;
      item.x = this.containerWidthPlan - left - width;
      item.end_position = item.x + item.w;
      console.log('调整大小:', item.x, item.w, item.end_position)
    },
    updateContainerWidth() {
      this.containerWidthPlan = this.$refs.container.clientWidth
      console.log('容器宽度:', this.containerWidthPlan);
      
    },
    // 将右上角坐标系转换为组件需要的左侧坐标系
    getRightBasedX(item) {
      return this.containerWidthPlan - item.x - item.w
    },
    onRightClick(index, event, item) {
      //console.log(index, event, item);
      this.mateName = item.stack_name;
      // 计算菜单位置
      const { clientX, clientY } = event
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      const menuWidth = 160
      const menuHeight = 120
      this.contextMenu = {
        visible: true,
        x: clientX + menuWidth > viewportWidth ? viewportWidth - menuWidth : clientX,
        y: clientY + menuHeight > viewportHeight ? viewportHeight - menuHeight : clientY
      }
      // 绑定全局点击关闭
      document.addEventListener('click', this.closeContextMenu)
    },
    closeContextMenu() {
      this.contextMenu.visible = false
      document.removeEventListener('click', this.closeContextMenu)
    },
    handleAction(action) {
      this.$emit('contextmenu-action', {
        action,
        data: this.layoutData
      })
      this.titleData = action;
      this.isTrue = true;
      this.closeContextMenu()
    },
    handlelDialogConfirm() {
      this.handlelDialogCancel();
    },
    handlelDialogCancel() {
      this.isTrue = false;
    },
  },
}
</script>
<style>
.vdr {
  font-size: 16px;
  border: 2px dashed #40e4ff;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 2px #40e4ff;
}
.vdr:hover,
.vdr.active {
  color: #fff;
  background: rgba(66, 135, 254, 0.8);
}
.handle {
  border: 1px solid #40e4ff;
  box-shadow: 0 0 2px #40e4ff;
}

.item-content {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  font-size: 14px;
}
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}
</style>
<style lang="scss">
.grid_box {
  font-family: Arial, Helvetica, sans-serif;
  display: flex;
  height: 98px;
  position: relative;
  margin: 68px 0 0 0;
  .grid_title {
    height: 100%;
    text-align: center;
    font-size: 22px;
    font-weight: bolder;
    position: absolute;
    letter-spacing: 5px;
    color: #fff;
    text-shadow: 0 0 5px #80add6, /* 外发光效果 */ 0 0 10px #80add6,
      0 0 15px #80add6, 0 0 20px #80add6;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 0 0 15px;
  }
  .grid_left {
    flex: 1;
    margin: 0 4vw 0 2vw;
    padding: 10px 0;
    justify-content: space-evenly;
    display: flex;
    flex-direction: column;
    .coordinate_axis {
      display: flex;
      .axis_item {
        flex: 1;
        position: relative;
        height: 8px;
        border-top: 2px solid #ccc;
        border-right: 2px solid #ccc;
        &:first-child {
          border-left: 2px solid #ccc;
          &::before {
            content: "0";
            position: absolute;
            display: inline-block;
            top: 0;
            left: 0;
            transform: translate(-50%, -100%);
            color: #666;
          }
        }
        span {
          position: absolute;
          display: inline-block;
          top: 0;
          right: 0;
          transform: translate(50%, -100%);
          color: #666;
        }
      }
    }
    .vue-grid-layout {
      .vue-grid-item {
        touch-action: pan-x; /* 只允许横向触摸拖拽 */
        user-select: none;
        border: 2px dashed #40e4ff;
        background: rgba(255, 255, 255, 0.8);
        /* background: url("~@/assets/images/imageKQT/singleBG.png") center/100%
          100% no-repeat; */
        &:not(.vue-grid-placeholder) {
          /* border: none; */
          /* border-radius: 6px; */
        }
        .text {
          font-size: 14px;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;
          flex-wrap: nowrap;
          align-content: center;
          justify-content: center;
          align-items: center;
          color: #000;
          z-index: 2;
          .numMark {
            color: #33abf2;
            > span {
              color: rgb(36, 36, 36);
              font-size: 13px;
            }
          }
        }
        .item-content {
          width: 100%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          position: relative;
          padding: 10px;
          box-sizing: border-box;
          .item-tooltip {
            position: absolute;
            bottom: calc(100% + 0px);
            left: 50%;
            transform: translateX(-22%);
            background: url("~@/assets/images/imageKQT/messageR.png") center /
              100% 100% no-repeat;
            color: white;
            padding: 6px 12px 22px 12px;
            font-size: 12px;
            white-space: nowrap;
            z-index: 20;
          }
        }
        :hover,
        &.active {
          color: #fff;
          background: rgba(66, 135, 254, 0.8);
          /*  background: url("~@/assets/images/imageKQT/singleBgClick.png")
            center/100% 100% no-repeat; */
          border: none;
          .numMark {
            color: #fff;
            > span {
              color: #fff;
            }
          }
        }
        .vue-resizable-handle {
          left: 0;
          background: transparent;
          transition: all 0.3s ease;
          z-index: 3;
          cursor: w-resize;
          &::after {
            content: "";
            position: absolute;
            bottom: 35px;
            left: 2px;
            width: 10px;
            height: 10px;
            background: linear-gradient(
              to left,
              transparent 0%,
              transparent calc(35% - 1px),
              #72eaff 35%,
              transparent calc(35% + 1px),
              transparent calc(70% - 1px),
              #72eaff 70%,
              transparent calc(70% + 1px),
              transparent 100%
            );
          }
        }
      }
    }
  }
  .grid_right {
    height: 150px;
    flex-shrink: 0;
    flex-grow: 0;
    padding: 0 10px 0;
    .data_list {
      padding: 0 10px 0 10px;
      margin: -20px 0 0 0;
      height: calc(100% - 0px);
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
      flex-wrap: nowrap;
      align-items: stretch;
      justify-content: flex-start;
      &::-webkit-scrollbar {
        display: none;
      }
      .list_item {
        color: #000;
        font-size: 14px;
        display: flex;
        justify-content: flex-end;
        padding: 5px 0;
        border-bottom: 1px dashed rgb(0, 0, 0);
        > div {
          display: flex;
          align-items: center;
          &.item_title {
            white-space: nowrap;
            flex: 1;
          }
          .el-input {
            .el-input__inner {
              color: black;
              font-size: 11px;
              background: #dce9fa;
              border: 1.5px solid #d0d7db;
              border-radius: 0;
            }
          }
          .el-input-number {
            margin: 0 2px;
            width: 40px;
            > span {
              width: 8px;
              i {
                font-size: 8px;
              }
            }
            .el-input {
              input {
                padding: 0 10px 0 2px;
                color: #4d85ca;
                background: #ffffff;
              }
            }
          }
        }
      }
    }
  }
  .context-menu {
    position: fixed;
    background: url("~@/assets/images/imageKQT/messageL.png") 58% 100%/128% 100%
      no-repeat;
    z-index: 9999;
    padding: 10px 0 6px 0;
    font-family: Arial, Helvetica, sans-serif;
    .menu-item {
      margin: 3px 3px 0px 16px;
      font-size: 14px;
      padding: 2px 16px 2px 2px;
      color: #1c63af;
      cursor: pointer;
      transition: background-color 0.3s;
      background-color: #ddeafd;

      &:hover {
        background-color: #488ff7;
        color: #fff;
      }
    }
  }

  * {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
  }
}
</style>