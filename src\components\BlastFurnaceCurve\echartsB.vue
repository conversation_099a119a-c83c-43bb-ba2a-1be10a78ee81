<template>
  <div class="echartsLB_box">
    <div class="title-bar">
      <div class="title-group">
        <div class="title-text" :class="{active: active === '利用系数'}" @click="chagngePrecent('利用系数')">利用系数</div>
        <div class="title-text" :class="{active: active === '热风温度'}" @click="chagngePrecent('热风温度')">热风温度</div>
        <div class="title-text" :class="{active: active === '炉腹煤气量'}" @click="chagngePrecent('炉腹煤气量')">炉腹煤气量</div>
        <div class="title-text" :class="{active: active === '休风率'}" @click="chagngePrecent('休风率')">休风率</div>
      </div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉'}" @click="chagngeGl('一高炉')">一高炉</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉'}" @click="chagngeGl('二高炉')">二高炉</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsL" class="echartsL" v-if="active === '利用系数'|| active === '炉腹煤气量' || active === '休风率'"></div>
      <div ref="echartsB" class="echartsB" v-if="active === '热风温度'"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenStatistics } from "@/api/analyse/blastfurnace";

export default {
  name: 'EChartsB',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      active: '利用系数',
      activeGL: '一高炉',
      dataNow: 212,
      chart: null,
      chart1: null,
      param: {
        prodCenterCode: '1',
        type: 'divide'
      }
    }
  },
  activated() {
    this.initValue();
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
        if (this.chart1) {
          this.chart1.resize();
        }
      }, 300);
    }
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    if (this.chart1) {
      this.chart1.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    initValue() {
      ironScreenStatistics(this.param).then(res => {
        if (res.code === 200) {
          var data = res.data;
          const result = Object.keys(data).map(key => ({
            date: key,
            value: data[key]
          }));
          var xData = [];
          var yData = [];
          result.forEach((item, index) => {
            xData[index] = item.date || 0;
            yData[index] = item.value || '0';
          });
          this.$nextTick(() => {
            if (this.active == '热风温度') {
              this.initChart1(xData, yData);
              window.addEventListener('resize', this.resizeChart);
            } else {
              this.initChart(xData, yData);
              window.addEventListener('resize', this.resizeChart);
            }
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
        var xData = [];
        var yData = [];
        this.$nextTick(() => {
          if (this.active == '热风温度') {
            this.initChart1(xData, yData);
            window.addEventListener('resize', this.resizeChart);
          } else {
            this.initChart(xData, yData);
            window.addEventListener('resize', this.resizeChart);
          }
        })
      });
    },
    initChart(xAxis, yAxis) {
      /** 利用系数、 炉腹煤气量、休风率*/
      let that = this;
      const chartDom = this.$refs.echartsL;
      this.chart = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: xAxis.length > 0 && yAxis.length > 0
          }]
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getFullYear()} - ${date.getMonth() + 1}-${date.getDate()}</div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        grid: {
          top: '8%',
          left: '1.5%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#DCDDDE',
              width: 2
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#3E4B5C',
              fontSize: 10
            },
            formatter: function (value) {
              return value.split('-').slice(1).join('-');
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          data: xAxis
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#979A9F',
              fontSize: 10
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F3F3F4'
            }
          },
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: 15,
            itemStyle: {
              color: '#61B8FF',
              barBorderRadius: 50,
            },
            data: yAxis
          },
        ]
      };
      this.chart.setOption(option);
    },
    initChart1(xAxis, yAxis) {
      /** 热风温度 */
      let that = this;
      const chartDom1 = this.$refs.echartsB;
      this.chart1 = echarts.init(chartDom1);
      const option1 = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: xAxis.length > 0 && yAxis.length > 0
          }]
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getFullYear()} - ${date.getMonth() + 1}-${date.getDate()}</div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value}</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        grid: {
          top: '8%',
          left: '1.5%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#DCDDDE',
              width: 2
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#3E4B5C',
              fontSize: 10
            },
            formatter: function (value) {
              return value.split('-').slice(1).join('-');
            },
          },
          axisTick: { show: false },
          splitLine: { show: false },
          data: xAxis
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#979A9F',
              fontSize: 10
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F3F3F4'
            }
          },
        },
        series: [
          {
            name: '',
            type: 'line',
            smooth: true,
            showSymbol: false,
            triggerEvent: true,
            itemStyle: {
              color: 'rgba(149, 224, 255, 1)'
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(
                0, 1, 0, 0,
                [
                  {
                    offset: 0,
                    color: 'rgba(255,255,255,0.43)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(149, 224, 255, 0.3)'
                  }
                ]
              )
            },
            emphasis: {
              focus: 'series'
            },
            data: yAxis
          }
        ]
      };
      this.chart1.setOption(option1);
    },
    resizeChart() {
      if (this.chart1) {
        this.chart1.resize();
      }
      if (this.chart) {
        this.chart.resize();
      }
    },
    chagngePrecent(name) {
      if (name === '利用系数') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart1) {
            this.chart1.dispose();
          }
          this.param.type = 'divide';
          this.initValue();
        })
      }
      if (name === '热风温度') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart) {
            this.chart.dispose();
          }
          this.param.type = 'temperature';
          this.initValue();
        })
      }
      if (name === '炉腹煤气量') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart1) {
            this.chart1.dispose();
          }
          this.param.type = 'gas';
          this.initValue();
        })
      }
      if (name === '休风率') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart1) {
            this.chart1.dispose();
          }
          this.param.type = 'rest';
          this.initValue();
        })
      }
    },
    chagngeGl(name) {
      if (name == '一高炉') {
        this.activeGL = '一高炉';
        this.param.prodCenterCode = '1';
        this.initValue();
      }
      if (name == '二高炉') {
        this.activeGL = '二高炉';
        this.param.prodCenterCode = '2';
        this.initValue();
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsLB_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px 0 0;
  justify-content: space-between;
}
.title-group {
  width: 400px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: center;
  align-items: center;
}
.title-text {
  width: 100px;
  text-align: center;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
  cursor: pointer;
}
.title-text:hover,
.title-text.active {
  color: #3c83ff;
  text-decoration: underline solid #3c83ff;
  text-underline-offset: 5px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 5px 0 0 0;
}
.echartsL,
.echartsB {
  width: 100%;
  height: 100%;
}
</style>