<template>
  <div class="app-container">
    <el-form :model="selectParam" ref="queryForm" size="small" :inline="true" label-width="68px" class="elForm">
      <el-form-item label="时间" prop="componentTime">
        <el-date-picker v-model="componentTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="班组" prop="prodCode">
        <el-select v-model="selectParam.teamGroup" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.common_workGroup" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="班别" prop="status">
        <el-select v-model="selectParam.teamClass" placeholder="请选择">
          <el-option
            v-for="dict in dict.type.common_workClass" :key="dict.value" :label="dict.label" :value="dict.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="10" class="elRow">
      <el-col :span="1.5">
        <el-button icon="el-icon-plus" plain size="mini" type="primary" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-edit" plain size="mini" type="success" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-delete" plain size="mini" type="danger" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="el-icon-download" plain size="mini" type="warning" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>
  </div>

</template>

<script>
    import dayjs from "dayjs";

    export default {
      name: "selectComponent",
      props: {
        parentMessage: {
          type: String, // 指定prop的类型
          required: true // 指定prop是否必须
        }
      },
      dicts: [
        'common_workGroup',
        'common_workClass',
      ],
      // inject: ['refreshChild'], // 注入父组件提供的方法
      inject: {
        refreshChild: { value: "refreshChild", default: null },
      },
      data(){
        return{
          // 日期
          componentTimeArr:[],
          selectParam:{
            teamGroup:'',
            teamClass:'',
            componentTimeStart:'',
            componentTimeEnd:'',
          },
          // 传递父附件数据
          parentTableData:[

            { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc',title:'Name',colum:'name' },
            { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' ,title:'Sex',colum:'sex'},
            { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai',title:'Age',colum:'age' },
            { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' ,title:'Address',colum:'address'},

          ],
          parentColumeData:[
            {title:'Name',colum:'name'},
            {title:'Sex',colum:'sex'},
            {title:'Age',colum:'age'},
            {title:'Address',colum:'address'},
            {title:'role',colum:'role'},
          ],
        }
      },
      created() {
        this.componentTimeArr.push(dayjs(new Date()).add(-1, "day"));
        this.componentTimeArr.push(dayjs(new Date()).add(1, "day"));

      },
      methods:{
        fetchData() {
          // 执行数据获取的逻辑，例如调用API等
          console.log('Fetching data in child component');
          // 示例API调用：axios.get('/api/data').then(response => {...})
        },

        /* 获取路径信息 */
        getEleName(){
          return this.$route.query.eleName;
        },

        queryChildList(){
          this.$emit('update-message', this.parentTableData,this.parentColumeData);
        },
          /* 搜索  */
        handleQuery(){
          if (this.componentTimeArr.length == 2) {
            this.selectParam.componentTimeStart = dayjs(this.componentTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.componentTimeEnd = dayjs(this.componentTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
          console.log("this.selectParam:",JSON.stringify(this.selectParam))

          // 向父组件传递数据
          this.$emit('update-message', this.parentTableData,this.parentColumeData);

        },

          /* 添加 */
        handleAdd(){
          console.log("handleAdd:"+this.getEleName())

        },
        /* 修改 */
        handleUpdate(){
          console.log("handleUpdate:"+this.getEleName())
        },
        /* 删除 */
        handleDelete(){
          console.log("handleDelete:"+this.getEleName())
        },
        /* 导出 */
        handleExport(){
          console.log("handleExport:"+this.getEleName())
        },
        /* 重置  */
        resetQuery(){
          this.selectParam=[]
        },
      },
      mounted() {
        this.fetchData(); // 组件挂载时自动获取数据
      }
    }
</script>

<style scoped>
  .elForm{
    margin-top: -20px;
    margin-left: -43px;
  }
  .elRow{
    margin-left: -20px;
    margin-top: -6px;
  }

</style>
