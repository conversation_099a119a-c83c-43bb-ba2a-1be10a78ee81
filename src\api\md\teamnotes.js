import request from '@/utils/request'

// 查询班组记事列表
export function listNotes(query) {
  return request({
    url: '/api/md/teamnotes/list',
    method: 'get',
    params: query
  })
}

// 查询班组记事详细
export function getNotes(notesId) {
  return request({
    url: '/api/md/teamnotes/' + notesId,
    method: 'get'
  })
}
// 新增班组记事
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/teamnotes/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除班组记事
export function delNotes(notesId) {
  return request({
    url: '/api/md/teamnotes/' + notesId,
    method: 'delete'
  })
}


// 查询班制
export function getWorkMode() {
  return request({
    url: "/api/md/teamnotes/getWorkMode",
    method: "get",
  });
}

// 查询日志角色
export function getWorkNoteRole(prodCode) {
  return request({
    url: "/formtemp/util/worknote/role/"+prodCode,
    method: "get",
  });
}