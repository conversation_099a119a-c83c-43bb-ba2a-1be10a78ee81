<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <div>
        <keep-alive>
          <router-view :key="key" />
        </keep-alive>
        <mineIframe v-for="(item, index) in cacheIframeList" :key="index" :url="item.url"  v-show="$route.name==item.name"></mineIframe>
      </div>
    </transition>
  </section>
</template>

<script>

export default {
  name: 'AppMain',
  components: {
    mineIframe: () => import('@/views/report/autoshow/index')
  },
  data() {
    return {
      iframeList: [],
      cacheIframeList: [],// 已缓存的iframe路由列表
    }
  },
  mounted() {
    const array = this.$store.state.permission.iframe
    this.iframeList = array.filter((item, index, self) =>
      index === self.findIndex(t => { return t.name === item.name })
    )
    this.iframeList.forEach(item => {
      item.url = JSON.parse(item.query).url
    });
    // 添加路由守卫，判断已打开过的iframe进行缓存
    this.$router.beforeEach((to, from, next) => {
      if (this.iframeList.findIndex(t=> t.name===to.name) !==-1) {
        if (this.cacheIframeList.findIndex(i=> i.name === to.name) ===-1) {
          this.cacheIframeList.push(this.iframeList[this.iframeList.findIndex(t=> t.name===to.name)])
          next()
        } else {
          next()
        }
      } else {
        next()
      }
    })
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    }
  },
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.fixed-header+.app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: calc(100vh - 84px);
  }

  .fixed-header+.app-main {
    padding-top: 84px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
