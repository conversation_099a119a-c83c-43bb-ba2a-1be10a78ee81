import request from '@/utils/request'

/**铁水产量数据总览 */
export function ironScreenDay(query) {
  return request({
    url: '/public/ironScreen/day',
    method: 'get',
    params: query
  });
}
export function ironScreenMonth(query) {
  return request({
    url: '/public/ironScreen/month',
    method: 'get',
    params: query
  });
}
/** 铁水温度 */
export function ironScreenTemperature(query) {
  return request({
    url: '/public/ironScreen/temperature',
    method: 'get',
    params: query
  });
}
/** 利用系数、热风温度... */
export function ironScreenStatistics(query) {
  return request({
    url: '/public/ironScreen/Statistics',
    method: 'get',
    params: query
  });
}
/** 原料消耗 */
export function ironScreenMaterial(query) {
  return request({
    url: '/public/ironScreen/material',
    method: 'get',
    params: query
  });
}
/** 铁水成分 */
export function ironScreenComponent(query) {
  return request({
    url: '/public/ironScreen/Component',
    method: 'get',
    params: query
  });
}
/** 燃料比/焦比/煤比 */
export function ironScreenRatio(query) {
  return request({
    url: '/public/ironScreen/ratio',
    method: 'get',
    params: query
  });
}