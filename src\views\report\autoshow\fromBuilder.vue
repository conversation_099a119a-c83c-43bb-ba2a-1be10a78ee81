<template>
	<div class="app-container" v-loading="loading">
		<v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vfr">
		</v-form-render>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" @click="submitForm">保存</el-button>
			<el-button type="primary" @click="testfuncfrm" v-show="false">测试按钮</el-button>
		</div>
		<div>{{ testvalue }}</div>
	</div>
</template>
<script>
import { getformjson, getformdata, saveformdata, querynoterole } from '@/api/formtemplate/details';
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
	props: ["propPageKey","propDefFormData","propFilterParam"],
	data() {
		return {
			formJson: {},
			formData: {},
			optionData: {},
			pageKey: '',
			filterParam: {},
			testvalue: '',
			saveflag: false,
			loading: false,
			defFormData: {},
		}
	},
	created() {
		console.log("propFilterParam",this.propFilterParam);
		console.log("propPageKey",this.propPageKey);
		console.log("propDefFormData",this.propDefFormData);
		// if(this.$parent.$parent.pageKey!=null){
		// 	this.pageKey = this.$parent.$parent.pageKey;
		// }
		// console.log("++++formbuilder created++++", this.builderParentData);
		// if(this.propPageKey!=null){
		// 	this.pageKey = this.propPageKey;
		// }
		this.filterParam = this.propFilterParam;
		this.pageKey = this.propPageKey;
		this.propDefFormData = this.propDefFormData;

		// console.log("获取父窗体pageKey=" + this.$parent.$parent.pageKey);
		this.renderForm();
	},
	mounted() {
		this.loadData();
	},
	methods: {
		submitForm() {
			this.loading = true;
			this.$refs.vfr.getFormData().then(formData => {
				//校验成功
				// this.$message.success(JSON.stringify(formData));
				//保存数据
				saveformdata(this.pageKey, this.filterParam, formData).then(response => {
					if (response == undefined) {
						this.loading = false;
						this.$message.error("修改失败");
					}
					if (response.code == 200) {
						this.$message.success("修改成功");
						this.saveflag = true;
					} else {
						this.$message.error("修改失败");
					}
					// console.log(response.code);
					this.loading = false;
				}).catch(error => {
					this.loading = false;
				});
			}).catch(error => {
				// 校验失败
				this.$message.error(error);
				this.loading = false;
			})
		},
		clearFormData() {
			// console.log("清除builder");
			// this.$nextTick(()=>{
			// 	this.$refs.vfr.resetForm();
			// })
		},
		renderForm() {
			if (this.pageKey.length === 0) {
				// console.log('pagekey是空');
				return;
			}
			getformjson(this.pageKey).then((response) => {
				this.formjson = response;
				this.clearFormData();
				this.$refs.vfr.setFormJson(this.formjson);
				this.$nextTick(() => {
					// 加载缺省数据
					Object.keys(this.defFormData).forEach(k => {
						this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
					});
				});
			});
		},
		loadData() {
			this.loading = true;
			this.clearFormData();
			this.$forceUpdate();
			this.$nextTick(() => {
				// console.log('清空前:',this.formData);
				// this.formData = {};
				// console.log('清空后:',this.formData);

				this.clearFormData();
				this.$forceUpdate();

				if (JSON.stringify(this.filterParam) == "{}" || this.pageKey.length === 0) {
					// console.log("没有加载数据");
					this.loading = false;
					return;
				}
				
				// 加载数据
				// console.log('开始加载数据...');
				getformdata(this.filterParam, this.pageKey).then((response) => {
					// console.log(response);
					if (response == undefined) {
						this.loading = false;
						this.$message.error("修改失败");
						return;
					}
					this.formData = response;
					// console.log('formData为:',this.formData);
					this.$refs.vfr.setFormData(this.formData);
					// console.log('结束加载数据...');
					this.$forceUpdate();
					this.loading = false;
				})
			});
		},
		testfuncfrm() {
			// alert('这是formbuilder.vue的testfunc');
			// console.log("执行testfunc之前");
			window.testfunc();
			// console.log("执行testfunc以后");
		},
		hhh() {
			alert('hhh');
		},

	}
}
</script>
