<template>
  <div class="mes_new_table">
    <vxe-toolbar ref="toolbarRef" export custom></vxe-toolbar>
    <vxe-table
      border
      :header-cell-style="headerCellStyle"
      show-overflow
      :cell-config="{height: 35}"
      :loading="loading"
      :export-config="{}"
      :edit-config="{trigger: 'click', mode: 'cell', showStatus: true}"
      @edit-closed="editClosedEvent"
      ref="tableRef"
      :height="dltableHeight"
      @cell-click="cellClickEvent"
      :data="tableData"
      >
      <vxe-column type="seq" width="70"></vxe-column>
      <vxe-column field="role" title="role"></vxe-column>
      <vxe-column field="name" title="name" :edit-render="{autofocus: true}">
        <template #edit="scope">
          <vxe-input type="text" v-model="scope.row.name" @input="updateRowStatus(scope)"></vxe-input>
        </template>
      </vxe-column>
      <vxe-column field="date" title="date"></vxe-column>
      <vxe-column field="age" title="年龄" ></vxe-column>
      <vxe-column field="C" title="碳"></vxe-column>
    </vxe-table>
    <vxe-pager
      :current-page.sync="pageVO.currentPage"
      :page-size.sync="pageVO.pageSize"
      :total="pageVO.total"
      :layouts="['Home', 'PrevJump', 'PrevPage', 'Number', 'NextPage', 'NextJump', 'End', 'Sizes', 'FullJump', 'Total']"
      @page-change="pageChange">
    </vxe-pager>
<!--    <BarChart :BarChartData="BarChartData" />-->
    <lineChart :LineChartData="LineChartData" class="linechartClass" />
  </div>
</template>

<script>
  import { VxeUI } from 'vxe-table'
  import BarChart from './BarChart';
  import lineChart from './lineChart';
  import { echartsComm } from '@/api/vuexdemo/echartDataTemplate.js';
  export default {
    components: {
      BarChart,
      lineChart
      },
    data () {
      return {
        headerCellStyle:null,
        pageCharConfig:null,
        dltableHeight:400,
        yDataValue:[], //  y轴坐标
        AllList : [
           {"name": "智利精粉",  "role": "智利精粉1010100045", "date":"2025-01", "age":33,"C":22},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-02", "age":32,"C":76},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-03", "age":34,"C":67},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-04", "age":45,"C":56},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-05", "age":65,"C":34},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-06", "age":67,"C":45},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-07", "age":43,"C":23},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-08", "age":21,"C":25},

           {"name": "智利精粉",  "role": "智利精粉1010100045", "date":"2025-01", "age":33,"C":22},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-02", "age":32,"C":76},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-03", "age":34,"C":67},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-04", "age":45,"C":56},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-05", "age":65,"C":34},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-06", "age":67,"C":45},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-07", "age":43,"C":23},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-08", "age":21,"C":25},

           {"name": "智利精粉",  "role": "智利精粉1010100045", "date":"2025-01", "age":33,"C":22},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-02", "age":32,"C":76},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-03", "age":34,"C":67},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-04", "age":45,"C":56},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-05", "age":65,"C":34},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-06", "age":67,"C":45},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-07", "age":43,"C":23},
           {"name": "智利精粉6", "role": "智利精粉1010100046", "date":"2025-08", "age":21,"C":25},
        ],
        tableData:[],
        updateRecords:[],
        pageVO:{
          total: 0,
          currentPage: 1,
          pageSize: 10
        },

        BarChartData: {
          xData: ['2025-01','2025-02','2025-03','2025-04'], // X轴数据
          yData:  [56,55,57,87] // Y轴数据（柱形高度）
        },
        LineChartData: {
          xData:  ['周一', '周二', '周三', '周四', '周五', '周六', '周日'], // X轴数据
          yData:   [120, 132, 101, 134, 90, 230, 210] ,// Y轴数据（曲线形高度）
          // yData1:  [223, 332, 431, 535, 690,730, 810] // Y轴数据（曲线形高度）
        },


      }

    },
    created() {
      this.handlePageData(); //  前端分页
      this.LineChartData.description = this.getFileName();  // 曲线元素名称
      let config = null;
      // let barConfig = echartsComm().filter(x=>x.businessCode==this.getBarFileUrl());
      let lineConfig = echartsComm().filter(x=>x.businessCode==this.getLineFileUrl());
      // if(barConfig != null || lineConfig.length > 0){
      //   config = barConfig;
      // }
      if(lineConfig != null || lineConfig.length > 0){
        config = lineConfig;
      }
      console.log("config:",JSON.stringify(config))
      if(config==null || config.length==0){
      //  提示
      }
      this.pageCharConfig=config[0];

      // 显示曲线得列填充颜色
      if(this.pageCharConfig.Y_line_axis.length>0){
        let tableFieldArr=[];
        for(let i=0;i<this.pageCharConfig.Y_line_axis.length;i++){
          tableFieldArr.push( this.pageCharConfig.Y_line_axis[i].tableField)
          this.headerCellStyle = ({ column }) => {
            if (tableFieldArr.includes(column.field ) ) {
              return {
                backgroundColor: '#f60',
                color: '#ffffff'
              }
            }
          }
        }
      }

    },
    // mounted() {
    //   this.$nextTick(() => {
    //     /**mes_new_table 到顶部的高炉 */
    //     let topValue = document.getElementsByClassName('mes_new_table')[0].getBoundingClientRect().top;
    //     this.dltableHeight = document.body.clientHeight - topValue - 50;
    //   })
    // },

    // 表格导出功能
    mounted() {
      const $table = this.$refs.tableRef
      const $toolbar = this.$refs.toolbarRef
      if ($table && $toolbar) {
        $table.connect($toolbar)
      }
    },
    methods:{
      // 前端分页
      handlePageData () {
        this.loading = true
        setTimeout(() => {
          const { pageSize, currentPage } = this.pageVO
          this.pageVO.total = this.AllList.length
          this.tableData = this.AllList.slice((currentPage - 1) * pageSize, currentPage * pageSize)
          this.loading = false
        }, 100)
      },
      pageChange ({ pageSize, currentPage }) {
        this.pageVO.currentPage = currentPage
        this.pageVO.pageSize = pageSize
        this.handlePageData()
      },
      /** 柱形图路径参数 */
      getBarFileUrl() {
        return this.$route.query.barFileUrl;
      },
      /** 曲线图路径参数 */
      getLineFileUrl() {
        return this.$route.query.lineFileUrl;
      },
      /** 图形名称 */
      getFileName() {
        return this.$route.query.FileName;
      },
      // 单机获取列数据
      cellClickEvent ({ row, column }) {
        // console.log(`单击行：${JSON.stringify(row)} 单击列：${JSON.stringify(column)}`)
        console.log(`单击行：${row.id} 单击列：${column.field}`)
        const tableFild=column.field;
        let yitem = null;
        if(this.pageCharConfig.echarType == "柱形图"){
          yitem = this.pageCharConfig.Y_bar_axis.find(x=>x.tableField==column.field);
        }
        if(this.pageCharConfig.echarType == "曲线图"){
          yitem = this.pageCharConfig.Y_line_axis.find(x=>x.tableField==column.field);
          this.LineChartData.description = this.pageCharConfig.description; // 曲线元素名称
          this.LineChartData.seriesName1 = yitem.name
          this.LineChartData.legendName = yitem.name
        }

        if(yitem==null || yitem.length==0){
          return;
        }
        var xData=[];
        var yData=[];
        for (let i = 0; i < this.tableData.length ; i++) {
          const  item = this.tableData[i];
          yData.push(item[yitem.tableField])
          if(this.pageCharConfig.echarType == "柱形图"){
            xData.push(item[this.pageCharConfig.X_bar_axis[0].tableField])
            this.BarChartData.yData = yData   // 柱形图 y轴
            this.BarChartData.xData = xData  // 柱形图 x轴
          }
          if(this.pageCharConfig.echarType == "曲线图"){
            xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
            this.LineChartData.yData = yData   // 曲线图 y1轴
            this.LineChartData.xData = xData   // 曲线图 x轴
          }
        }
      },

      // 编辑修改
      updateRowStatus (params) {
        // const $table = this.$refs.tableRef
        // console.log("$table", $table)
        console.log("params", params.row)
      },
      //实现单元格实时保存数据
      editClosedEvent ({ row, column }) {
        const $table = this.$refs.tableRef
        if ($table) {
          const field = column.field
          console.log("field:",JSON.stringify(field))
          const cellValue = row[field]
          console.log("cellValue:",JSON.stringify(cellValue))
          console.log("row:",JSON.stringify(row))
          // 判断单元格值是否被修改
          // if ($table.isUpdateByRow(row, field)) {
          //   setTimeout(() => {
          //     VxeUI.modal.message({
          //       content: `局部保存成功！ ${field}=${cellValue}`,
          //       status: 'success'
          //     })
          //     // 局部更新单元格为已保存状态
          //     $table.reloadRow(row, null, field)
          //   }, 300)
          // }
        }
      }

    }
  }
</script>

