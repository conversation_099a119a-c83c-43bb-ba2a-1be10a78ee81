<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="高炉" prop="prodcode">
        <!-- <el-input v-model="queryParams.prodCode" placeholder="加工中心编码" clearable @keyup.enter.native="handleQuery" /> -->
        <el-select v-model="queryParams.prodcode" placeholder="加工中心编码" clearable>
          <el-option v-for="item in optionProdCode" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <!-- <el-input v-model="queryParams.status" placeholder="选择状态" clearable @keyup.enter.native="handleQuery" /> -->
        <el-select v-model="queryParams.status" placeholder="选择状态" clearable>
          <el-option v-for="item in optionStatus" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="启用时间" prop="beginTime" v-if="false">
        <!-- <el-input v-model="queryParams.beginTime" placeholder="启用时间" clearable @keyup.enter.native="handleQuery" /> -->
        <el-date-picker v-model="queryParams.beginTime" type="date" placeholder="启用日期" @keyup.enter.native="handleQuery"
          clearable>
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini" v-if="canEditFlag" @click="handleAdd">{{
            labelAddBtn }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="btnDisableFlag"
            v-if="canEditFlag&&canEditFlagStatus" @click="handleUpdate">修改</el-button>
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
            @click="handleDelete">删除</el-button>
        </el-col> -->
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-caret-right" size="mini" :disabled="btnDisableFlag"
            @click="handleNext">{{ this.currrownextnode }}</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-edit" size="mini" :disabled="btnDisableFlag"
            v-if="canEditFlag&&canEditFlagStatus" @click="handleEditCoke">编辑焦</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-edit" size="mini" :disabled="btnDisableFlag"
            v-if="canEditFlag&&canEditFlagStatus" @click="handleEditSilo">编辑矿</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table ref="tbdata" v-loading="loading" element-loading-text="列表loading..." :data="planlist"
        @selection-change="handleSelectionChange" border style="width: 100%;height: 100%;" :header-cell-class-name="cellClass"
        :header-cell-style="{background:'#409EFF !important',color:'#fffff0 !important'}"
        :row-class-name="tableRowClassName">
        <el-table-column type="expand" fixed="left">
          <template slot-scope="props">
            <el-form label-position="left" inline class="demo-table-expand">
              <el-row>
              <!-- 合计展开内容 -->
              <el-col :span="24"><el-divider content-position="left" v-if="false"><i class="el-icon-s-ticket"><span>&nbsp;&nbsp;合计</span></i></el-divider></el-col>
              <el-col :span="6"  v-if="false">
                <el-form-item label="矿批">
                  <span>{{ props.row.oresumwt }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6"  v-if="false">
                <el-form-item label="焦炭">
                  <span>{{ props.row.cokesumwt }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6"  v-if="false">
                <el-form-item label="焦丁">
                  <span>{{ props.row.cokenutsumwt }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="6"  v-if="false">
                <el-form-item label="煤">
                  <span>{{ props.row.coalsumwt }}</span>
                </el-form-item>
              </el-col>
              <!-- 矿批展开内容 -->
              <el-col :span="24"><el-divider class="mydivider" content-position="left"><i class="el-icon-pie-chart"></i><span>&nbsp;&nbsp;矿批</span></el-divider></el-col>
              <el-col :span="calcExSpan(mitem.label)" v-for="(mitem,index) in props.row.exore">
                <el-form-item :label="mitem.label" label-width="auto">
                  <span>{{ mitem.val }}</span>
                </el-form-item>
              </el-col>
              <!-- 焦炭展开内容 -->
              <el-col :span="24"><el-divider class="mydivider" content-position="left"><i class="el-icon-pie-chart"><span>&nbsp;&nbsp;焦炭</span></i></el-divider></el-col>
              <el-col :span="calcExSpan(mitem.label)" v-for="(mitem,index) in props.row.excoke">
                <el-form-item :label="mitem.label" label-width="auto">
                  <span>{{ mitem.val }}</span>
                </el-form-item>
              </el-col>
              <!-- 焦丁展开内容 -->
              <el-col :span="24"><el-divider class="mydivider" content-position="left"><i class="el-icon-pie-chart"><span>&nbsp;&nbsp;焦丁</span></i></el-divider></el-col>
              <el-col :span="calcExSpan(mitem.label)" v-for="(mitem,index) in props.row.excokenut">
                <el-form-item :label="mitem.label" label-width="auto">
                  <span>{{ mitem.val }}</span>
                </el-form-item>
              </el-col>
              <!-- 煤展开内容 -->
              <el-col :span="24"><el-divider content-position="left" v-if="false"><i class="el-icon-pie-chart"><span>&nbsp;&nbsp;煤</span></i></el-divider></el-col>
              <el-col :span="calcExSpan(mitem.label)" v-for="(mitem,index) in props.row.excoal"  v-if="false">
                <el-form-item :label="mitem.label" label-width="auto">
                  <span>{{ mitem.val }}</span>
                </el-form-item>
              </el-col>
            </el-row>
            </el-form>
          </template>
        </el-table-column>
        <af-table-column type="selection" :fit="false" width="55" align="left" header-align="center" fixed="left" />
        <!-- <af-table-column label="ID" align="center" header-align="center" prop="planid" /> -->
        <af-table-column label="编号" align="center" header-align="center" prop="plan_no" width="120" />
        <af-table-column label="加工中心" align="center" header-align="center" prop="prod_code" v-if="'all' == roletag" />
        <af-table-column label="开始批次" align="center" header-align="center" prop="beginbat" width="150" />
        <af-table-column label="状态" align="center" header-align="center" prop="status" width="100" />
        <!-- <af-table-column label="结束批次" align="center" header-align="center" prop="endbat" /> -->
        <el-table-column label="合计" align="center" header-align="center">
          <af-table-column label="矿批" align="center" header-align="center" prop="oresumwt" />
          <af-table-column label="焦炭" align="center" header-align="center" prop="cokesumwt" />
          <af-table-column label="焦丁" align="center" header-align="center" prop="cokenutsumwt" />
          <af-table-column label=" 煤 " align="center" header-align="center" prop="coalsumwt" />
        </el-table-column>
        <!-- <el-table-column v-for="(itemtype,indextype) in colinfo" :key="indextype" align="center" :label="itemtype.label">
            <el-table-column v-for="(itemmate,indexmate) in itemtype.subcol" :key="indexmate" :prop="itemmate.prop" :label="itemmate.label" align="center">
            </el-table-column>
          </el-table-column> -->

        <el-table-column v-for="(itemtype, typeIndex) in colinfo" :key="`type-${typeIndex}-${itemtype.label}`" align="center" :label="itemtype.label">
          <el-table-column v-for="(itemmate, mateIndex) in itemtype.children" :key="`mate-${typeIndex}-${mateIndex}-${itemmate.prop}`" :prop="itemmate.prop"
            :label="itemmate.label" align="center">
          </el-table-column>
        </el-table-column>

        <af-table-column label="操作时间" align="center" header-align="center" prop="optime" />
        <af-table-column label="操作人" align="center" header-align="center" prop="opuser" />

        <!-- <af-table-column label="R2" align="center" header-align="center" prop="mainTargets.r2" />
          <af-table-column label="R3" align="center" header-align="center" prop="mainTargets.r4" />
          <af-table-column label="富氧率" align="center" header-align="center" prop="mainTargets.oxRate" />
          <af-table-column label="综合负载" align="center" header-align="center" prop="mainTargets.comLoad" />
          <af-table-column label="综合品味" align="center" header-align="center" prop="mainTargets.comGrade" />
          <af-table-column label="焦比" align="center" header-align="center" prop="mainTargets.ratioOfCoke" /> -->

        <el-table-column label="操作" align="center" header-align="center" fixed="right" width="200"
          class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
              v-if="canEditFlag&&'1'==scope.row.can_edit">修改</el-button>
            <el-popconfirm title="确定要删除选中方案吗？" trigger="click" @confirm="handleDelete(scope.row)" v-if="canEditFlag">
              <el-button slot="reference" size="mini" type="text" icon="el-icon-delete" @click="">删除</el-button>
            </el-popconfirm>
            <el-button size="mini" type="text" icon="el-icon-caret-right" @click="handleNext(scope.row)">{{
              scope.row.nextnode }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" :page-sizes="[10, 20, 30, 40]" />
    </el-row>

    <el-dialog :title="title" :visible.sync="dialogStartPlanOpen" v-if="dialogStartPlanOpen" width="400px"
      append-to-body destroy-on-close @close='cancel'>
      <el-form ref="formStartPlan" :model="startPlanInfo">
        <el-form-item label="工作日期" prop="begin_workdate">
          <!-- <el-input v-model="startPlanInfo.begin_workdate" placeholder="工作日期" clearable /> -->
          <el-date-picker v-model="startPlanInfo.begin_workdate" type="date" placeholder="工作日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="矿批号" prop="begin_batchnum">
          <el-input v-model="startPlanInfo.begin_batchnum" placeholder="使用计划的起始矿批号" clearable />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title=titleDiaPlan center ref="diaPlanInfo" :visible.sync="dialogPlanInfo" v-if="dialogPlanInfo"
      fullscreen append-to-body @close='cancel'>
      <planinfo ref="formPlanInfo" :planid="childrenKey" :srcplanid="childrenSrcKey"
        :propProdCode="defParamObj.prod_code"></planinfo>
    </el-dialog>

    <!-- 编辑焦弹窗组件 -->
    <EditCokeDialog
      :visible.sync="dialogEditCokeVisible"
      :plan-id="checkedPlanID"
      @save="handleSaveCoke"
      @close="handleCloseCoke"
    />

    <!-- 编辑矿弹窗组件 -->
    <EditOreDialog
      :visible.sync="dialogEditOreVisible"
      :plan-id="checkedPlanID"
      @save-success="handleSaveOreSuccess"
      @close="handleCloseOre"
    />

  </div>
</template>

<script>

import { getPlanMateList, gonext, bindserchbox, getallownode, obsplan } from "@/api/feedingplan/feedingpage";
import Treeselect from "@riophae/vue-treeselect";
import { getToken } from "@/utils/auth";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import moment from 'moment';
import planinfo from "./index.vue";
import { mount } from "sortablejs";
import EditCokeDialog from "./components/EditCokeDialog.vue";
import EditOreDialog from "./components/EditOreDialog.vue";

export default {
  name: "Material",
  dicts: ["effective_or_not", "material_tags"],
  components: { Treeselect, moment, planinfo, EditCokeDialog, EditOreDialog },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 列表主数据
      planlist: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workdate: null,
        prodcode: null,
        status: null,
        pagekey: null,
      },
      // 列信息
      colinfo: [],
      // 计划开始日期和批次
      startPlanInfo: {
        begin_workdate: this.$moment().format('YYYY-MM-DD'),
        begin_batchnum: null,
      },
      // 计划开始日期和批次显示标记
      dialogStartPlanOpen: false,
      // 计划详情显示标记
      dialogPlanInfo: false,
      // 选中计划的下一节点
      currrownextnode: '下一步',
      // 加工中心下拉数据
      optionProdCode: [],
      // 状态下拉数据
      optionStatus: [],
      // 子窗体计划主键
      // selectedPlanid: '',
      childrenKey: '',
      // 子窗体另存为的源计划主键
      // currrowplanid: '',
      childrenSrcKey: '',
      // 选中行计划ID
      checkedPlanID: '',
      // 选中行的计划状态
      // selectedStatus: null,
      checkedPlanStatus: '',
      // 选中行的计划编号
      checkedPlanNo: '',
      // 新增按钮文本
      labelAddBtn: '新增',
      // 页面权限标记
      roletag: null,
      // 页面可以操作的任务节点
      allownode: [],
      // 计划弹窗标题
      titleDiaPlan: '',
      // 按钮启用标记
      btnDisableFlag: true,
      // 行操作区可编辑标记
      canEditFlag: true,
      // 计划状态是否可编辑
      canEditFlagStatus: true,
      // 页面默认查询条件(服务端返回)
      defParamObj: {},
      // 编辑焦弹窗显示标记
      dialogEditCokeVisible: false,
      // 编辑矿弹窗显示标记
      dialogEditOreVisible: false,
    };
  },
  created() {
    if (undefined != this.$route.query.pagekey) {
      this.queryParams.pagekey = this.$route.query.pagekey;
    }
    if (undefined != this.$route.query.roletag) {
      this.roletag = this.$route.query.roletag;
    }
    console.log("roletag=", this.roletag);
    // 获取节点文字
    getallownode(this.roletag).then(resp => {
      if (resp.code == 200) {
        this.allownode = resp.data;
      }
    });
  },
  mounted() {
    // this.$refs.diaPlanInfo.rendered = true;
    // 是否可新增和编辑
    if(this.roletag == 'jc') {
      this.canEditFlag = false;
    }
    // 绑定搜索框数据源
    bindserchbox(this.queryParams.pagekey, null).then(resp => {
      if (resp.code == 200) {
        // console.log(resp);
        this.optionProdCode = resp.data.prod;
        this.optionStatus = resp.data.status;
      }
    }).catch(ex => {
    });
    // 加载数据
    this.getList();
  },
  methods: {
    /** 查询物料管理列表 */
    getList() {
      this.loading = true;
      // 清空选中计划ID
      this.resetCurrPlanID();
      getPlanMateList(this.queryParams).then((response) => {
        this.planlist = response.data.rows;
        // 处理展开表单信息
        this.planlist.forEach((item,index)=>{
          // item.exinfo = JSON.parse(item.exinfo);
          item.exore = JSON.parse(item.exore);
          item.excoke = JSON.parse(item.excoke);
          item.excokenut = JSON.parse(item.excokenut);
          item.excoal = JSON.parse(item.excoal);
        });
        console.log("planlist ",this.planlist);

        this.colinfo = JSON.parse(JSON.stringify(response.data.colinfo));
        this.defParamObj = response.data.defparam;
        if(this.defParamObj == null){
          this.defParamObj = {"prod_code" : ''};
        }
        this.total = response.data.total;
        this.loading = false;
      }).catch(err => {
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.dialogStartPlanOpen = false;
      this.dialogPlanInfo = false;
      this.reset("formStartPlan");
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      if (undefined != this.$route.query.pagekey) {
        this.queryParams.pagekey = this.$route.query.pagekey;
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // console.log(selection);
      this.btnDisableFlag = true;
      this.canEditFlagStatus = true;
      this.checkedPlanID = String(selection.map((item) => item.planid)[0] || '');
      this.checkedPlanNo = selection.map((item) => item.plan_no)[0];
      this.checkedPlanStatus = selection.map((item) => item.status)[0];
      this.currrownextnode = selection.map((item) => item.nextnode)[0];
      let rowCanEdit = selection.map((item) => item.can_edit)[0];
      // 设置单选
      if (selection.length > 1) {
        this.$refs.tbdata.clearSelection();
        const currObj = selection.pop();
        this.$refs.tbdata.toggleRowSelection(currObj);
      } else if (selection.length == 1) {
        this.btnDisableFlag = false;
        this.canEditFlagStatus = rowCanEdit == '1';
        this.labelAddBtn = "按模板新增";
        if (this.currrownextnode == null) {
          this.currrownextnode = "无后续步骤";
        }
      } else if (selection.length == 0) {
        this.labelAddBtn = "新增";
        this.currrownextnode = "下一步";
      }
      console.log("planNo=", this.checkedPlanNo, "  planID=", this.checkedPlanID);
    },
    /** 新增按钮操作 */
    handleAdd() {
      if (this.isEmptyStr(this.checkedPlanID)) {
        this.titleDiaPlan = "新增方案";
      } else {
        this.titleDiaPlan = "新增方案,模板为[" + this.checkedPlanNo + "]";
        this.childrenSrcKey = this.checkedPlanID;
        this.childrenKey = '';
      }
      this.dialogPlanInfo = true;
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      const rowplanid = String(row.planid || this.checkedPlanID)
      const rowplanno = row.plan_no || this.checkedPlanNo
      this.titleDiaPlan = "修改" + rowplanno;
      this.childrenKey = rowplanid;
      this.dialogPlanInfo = true;
    },
    /** 编辑焦按钮操作 */
    handleEditCoke() {
      if (!this.checkedPlanID) {
        this.$message.warning("请先选择一个计划");
        return;
      }

      this.dialogEditCokeVisible = true;
    },
    /** 编辑矿按钮操作 */
    handleEditSilo() {
      console.log("点击编辑矿按钮");
      console.log("checkedPlanID:", this.checkedPlanID);
      console.log("btnDisableFlag:", this.btnDisableFlag);
      console.log("canEditFlag:", this.canEditFlag);
      console.log("canEditFlagStatus:", this.canEditFlagStatus);

      if (!this.checkedPlanID) {
        this.$message.warning("请先选择一个计划");
        return;
      }

      this.dialogEditOreVisible = true;


      // 强制触发 Vue 的响应式更新
      this.$nextTick(() => {
        console.log("nextTick 后 dialogEditOreVisible:", this.dialogEditOreVisible);
      });
    },
    /** 焦保存 */
    handleSaveCoke(tableData) { 
      console.log("保存编辑焦数据:", tableData);
      this.dialogEditCokeVisible = false;
    },
    handleCloseCoke() {
      this.dialogEditCokeVisible = false;
    },

    /** 处理编辑矿保存成功事件 */
    handleSaveOreSuccess(tableData) {

      this.$message.success("保存成功");
      this.dialogEditOreVisible = false;
      this.getList();
    },
    /** 处理编辑矿关闭事件 */
    handleCloseOre() {
      this.dialogEditOreVisible = false;
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["formStartPlan"].validate((valid) => {
        if (valid) {
          gonext(this.roletag, this.currrowplanid, this.currrownextnode, this.startPlanInfo.begin_workdate, this.startPlanInfo.begin_batchnum).then(resp => {
            if (resp.code == 200) {
              // console.log(resp);
              this.$message.success("计划流程已执行到" + resp.data.node_name);
              this.getList();
              this.dialogStartPlanOpen = false;
            }
          }).catch(ex => {

          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const rowplanid = row.planid || this.ids[0];
      const rowstatus = row.status || this.selectedStatus;
      const rowplanno = row.plan_no || this.checkedPlanNo
      obsplan(this.roletag, rowplanid, rowstatus).then(resp => {
        if (resp.code == 200) {
          // console.log(resp);
          this.$message.success("计划", rowplanno, "已作废");
          this.getList();
        }
      }).catch(err => {

      })
    },
    // 计划业务流程
    handleNext(row) {
      // console.log(row);
      const rowplanid = row.planid || this.checkedPlanID
      const rowplanno = row.plan_no || this.checkedPlanNo
      this.childrenKey = rowplanid;
      const rowstatus = row.status || this.selectedStatus;
      // console.log("选中数据planid和status:", rowplanid, this.currrownextnode);
      if (this.currrownextnode == '启用' || rowstatus == '卷称接收') {
        // if (rowstatus == '卷称接收') {
        this.currrowplanid = rowplanid;
        this.dialogStartPlanOpen = true;
        this.currrownextnode = rowstatus;
      } else {
        gonext(this.roletag, rowplanid, rowstatus).then(resp => {
          if (resp.code == 200) {
            // console.log(resp);
            this.$message.success("计划流程已执行到" + resp.data.node_name);
            this.getList();
          }
        }).catch(err => {
          // this.$message.error(err);
        })
      }
    },
    // 清空form
    reset(refform) {
      this.resetForm(refform);
    },
    // 清空选中计划信息
    resetCurrPlanID() {
      this.checkedPlanID = '';
      this.checkedPlanNo = '';
      this.childrenKey = '';
      this.childrenSrcKey = '';
    },
    isEmptyStr(s) {
      if (s == undefined || s == null || s == '') {
        return true
      }
      return false
    },
    calcExSpan(s) {
      if(this.isEmptyStr(s)){
        return;
      }
      if (s.length > 10) {
        return 24;
      }
      return 6;
    },
    // 表格样式
    // tableRowClassName({ row, rowIndex }) {
    //   return 'orebg';
    // },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'disabledCheck'
      }
    },
    tableRowClassName({row, rowIndex}) {
        if (rowIndex%2 === 1) {
          return 'warning-row';
        } else {
          return 'success-row';
        }
        return '';
      }
  },
};
</script>

<style scoped>
/* 单元格回行设置 */
::deep .el-table .cell {
  white-space: nowrap;
  padding-left: 5px;
  padding-right: 5px;
}

.el-table .orebg {
  height: 5px;
  background: #c72036;
}

::v-deep .el-table__body-wrapper {
  .el-table__expanded-cell {
    z-index: 100;
    padding: 0;
  }
}
::v-deep .el-table__fixed,
::v-deep.el-table__fixed-right {
  .el-table__expanded-cell {
    visibility: hidden;
    padding: 0;
  }
}
</style>

<style>
.demo-table-expand {
  font-size: 0;
  width: 1200px;
  background: rgb(217, 236, 255);
}

.demo-table-expand label {
  width: 90px;
  /* color: #99a9bf; */
  font-family: "PingFang SC";
  color: #040f1e;
}

.demo-table-expand span {
  width: 90px;
  /* color: #99a9bf; */
  font-family: "Hiragino Sans GB";
  font-weight: bold;
  color: rgb(245, 108, 108);
}

.demo-table-expand .el-form-item {
  margin-left: 50px;
  margin-right: 0;
  margin-bottom: 0;
  width: 50%;
}

.el-table .warning-row {
  /* background: oldlace; */
  background: rgb(225, 243, 216);
}

.el-table .success-row {
  background: #f0f9eb;
}

.el-dialog__body {
  padding: 0 !important;
  padding-left: 2rem !important;
  padding-right: 2rem !important;
  padding-bottom: 0.55rem !important;
}

.mydivider {
  height: 3px;
  background-color: #040f1e;
}

.el-divider__text {
    position: absolute;
    background-color: rgb(217, 236, 255);
    padding: 0 20px;
    font-weight: 500;
    color: rgb(245, 108, 108);
}


</style>

<style scoped lang="scss">
/* 去掉全选按钮 */
::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
  display: none !important;
}

// 扩展设置表头文字
// ::v-deep .el-table .disabledCheck .cell::before {
//   content: '';
//   text-align: center;
//   line-height: 37px;
// }
</style>
