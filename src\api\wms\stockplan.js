import request from '@/utils/request'


//查询 
export function selectStockPlanData(query) {
    return request({
        url: '/api/wms/stockplan/list',
        method: 'get',
        params: query,
        // selectVO: selectVO
    })
}

//新增 修改
export function saveOrUpdate(data) {
    return request({
        url: '/api/wms/stockplan/saveOrUpdate',
        method: 'post',
        data: data,
        // selectVO: selectVO
    })
}

// 删除
export function deletePlan(planId) {
    return request({
        url: "/api/wms/stockplan/" + planId,
        method: "delete",
    });
}
