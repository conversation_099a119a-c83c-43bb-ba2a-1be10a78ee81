import request from '@/utils/request'

// 查询供应商列表
export function listSupplier(query, selectVO) {
  return request({
    url: '/api/md/supplier/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询供应商详细
export function getSupplier(id) {
  return request({
    url: '/api/md/supplier/' + id,
    method: 'get'
  })
}

// 新增供应商
export function addSupplier(data) {
  return request({
    url: '/api/md/supplier/add',
    method: 'post',
    data: data
  })
}

// 修改供应商
export function updateSupplier(data) {
  return request({
    url: '/api/md/supplier/edit',
    method: 'put',
    data: data
  })
}

// 删除供应商
export function delSupplier(id) {
  return request({
    url: '/api/md/supplier/' + id,
    method: 'delete'
  })
}

export function queryAllUsed() {
  return request({
    url: '/api/md/supplier/queryAllUsed',
    method: 'get'
  })
}

// 保存供应商
export function saveOrUpdate(data) {
  return request({
    url: '/api/md/supplier/saveOrUpdate',
    method: 'post',
    data: data
  })
}
