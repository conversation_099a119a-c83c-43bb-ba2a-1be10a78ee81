<template>
  <div class="echartsH_box">
    <div class="title-bar">
      <div class="title-group">
        <div class="title-text" :class="{active: active === '实际燃料比'}" @click="chagngePrecent('实际燃料比')">燃料比</div>
        <div class="title-text" :class="{active: active === '实际焦比'}" @click="chagngePrecent('实际焦比')">焦比</div>
        <div class="title-text" :class="{active: active === '实际煤比'}" @click="chagngePrecent('实际煤比')">煤比</div>
      </div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉'}" @click="chagngeGl('一高炉')">一高炉</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉'}" @click="chagngeGl('二高炉')">二高炉</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div class="echartsData-show">
        <div class="echarts-data">
          <div class="dataMarkRed"></div>
          <div class="dataLast">当前值： <span> {{ dataNow == '-' ?  dataNow : dataNow.toFixed(2) || '-' }} </span> kg/t </div>
        </div>
        <div class="echarts-data">
          <div class="dataMarkG"></div>
          <div class="dataLast">目标值： <span> {{ 100 || '-'  }} </span> kg/t </div>
        </div>
        <div class="echarts-data">
          <div class="dataPrecent" :style="{'color': dataPrecent > 0 ? '#F54E43' : '#4caf50'}">较昨日
            <img v-if="dataPrecent != '-'"
                 :src="dataPrecent > 0 ? require('@/assets/images/blastFurnace/up.png') : require('@/assets/images/blastFurnace/down.png')" alt="">
            <span> {{  dataPrecent == '-' ?  dataPrecent : (dataPrecent*100).toFixed(2) || '-'  }} </span>
            %
          </div>
        </div>
      </div>
      <div ref="echartsH" class="echartsH"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenRatio } from "@/api/analyse/blastfurnace";

export default {
  name: 'EChartsH',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      active: '实际燃料比',
      activeGL: '一高炉',
      dataNow: '-',
      chart: null,
      initDate: [],
      chartData: [
        { value: 75, name: '目标值', itemStyle: { color: '#95D475' } },
        { value: 25, name: '当前值', itemStyle: { color: '#F44336' } }
      ],
      centerText: '25%',
      dataPrecent: '-',
      param: {
        prodCenterCode: '1'
      }
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    initValue() {
      ironScreenRatio(this.param).then(res => {
        if (res.code === 200) {
          var data = res.data;
          this.initDate = data;
          this.dataNow = '-';
          this.dataPrecent = '-';
          this.active = '实际燃料比'
          if (data.length > 0) {
            data.forEach((item, index) => {
              if (item.name == '实际燃料比') {
                this.dataNow = item.value;
                this.dataPrecent = item.ratio;
                this.chartData[0].value = 100;
                this.chartData[1].value = item.value.toFixed(2);
              }
            });
          } else {
            this.chartData[0].value = 100;
            this.chartData[1].value = 0;
          }
          this.$nextTick(() => {
            this.initChart(this.chartData);
            window.addEventListener('resize', this.resizeChart);
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(chartData) {
      const chartDom = this.$refs.echartsH;
      this.chart = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: chartData.length > 0
          }]
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(255, 255, 255, 1)',
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',
          padding: 10
        },
        series: [
          {
            type: 'pie',
            center: ['35%', '50%'],
            radius: ['65%', '85%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'center',
              fontSize: 16,
              color: '#000',
              formatter: function (params) {
                if (params.name === '当前值') {
                  return params.value == 0 ? '-' : params.value + 'kg/t';
                }
                return '';
              },
            },
            labelLine: {
              show: false
            },
            data: chartData,
            itemStyle: {
              borderWidth: 0
            }
          }
        ],
      };
      this.chart.setOption(option);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    chagngePrecent(name) {
      this.active = name
      if (this.initDate.length > 0) {
        this.initDate.forEach((item, index) => {
          if (item.name == name) {
            this.dataNow = item.value;
            this.dataPrecent = item.ratio;
            this.chartData[0].value = 100;
            this.chartData[1].value = item.value.toFixed(2);
          }
        });
      } else {
        this.chartData[0].value = 100;
        this.chartData[1].value = 0;
      }
      this.$nextTick(() => {
        this.initChart(this.chartData);
        window.addEventListener('resize', this.resizeChart);
      })
    },
    chagngeGl(name) {
      if (name == '一高炉') {
        this.activeGL = '一高炉';
        this.param.prodCenterCode = '1';
        this.initValue();
      }
      if (name == '二高炉') {
        this.activeGL = '二高炉';
        this.param.prodCenterCode = '2';
        this.initValue();
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsH_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-group {
  width: 240px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: center;
  align-items: center;
}
.title-text {
  width: 80px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
  cursor: pointer;
}
.title-text:hover,
.title-text.active {
  color: #3c83ff;
  text-decoration: underline solid #3c83ff;
  text-underline-offset: 5px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 0 0 0;
}
.echartsData-show {
  width: 50%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-evenly;
}
.echarts-data {
  width: 100%;
  height: 15%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  align-content: center;
  justify-content: flex-start;
  align-items: center;
  padding: 0 0 0 30px;
}
.dataMarkRed,
.dataMarkG {
  background: #f44336;
  width: 8px;
  height: 8px;
}
.dataMarkG {
  background: #4caf50;
}
.dataLast,
.dataPrecent {
  font-family: Arial, Helvetica, sans-serif;
  font-size: 16px;
  margin: 0 0 0 10px;
}
.dataPrecent {
  margin: 0;
}
.dataLast > span {
  color: black;
}
.echartsH {
  width: 50%;
  height: 100%;
}
</style>
<style>
.buttonGLXL.el-button {
  border: none;
  background: #f2f3f5;
  color: #0852ff;
  height: 28px;
  width: 78px;
  padding: 0px;
  font-size: 13px;
  font-family: Arial, Helvetica, sans-serif;
}
.buttonGLXL.el-button:hover,
.buttonGLXL.el-button.active {
  background: #0852ff;
  color: white;
}
</style>