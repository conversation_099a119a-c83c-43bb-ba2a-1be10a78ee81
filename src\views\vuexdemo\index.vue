<template>
  <div class="app-container">
    <el-tabs type="border-card">
      <el-tab-pane label="单选">
        <vxe-toolbar ref="toolbarRef" custom></vxe-toolbar>
        <vxe-table
          save-state
          ref="tableRef"
          :local-storage-key="'vxe-myTable-state'"
          border
          stripe
          show-overflow
          height="500"
          header-align="center"
          :edit-config="{mode: 'row', trigger: 'click'}"
          @radio-change="radioChangeEvent"
          @checked-change="clearRadioRowEvent"
          :column-config="columnConfig"
          @column-dragstart="columnDragstartEvent"
          @column-dragend="columnDragendEvent"
          @cell-click="cellClickEvent"
          :radio-config="{labelField: 'role', trigger: 'row',highlight:true}"
          :header-cell-style="headerCellStyle"
          :row-style="rowStyle"
          :cell-style="cellStyle"
          :merge-cells="mergeCells"
          :data="tableData">
          <!--2.1 冻结列 fixed="left"-->
          <vxe-column type="radio" width="60" fixed="left">
            <template #header>
              <vxe-button mode="text" @click="clearRadioRowEvent" :disabled="!selectRow">选择</vxe-button>
            </template>
          </vxe-column>
          <vxe-colgroup title="基本信息" fixed="left">
            <vxe-column field="sex2" title="sex2" width="140" sortable :edit-render="{ name: 'input' }"></vxe-column>
            <vxe-column field="name2" title="Name2" width="140" sort-type="string" sortable :edit-render="{ name: 'input' }"></vxe-column>
          </vxe-colgroup>
          <!--2.2 多级表头设定，单元格内容合并 vxe-colgroup-->
          <vxe-colgroup title="更多信息" fixed="left">
            <vxe-column field="role3" title="Role" width="70"></vxe-column>
            <vxe-colgroup title="详细信息">
              <vxe-column field="sex3" title="Sex" width="140" :edit-render="sexEditRender"></vxe-column>
              <vxe-column field="age3" title="Age" width="70" sort-type="number" sortable></vxe-column>
            </vxe-colgroup>
          </vxe-colgroup>
          <vxe-colgroup title="分类信息" width="100">
            <vxe-column field="date4" title="Date" width="70"></vxe-column>
          </vxe-colgroup>
          <vxe-column field="address" title="Address" width="80" show-overflow></vxe-column>
          <vxe-column type="seq" width="70" ></vxe-column>
          <vxe-column field="name" title="Name" width="300"></vxe-column>
          <vxe-column field="sex" title="Sex" width="900"></vxe-column>
          <vxe-column field="age" title="Age" width="500"></vxe-column>
        </vxe-table>
      </el-tab-pane>
      <el-tab-pane label="多选">
        <vxe-table
          border
          stripe
          ref="tableRef"
          header-align="center"
          :row-config="{isHover: true}"
          @checkbox-change="handleCheckboxChange"
          :data="tableDataHandle">
          <vxe-column type="checkbox"  align="center" width="60"></vxe-column>
          <vxe-column type="fieldIndex" width="70"></vxe-column>
          <vxe-column field="filterable" title="Name"></vxe-column>
          <vxe-column field="label" title="Sex"></vxe-column>
          <vxe-column field="result_code" title="Age"></vxe-column>
        </vxe-table>
      </el-tab-pane>
    </el-tabs>

  </div>
</template>

<script>
export default {
  name: "vuexdemo",
  data() {
    return {
      selectRow: null,
      sexOptions:[
        { label: 'Man', value: '1' },
        { label: 'Woman', value: '0' }
      ],
      sexEditRender : {
        name: 'VxeSelect',
        options: [
          { label: '女', value: 'Women' },
          { label: '男', value: 'Man' }
        ]
      },
      headerCellStyle : ({ column }) => {
        if (column.field === 'name') {
          return {
            backgroundColor: '#f60',
            color: '#ffffff'
          }
        }
      },
      rowStyle : ({ rowIndex }) => {
        if ([2, 3, 5].includes(rowIndex)) {
          return {
            backgroundColor: 'red',
            color: '#ffffff'
          }
        }
      },
      cellStyle : ({ row, column }) => {
        if (column.field === 'sex') {
          if (row.sex >= '1') {
            return {
              backgroundColor: '#187'
            }
          } else if (row.age === 26) {
            return {
              backgroundColor: '#2db7f5'
            }
          }
        }
      },
      // 列拖拽
      columnConfig : {
        drag: true
      },
      // 合并行列
      mergeCells : [
        // { row: 1, col: 1, rowspan: 3, colspan: 1 },
        { row: 2, col: 9, rowspan: 2, colspan: 1 },
        { row: 4, col: 3, rowspan: 1, colspan: 3 }
      ],
      tableData:[
        { id: 10001, name: 'Test1', role: 'Develop', sex: 'Man', age: 28, address: 'test abc' },
        { id: 10002, name: 'Test2', role: 'Test', sex: 'Women', age: 22, address: 'Guangzhou' },
        { id: 10003, name: 'Test3', role: 'PM', sex: 'Man', age: 32, address: 'Shanghai' },
        { id: 10004, name: 'Test4', role: 'Designer', sex: 'Women', age: 24, address: 'Shanghai' },
        { id: 10005, name2: 'Test5', role: 'Develop', sex2: 'Man', age: 28, address: 'test abc' },
        { id: 10006, name: 'Test6', role: 'Designer', sex3: 'Women', age3: 24, address: 'Shanghai' },
        { id: 10007, name: 'Test7', role: 'Designer', sex3: 'Women', age3: 25, address: 'Shanghai' },
        { id: 10008, name: 'Test8', role: 'Designer', sex3: 'Women', age3: 26, address: 'Shanghai' },
        { id: 10009, name: 'Test9', role: 'Designer', sex3: 'Women', age3: 22, address: 'Shanghai' },
        { id: 100010, name: 'Test10', role: 'Designer', sex3: 'Women', age3: 21, address: 'Shanghai' },
        { id: 100011, name: 'Test11', role: 'Designer', sex3: 'Women', age3: 32, address: 'Shanghai' },
        { id: 100012, name: 'Test12', role: 'Designer', sex3: 'Women', age3: 34, address: 'Shanghai' },
        { id: 100013, name: 'Test13', role: 'Designer', sex3: 'Women', age3: 56, address: 'Shanghai' },
        { id: 100014, name: 'Test14', role: 'Designer', sex3: 'Women', age3: 44, address: 'Shanghai' },
        { id: 100015, name: 'Test15', role: 'Designer', sex3: 'Women', age3: 33, address: 'Shanghai' },
        { id: 100016, name: 'Test16', role: 'Designer', sex3: 'Women', age3: 31, address: 'Shanghai' },
        { id: 100017, name: 'Test17', role: 'Designer', sex3: 'Women', age3: 45, address: 'Shanghai' },
      ],
      tableDataHandle:[
        {"concise": true, "fieldIndex": "work_date", "filterable": true, "label": "变更日期"},
        {"concise": true, "fieldIndex": "智利精粉1010100045", "filterable": true, "label": "智利精粉", "result_code": "1010100045", "result_name": "智利精粉"},
        {"concise": true, "fieldIndex": "智利精粉1010100046", "filterable": true, "label": "智利精粉6", "result_code": "1010100046", "result_name": "智利精粉6"}
      ],
    }
  },
  created() {

  },
  mounted () {
    const $table = this.$refs.tableRef
    const $toolbar = this.$refs.toolbarRef
    if ($table && $toolbar) {
      $table.connect($toolbar)
    }
  },
  methods: {
    clearRadioRowEvent() {
      console.log('clearRadioRowEvent');
    },
    radioChangeEvent ({ row }) {
     console.log(JSON.stringify(row));
    },
    handleCheckboxChange({ checked, record }) {
      const table = this.$refs.tableRef;
      const selectedRows = table.getCheckboxRecords();
      console.log('选中的行数据：', selectedRows);
    },
    columnDragstartEvent ({ column }) {
      console.log(`拖拽开始 ${column.field}`)
    },
    columnDragendEvent ({ newColumn, oldColumn }) {
      console.log(`拖拽完成，旧列 ${oldColumn.field} 新列 ${newColumn.field}`)
    },
    // 事件监听
    cellClickEvent ({ row, column }) {
      console.log(`单击行：${row.id} 单击列：${column.title}`)
    },


  }


};
</script>


<!--1、绑定数据。-->
<!--2、边框、斑马线。-->
<!--2.1 冻结列，冻结行-->
<!--2.2 多级表头设定，单元格内容合并-->

<!--3、单选、多选-->
<!--4、事件：当前行改变事件（单选、多选）-->
<!--4.1 统一获取选中信息（单选，多选）-->
<!--4.2 点击时间-->

<!--5、样式：单元格、行、列 动态样式-->

<!--6、单元格：模板（输入框、下拉框）-->

<!--7、列拖拽和保存。-->
