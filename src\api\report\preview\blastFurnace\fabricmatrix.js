import request from '@/utils/request'

// 查询操作日志记录列表
export function listFabricMatrixSelect(query) {
  return request({
    url: '/api/fabricmatrix/listFabricMatrixSelect',
    method: 'get',
    params: query
  })
}


// 高炉风量数据 新增
export function fabricmatrixAdd(data) {
  return request({
    url: '/api/fabricmatrix/fabricmatrixAdd',
    method: 'post',
    data: data
  })
}

//   高炉风量 保存数据
export function fabricmatrixUpdateData(data) {
  return request({
    url: '/api/fabricmatrix/fabricmatrixUpdateData',
    method: 'put',
    data: data
  })
}
//   高炉风量 删除数据
export function fabricmatrixDeleteData(data) {
  return request({
    url: '/api/fabricmatrix/fabricmatrixDeleteData',
    method: 'delete',
    data: data
  })
}




