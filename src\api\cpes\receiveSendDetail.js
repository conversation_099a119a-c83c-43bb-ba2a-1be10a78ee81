import request from '@/utils/request'

// 查询烧结物料收发子列表
export function listDetail(query,selectVO) {
  return request({
    url: '/api/cpes/receiveSendDetai/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询烧结物料收发子详细
export function getDetail(detailId) {
  return request({
    url: '/api/cpes/receiveSendDetai/' + detailId,
    method: 'get'
  })
}

// 新增烧结物料收发子
export function addDetail(data) {
  return request({
    url: '/api/cpes/receiveSendDetai',
    method: 'post',
    data: data
  })
}

// 修改烧结物料收发子
export function updateDetail(data) {
  return request({
    url: '/api/cpes/receiveSendDetai',
    method: 'put',
    data: data
  })
}

// 删除烧结物料收发子
export function delDetail(detailId) {
  return request({
    url: '/api/cpes/receiveSendDetai/' + detailId,
    method: 'delete'
  })
}
