<template>
  <!-- <el-dialog title="编辑焦" :visible.sync="dialogVisible" width="80%" append-to-body @close="handleClose"> -->
    <el-dialog title="编辑焦" :visible.sync="dialogVisible" width="80%" append-to-body>
    <div class="edit-coke-container">
      <!-- 数据面板部分 -->
      <div class="data-panel">
        <el-card>
          <!-- <div slot="header">
            <span>数据面板</span>
          </div> -->
          <div class="panel-content">
            <el-table :data="panelTableData" border style="width: 100%;" size="small" class="panel-table">
              <el-table-column prop="type" label="" width="120" align="center" fixed="left">
                <template slot-scope="scope">
                  <span class="type-label">{{ scope.row.type }}</span>
                </template>
              </el-table-column>
              <el-table-column
                v-for="(material, index) in headerInfo"
                :key="Object.keys(material)[0]"
                :label="Object.values(material)[0]"
                align="center"
                min-width="100">
                <template slot-scope="scope">
                  <span v-if="scope.row.type === '物料总量'" class="amount-value total-amount">
                    {{ formatNumber(getStatValue(index, 0)) }}
                  </span>
                  <span v-else-if="scope.row.type === '物料剩余量'" class="amount-value remain-amount">
                    {{ formatNumber(getStatValue(index, 1)) }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </div>

      <!-- 列表部分 -->
      <div class="coke-list">
        <el-table :data="tableData" border style="width: 100%; margin-top: 16px;" v-loading="loading" class="main-table">
          <el-table-column prop="siloname" label="槽号" width="120" align="center" fixed="left">
            <template slot-scope="scope">
              <span class="silo-text">{{ scope.row.siloname || scope.row.silocode }}</span>
            </template>
          </el-table-column>
          <el-table-column label="物料选择" width="280" align="center" fixed="left">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.materialnumber"
                placeholder="请选择物料"
                filterable
                @change="handleCokeChange(scope.row)"
                class="material-select">
                <el-option
                  v-for="item in cokeOptions"
                  :key="item.materialnumber"
                  :label="`${item.materialname}`"
                  :value="item.materialnumber">
                  <span class="option-code">{{ item.materialnumber }}</span>
                  <span class="option-name">{{ item.materialname }}</span>
                </el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="CH1" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH1 === 1 }"
                @click="toggleChannel(scope.row, 'CH1')">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CH2" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH2 === 1 }"
                @click="toggleChannel(scope.row, 'CH2')">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CH3" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH3 === 1 }"
                @click="toggleChannel(scope.row, 'CH3')">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CH4" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH4 === 1 }"
                @click="toggleChannel(scope.row, 'CH4')">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CH5" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH5 === 1 }"
                @click="toggleChannel(scope.row, 'CH5')">
              </div>
            </template>
          </el-table-column>
          <el-table-column label="CH6" width="80" align="center">
            <template slot-scope="scope">
              <div class="channel-box"
                :class="{ 'active': scope.row.CH6 === 1 }"
                @click="toggleChannel(scope.row, 'CH6')">
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getCokeSelectList, getCokeMapping, saveCokeMapping } from "@/api/feedingplan/feedingpage";

export default {
  name: "EditCokeDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    planId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      saving: false,
      cokeOptions: [],
      tableData: [],
      headerInfo: [],
      statData: []
    };
  },
  computed: {
    panelTableData() {
      return [
        { type: '物料总量' },
        { type: '物料剩余量' }
      ];
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        this.dialogVisible = newVal;
        if (newVal && this.planId) {
          this.loadData();
        }
      },
      immediate: true
    },
    dialogVisible(newVal) {
      if (!newVal) {
        this.$emit('update:visible', false);
      }
    }
  },
  methods: {
    /** 加载数据 */
    async loadData() {
      if (!this.planId) {
        this.$message.warning("计划ID不能为空");
        return;
      }

      this.loading = true;

      try {
        // 获取焦批料仓匹配数据
        const mappingResponse = await getCokeMapping(this.planId);

        if (mappingResponse.code === 200) {
          const data = mappingResponse.data;

          // 处理头部信息（数据面板的物料列表）
          this.headerInfo = data.headerinfo || [];

          // 处理统计数据（物料总量和剩余量）
          this.statData = data.stat || [];

          // 处理主数据（表格数据）
          this.tableData = this.processMainData(data.maindata || []);

          // 获取下拉框选项数据
          await this.loadCokeOptions();

        } else {
          throw new Error(`获取数据失败: ${mappingResponse.msg || '未知错误'}`);
        }

      } catch (error) {
        console.error("加载数据失败:", error);
        this.$message.error("请求失败");
        // 初始化空数据
        this.headerInfo = [];
        this.statData = [];
        this.tableData = [];
        this.cokeOptions = [];
      } finally {
        this.loading = false;
      }
    },

    /** 加载下拉框选项数据 */
    async loadCokeOptions() {
      try {
        const selectResponse = await getCokeSelectList(this.planId);

        if (selectResponse.code === 200) {
          this.cokeOptions = selectResponse.data || [];
        } else {
          throw new Error(`获取下拉框选项失败: ${selectResponse.msg || '未知错误'}`);
        }
      } catch (error) {
        console.error("获取下拉框选项失败:", error);
        this.cokeOptions = [];
        this.$message.warning("获取物料选项失败");
      }
    },

    /** 处理主数据 */
    processMainData(maindata) {
      if (!maindata || maindata.length === 0) {
        return [];
      }

      return maindata.map(item => ({
        planid: item.planid || '',
        materialnumber: item.materialnumber || '',
        materialname: item.materialname || '',
        CH1: Number(item.CH1) || 0,
        CH2: Number(item.CH2) || 0,
        CH3: Number(item.CH3) || 0,
        CH4: Number(item.CH4) || 0,
        CH5: Number(item.CH5) || 0,
        CH6: Number(item.CH6) || 0,
        silocode: item.silocode || '',
        siloname: item.siloname || item.silocode || '',
        slotname: item.slotname || item.siloname || item.silocode || '',
        prodcentercode: item.prodcentercode || '',
        prodcentername: item.prodcentername || ''
      }));
    },

    /** 获取统计数据值 */
    getStatValue(materialIndex, statIndex) {
      if (!this.statData || !this.statData[statIndex]) {
        return 0;
      }

      const statItem = this.statData[statIndex];
      const keys = Object.keys(statItem);

      if (materialIndex < keys.length) {
        return statItem[keys[materialIndex]] || 0;
      }

      return 0;
    },

    /** 格式化数字显示 */
    formatNumber(value) {
      if (!value) return '0';
      const num = parseFloat(value);
      if (isNaN(num)) return '0';
      return num.toLocaleString();
    },


    /** 物料选择变化 */
    handleCokeChange(row) {
      // 根据选择的物料编号，更新物料名称
      const selectedMaterial = this.cokeOptions.find(item => item.materialnumber === row.materialnumber);
      if (selectedMaterial) {
        row.materialname = selectedMaterial.materialname;
      }

      this.$emit('coke-change', row);
    },

    /** 切换状态 */
    toggleChannel(row, channel) {
      row[channel] = row[channel] === 1 ? 0 : 1;
     
      this.$emit('channel-toggle', { row, channel, value: row[channel] });
    },
    /** 处理关闭 */
    // handleClose() {
    //   if (this.hasUnsavedChanges()) {
    //     this.$confirm('有未保存的修改，确定要关闭吗？', '提示', {
    //       confirmButtonText: '确定',
    //       cancelButtonText: '取消',
    //       type: 'warning'
    //     }).then(() => {
    //       this.resetData();
    //       this.$emit('close');
    //     }).catch(() => {
    //       // 用户取消关闭，不做任何操作
    //     });
    //   } else {
    //     this.resetData();
    //     this.$emit('close');
    //   }
    // },

    /** 检查是否有未保存的修改 */
    // hasUnsavedChanges() {
    //   return this.tableData.some(row => row.materialnumber);
    // },
    /** 取消 */
    handleCancel() {
      this.dialogVisible = false;
    },
    /** 保存 */
    async handleSave() {
      if (!this.planId) {
        this.$message.warning("计划ID不能为空");
        return;
      }

      this.saving = true;
      try {
        // 构建保存数据，格式与获取接口相同
        const saveData = {
          headerinfo: this.headerInfo,
          stat: this.statData,
          maindata: this.buildSaveMainData()
        };

        const response = await saveCokeMapping(this.planId, saveData);

        if (response.code === 200) {
          this.$message.success("保存成功");
          this.$emit('save-success', this.tableData);
          this.dialogVisible = false;
        } else {
          this.$message.error(response.msg || "保存失败");
        }
      } catch (error) {

        this.$message.error("保存失败");
      } finally {
        this.saving = false;
      }
    },

    /** 构建保存的主数据 */
    buildSaveMainData() {
      return this.tableData.map(row => ({
        planid: row.planid || this.planId,
        materialnumber: row.materialnumber || '',
        materialname: row.materialname || '',
        CH1: String(row.CH1 || 0),
        CH2: String(row.CH2 || 0),
        CH3: String(row.CH3 || 0),
        CH4: String(row.CH4 || 0),
        CH5: String(row.CH5 || 0),
        CH6: String(row.CH6 || 0),
        silocode: row.silocode || '',
        siloname: row.siloname || '',
        slotname: row.slotname || '',
        prodcentercode: row.prodcentercode || '',
        prodcentername: row.prodcentername || ''
      }));
    },


    /** 重置数据 */
    resetData() {
      this.tableData = [];
      this.cokeOptions = [];
      this.headerInfo = [];
      this.statData = [];
      this.loading = false;
      this.saving = false;
    }
  }
};
</script>

<style scoped>

/* 去掉弹窗的margin */
.el-dialog {
  margin: 0 !important;
}

.edit-coke-container {
  height: 75vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.data-panel {
  flex: 0 0 auto;
  margin-bottom: 24px;
}

.data-panel .el-card__header {
  font-size: 16px;
  font-weight: 600;
  padding: 18px 20px;
}

.panel-content {
  padding: 0;
}

.panel-table .el-table th {
  font-size: 14px;
  font-weight: 600;
  padding: 16px 0;
}

.panel-table .el-table td {
  padding: 14px 0;
  font-size: 14px;
}

.type-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.amount-value {
  font-weight: 600;
  font-size: 15px;
}

.total-amount {
  color: #67c23a;
}

.remain-amount {
  color: #e6a23c;
}

.coke-list {
  flex: 1;
  overflow-y: auto;
}

.main-table .el-table th {
  font-size: 15px;
  font-weight: 600;
  padding: 18px 0;
}

.main-table .el-table td {
  padding: 16px 0;
  font-size: 14px;
}

.silo-text {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.material-select .el-input__inner {
  font-size: 14px;
  padding: 10px 12px;
  height: 38px;
}

.option-code {
  float: left;
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.option-name {
  float: right;
  color: #8492a6;
  font-size: 13px;
}


.channel-box {
  width: 24px;
  height: 24px;
  border: 2px solid #dcdfe6;
  background-color: white;
  cursor: pointer;
  margin: 0 auto;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.channel-box:hover {
  border-color: #409eff;
  box-shadow: 0 2px 6px rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
}

.channel-box.active {
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.dialog-footer {
  padding: 20px 0;
  text-align: center;
  border-top: 1px solid #e9ecef;
  background: #fafafa;
  margin: 0 -20px -20px -20px;
}

.dialog-footer .el-button {
  padding: 12px 28px;
  border-radius: 6px;
  font-weight: 500;
  margin: 0 8px;
  font-size: 14px;
}
</style>
