import request from '@/utils/request'

// 查询内控标准列表
export function listStanad(query,selectVO) {
  return request({
    url: '/api/md/internalStanad/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

export function queryForManger(query) {
  return request({
    url: '/api/md/internalStanad/queryForManger',
    method: 'get',
    params: query,
  })
}

// 查询内控标准详细
export function getStanad(stanadId) {
  return request({
    url: '/api/md/internalStanad/' + stanadId,
    method: 'get'
  })
}

// 新增内控标准
export function addStanad(data) {
  return request({
    url: '/api/md/internalStanad',
    method: 'post',
    data: data
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/md/internalStanad/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 修改内控标准
export function updateStanad(data) {
  return request({
    url: '/api/md/internalStanad',
    method: 'put',
    data: data
  })
}

// 删除内控标准
export function delStanad(stanadId) {
  return request({
    url: '/api/md/internalStanad/' + stanadId,
    method: 'delete'
  })
}
