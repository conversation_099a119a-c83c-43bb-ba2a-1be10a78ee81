<template>
  <div class="app-container">
    <el-tabs v-model="selectedWorkModeId" type="card" @tab-click="switchWorkMode">
      <el-tab-pane :key="item.id" v-for="(item, index) in workModeData" :label="item.workModeName" :name="item.id + ''">
        <el-row :gutter="20">
          <el-col :span="16" :xs="24">
            <el-tabs v-model="selectedTabName">
              <el-tab-pane label="班次管理" name="workShift">
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleWorkShiftAdd"
                      v-hasPermi="['work:shift:add']">新增</el-button>
                  </el-col>
                </el-row>
                <el-table :data="workShiftList" style="width: 100%">
                  <el-table-column prop="id" label="主键" align="center">
                  </el-table-column>
                  <el-table-column prop="workShiftName" label="班次名称" align="center">
                  </el-table-column>
                  <el-table-column prop="startTime" label="上班时间" align="center">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.startTime, '{h}:{i}:{s}') }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="endTime" label="下班时间" align="center">
                    <template slot-scope="scope">
                      <span>{{ parseTime(scope.row.endTime, '{h}:{i}:{s}') }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="createBy" label="创建人" align="center">
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" align="center">
                  </el-table-column>
                  <el-table-column prop="updateBy" label="修改人" align="center">
                  </el-table-column>
                  <el-table-column prop="updateTime" label="修改时间" align="center">
                  </el-table-column>
                </el-table>
              </el-tab-pane>
              <el-tab-pane label="班别管理" name="workClass">
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleWorkClassAdd"
                      v-hasPermi="['work:class:add']">新增</el-button>
                  </el-col>
                </el-row>
                <el-table :data="workClassList" style="width: 100%">
                  <el-table-column prop="id" label="主键" align="center">
                  </el-table-column>
                  <el-table-column prop="workClassName" label="班别名称" align="center">
                  </el-table-column>
                  <el-table-column prop="createBy" label="创建人" align="center">
                  </el-table-column>
                  <el-table-column prop="createTime" label="创建时间" align="center">
                  </el-table-column>
                  <el-table-column prop="updateBy" label="修改人" align="center">
                  </el-table-column>
                  <el-table-column prop="updateTime" label="修改时间" align="center">
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-col>
          <el-col :span="8" :xs="24">
            <el-tabs value="workShiftSet">
              <el-tab-pane label="倒班规则" name="workShiftSet">
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="generateShiftRules"
                      v-hasPermi="['work:shift:set']">生成倒班规则</el-button>
                  </el-col>
                </el-row>
                <el-table :data="workShiftSetList" style="width: 100%">
                  <el-table-column prop="sqeNum" label="排序码" align="center">
                  </el-table-column>
                  <el-table-column prop="workClass" label="班别" align="center">
                  </el-table-column>
                  <el-table-column prop="workShift" label="班次" align="center">
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </el-col>
        </el-row>
      </el-tab-pane>
    </el-tabs>
    <!-- 添加或修改班别对话框 -->
    <el-dialog :title="workClassTitle" :visible.sync="workClassDialogOpen" width="500px" append-to-body>
      <el-form ref="form" :model="workClassForm" label-width="80px">
        <el-form-item label="班别名称" prop="workModeName">
          <el-input v-model="workClassForm.workClassName" placeholder="请输入班别名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitWorkClass">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改班次对话框 -->
    <el-dialog :title="workShiftTitle" :visible.sync="workShiftDialogOpen" width="500px" append-to-body>
      <el-form ref="form" :model="workShiftForm" label-width="80px">
        <el-form-item label="班次名称" prop="workShiftName">
          <el-input v-model="workShiftForm.workShiftName" placeholder="请输入班次名称" />
        </el-form-item>
        <el-form-item label="上班时间" prop="startTime">
          <el-time-picker v-model="workShiftForm.startTime" value-format='yyyy-MM-dd HH:mm:ss' placeholder="任意时间点">
          </el-time-picker>
        </el-form-item>
        <el-form-item label="下班时间" prop="endTime">
          <el-time-picker v-model="workShiftForm.endTime" value-format='yyyy-MM-dd HH:mm:ss' placeholder="任意时间点">
          </el-time-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitWorkShift">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listByName, addWorkClass, listWorkClassByWorkModeId, addWorkShift, listWorkShiftByWorkModeId, generateShiftRules, queryWorkShiftSetByWorkModeId } from "@/api/system/work";

export default {
  name: "WorkClass",
  data() {
    return {
      // 班制树数据
      workModeData: [],
      selectedTabName: 'workShift',
      selectedWorkModeId: '',
      workClassDialogOpen: false,
      workClassForm: {
        workClassName: undefined,
        workModeId: undefined
      },
      workClassTitle: '新增班别',
      workClassList: [],
      workShiftTitle: '新增班次',
      workShiftDialogOpen: false,
      workShiftForm: {
        workShiftName: undefined,
        workModeId: undefined,
        startTime: undefined,
        endTime: undefined
      },
      workShiftList: [],
      workShiftSetList: []
    }
  },
  created() {
    this.init();
  },
  methods: {
    async init() {
      await this.getWorkMode();
      this.switchWorkMode();
    },
    async getWorkMode() {
      await listByName({ pageSize: 99999, name: '' }).then(res => {
        this.workModeData = res.rows;
        this.selectedWorkModeId = res.rows[0].id + ''
      })
    },
    handleWorkClassAdd() {
      this.workClassDialogOpen = true;
      this.workClassForm = {
        workClassName: undefined,
        workModeId: undefined
      }
      this.workClassForm.workModeId = this.selectedWorkModeId;
    },
    handleWorkShiftAdd() {
      this.workShiftDialogOpen = true;
      this.workShiftForm = {
        workShiftName: undefined,
        workModeId: undefined,
        startTime: undefined,
        endTime: undefined
      };
      this.workShiftForm.workModeId = this.selectedWorkModeId;
    },
    // 取消按钮
    cancel() {
      this.workClassDialogOpen = false;
      this.workShiftDialogOpen = false;
    },
    // 班别提交按钮
    submitWorkClass() {
      addWorkClass(this.workClassForm).then(res => {
        this.$modal.msgSuccess("新增成功");
        this.workClassDialogOpen = false;
        this.switchWorkMode();
      })
    },
    switchWorkMode() {
      listWorkClassByWorkModeId(this.selectedWorkModeId).then(res => {
        this.workClassList = res.data
      })
      listWorkShiftByWorkModeId(this.selectedWorkModeId).then(res => {
        this.workShiftList = res.data
      })
      this.getWorkShiftSetList();
    },
    // 班次提交按钮
    submitWorkShift() {
      addWorkShift(this.workShiftForm).then(res => {
        this.$modal.msgSuccess("新增成功");
        this.workShiftDialogOpen = false;
        this.switchWorkMode();
      })
    },
    // 生成倒班规则
    generateShiftRules() {
      generateShiftRules(this.selectedWorkModeId).then(res => {
        this.$modal.msgSuccess("新增成功");
        this.getWorkShiftSetList();
      })
    },
    // 查询倒班规则
    getWorkShiftSetList() {
      queryWorkShiftSetByWorkModeId(this.selectedWorkModeId).then(res => {
        this.workShiftSetList = res.data;
      })
    }
  }
}

</script>