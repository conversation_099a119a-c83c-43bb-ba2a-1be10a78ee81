<template>
	<div class="app-container" v-loading="loading">
		<v-form-render :form-json="formJson" :form-data="formData" :option-data="optionData" ref="vfr">
		</v-form-render>
		<div slot="footer" class="dialog-footer">
			<el-button type="primary" @click="submitForm">保存</el-button>
			<el-button type="primary" @click="testfuncfrm" v-show="false">测试按钮</el-button>
		</div>
		<div>{{ testvalue }}</div>
	</div>
</template>
<script>
import { getformjson, getformdata, saveformdata, querynoterole } from '@/api/formtemplate/details';
import dayjs from "dayjs";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
export default {
	props: ["propPageKey","propDefFormData","propFilterParam","propRowData"],
	data() {
		return {
			formJson: {},
			formData: {},
			optionData: {},
			pageKey: '',
			filterParam: {},
			testvalue: '',
			saveflag: false,
			loading: false,
			defFormData: {},
		}
	},
	created() {
		this.filterParam = this.propFilterParam;
		this.pageKey = this.propPageKey;
		this.renderForm();
	},
	mounted() {
    if(this.pageKey == 'CPES01013'){
      this.formData.CPES01013_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02013'){
      this.formData.CPES02013_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01007'){
      this.formData.CPES01007_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02007'){
      this.formData.CPES02007_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01015'){
      this.formData.CPES01015_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02015'){
      this.formData.CPES02015_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01014'){
      this.formData.CPES01014_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02014'){
      this.formData.CPES02014_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01010'){
      this.formData.CPES01010_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02010'){
      this.formData.CPES02010_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01011'){
      this.formData.CPES01011_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02011'){
      this.formData.CPES02011_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01012'){
      this.formData.CPES01012_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02012'){
      this.formData.CPES02012_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01016'){
      this.formData.CPES01016_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02016'){
      this.formData.CPES02016_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'IPES02001'){
      this.formData.IPES02001_t_summary_result_master_ipes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01018'){
          this.formData.CPES01018_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES02018'){
      this.formData.CPES02018_t_summary_result_master_cpes02_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01005'){
      this.formData.CPES01005_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }
    if(this.pageKey == 'CPES01006'){
      this.formData.CPES01006_t_summary_result_master_cpes01_WORK_DATE_ = dayjs(new Date()).format("YYYY-MM-DD");
    }

		this.loadData();
	},
	methods: {
		submitForm() {
			this.loading = true;
			this.$refs.vfr.getFormData().then(formData => {
        if(this.pageKey == 'CPES01013'){
          formData.CPES01013_t_summary_result_master_cpes01_CREATE_TIME_ = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
        }
        if(this.pageKey == 'CPES02013'){
          formData.CPES02013_t_summary_result_master_cpes02_CREATE_TIME_ = dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss");
        }
				//校验成功
				// this.$message.success(JSON.stringify(formData));
				//保存数据
				saveformdata(this.pageKey, this.filterParam, formData).then(response => {
					if (response == undefined) {
						this.loading = false;
						this.$message.error("修改失败");
					}
					if (response.code == 200) {
						this.$message.success("修改成功");
						this.saveflag = true;
            this.$emit('call-parent-method');

					} else {
						this.$message.error("修改失败");
					}
					// console.log(response.code);
					this.loading = false;
				}).catch(error => {
					this.loading = false;
				});
			}).catch(error => {
				// 校验失败
				this.$message.error(error);
				this.loading = false;
			})
		},
		clearFormData() {
			// console.log("清除builder");
			// this.$nextTick(()=>{
			// 	this.$refs.vfr.resetForm();
			// })
		},
		renderForm() {
			if (this.pageKey.length === 0) {
				return;
			}
			getformjson(this.pageKey).then((response) => {
				this.formjson = response;
				this.clearFormData();
				this.$refs.vfr.setFormJson(this.formjson);
				this.$nextTick(() => {
					// 加载缺省数据
					Object.keys(this.defFormData).forEach(k => {
						this.$refs.vfr.setFieldValue(k, this.defFormData[k]);
					});
				});
			});
		},
		loadData() {
			this.loading = true;
			this.clearFormData();
			this.$forceUpdate();
			this.$nextTick(() => {
				this.clearFormData();
				this.$forceUpdate();
				if (JSON.stringify(this.filterParam) == "{}" || this.pageKey.length === 0) {
					this.loading = false;
					return;
				}
				// 加载数据
				getformdata(this.filterParam, this.pageKey).then((response) => {
					if (response == undefined) {
						this.loading = false;
						this.$message.error("修改失败");
						return;
					}
					this.formData = response;
					console.log(response);
					this.$refs.vfr.setFormData(this.formData);
					this.$forceUpdate();
					this.loading = false;
				})
			});
		},
		testfuncfrm() {
			// alert('这是formbuilder.vue的testfunc');
			// console.log("执行testfunc之前");
			window.testfunc();
			// console.log("执行testfunc以后");
		},
		hhh() {
			alert('hhh');
		},

	}
}
</script>
