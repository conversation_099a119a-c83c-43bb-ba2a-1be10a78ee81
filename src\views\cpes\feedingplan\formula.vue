<template>
  <div class="app-container">
    <el-tabs v-model="activeTab" type="border-card">
      <el-tab-pane label="配料单" name="planCalc">
        <plan-calc v-if="activeTab === 'planCalc'" :planid="planIdLocal" :srcplanid="srcPlanIdLocal" :propProdCode="prodCodeLocal" ref="planCalc" />
      </el-tab-pane>
      <el-tab-pane label="block" name="formulaBlock" v-if="showBlock">
        <formula-block v-if="activeTab === 'formulaBlock'" :planid="planIdLocal" ref="formulaBlock" />
      </el-tab-pane>
      <el-tab-pane label="物料-料仓匹配" name="materialBinMatch">
        <material-bin-match v-if="activeTab === 'materialBinMatch'" :planid="planIdLocal" :prodCode="prodCodeLocal"
          :dataList2nd="list2nd" ref="materialBinMatch" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import PlanCalc from './planCalc.vue'
import FormulaBlock from './formulaBlock.vue'
import MaterialBinMatch from './materialBinMatch.vue'

export default {
  name: "Formula",
  components: {
    PlanCalc,
    FormulaBlock,
    MaterialBinMatch
  },
  props: {
    planId: {
      type: [String, Number],
      default: ''
    },
    srcPlanId: {
      type: [String, Number],
      default: ''
    },
    prodCode: {
      type: String,
      default: ''
    },
    dataList2nd: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeTab: 'planCalc',
      planIdLocal: this.planId,
      srcPlanIdLocal: this.srcPlanId,
      prodCodeLocal: this.prodCode,
      list2nd: this.dataList2nd,
      showBlock: this.prodCode === 'CPES01'
    }
  },
  watch: {
    planId(newVal) {
      this.planIdLocal = newVal;
      this.refreshData();
    },
    srcPlanId(newVal) {
      this.srcPlanIdLocal = newVal;
      this.refreshData();
    },
    prodCode(newVal) {
      this.prodCodeLocal = newVal;
      this.showBlock = newVal === 'CPES01';
      this.refreshData();
    },
    dataList2nd(newVal) {
      this.list2nd = newVal;
      this.refreshData();
    },
    activeTab(newVal) {
      this.$nextTick(() => {
        this.refreshData();
      });
    }
  },
  created() {
    // 初始化时刷新数据
    this.refreshData();
  },
  mounted() {
    this.$nextTick(() => {
      this.refreshData();
    });
  },
  methods: {
    refreshData() {
      // 刷新当前选项卡的数据
      if (this.activeTab === 'planCalc') {
        this.$nextTick(() => {
          if (this.$refs.planCalc && this.$refs.planCalc.getList) {
            this.$refs.planCalc.getList();
          }
        });
      } else if (this.activeTab === 'formulaBlock') {
        this.$nextTick(() => {
          if (this.$refs.formulaBlock && this.$refs.formulaBlock.refreshData) {
            this.$refs.formulaBlock.refreshData();
          }
        });
      } else if (this.activeTab === 'materialBinMatch') {
        this.$nextTick(() => {
          if (this.$refs.materialBinMatch && this.$refs.materialBinMatch.handleRefresh) {
            this.$refs.materialBinMatch.handleRefresh();
          }
        });
      }
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 10px;
  background-color: #f5f7fa;
}

/* 选项卡样式 */
/deep/ .el-tabs__header {
  margin-bottom: 15px;
}

/deep/ .el-tabs__item {
  height: 40px;
  line-height: 40px;
  font-size: 16px;
  font-weight: 500;
}

/deep/ .el-tabs__item.is-active {
  color: #409EFF;
  font-weight: bold;
}

/deep/ .el-tabs__content {
  background-color: #fff;
  border-radius: 0 0 4px 4px;
  padding: 0;
  overflow: visible;
}

.app-container {
  padding-bottom: 20px;
}
</style>