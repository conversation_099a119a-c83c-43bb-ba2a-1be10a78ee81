import request from '@/utils/request'

// 查询生产信息综合管理明细列表
export function listDetail(query,selectVO) {
  return request({
    url: '/api/cpes/generalInfodetail/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询生产信息综合管理明细详细
export function getDetail(detailId) {
  return request({
    url: '/api/cpes/generalInfodetail/' + detailId,
    method: 'get'
  })
}

// 新增生产信息综合管理明细
export function addDetail(data) {
  return request({
    url: '/api/cpes/generalInfodetail',
    method: 'post',
    data: data
  })
}

// 修改生产信息综合管理明细
export function updateDetail(data) {
  return request({
    url: '/api/cpes/generalInfodetail',
    method: 'put',
    data: data
  })
}

// 删除生产信息综合管理明细
export function delDetail(detailId) {
  return request({
    url: '/api/cpes/generalInfodetail/' + detailId,
    method: 'delete'
  })
}
