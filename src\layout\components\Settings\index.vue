<template>
  <el-drawer size="300px" :visible="visible" :with-header="false" :append-to-body="true" :show-close="false" >
    <div class="drawer-container">
      <!-- 标题 -->
      <div class="application">
        <div class="application-title">模块切换</div>
      </div>
      <!-- 系统 -->
      <div class="app" v-for="(item,index) in appList" :key="index" @click="application(item.url,index)">
        <div class="" :class="[listIndex !== index ? 'app-item' : 'app-itemcli']">
           <i class="el-icon-setting"></i>&nbsp;{{item.name}}
        </div>
      </div>
      
    </div>
    <!-- 退出登录 -->
    <el-row>
      <el-col :span="24">
          <div class="login-out">
            <div class="padd" @click="logout">
              退出登录
            </div>
          </div>
      </el-col>
    </el-row>
  </el-drawer>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
   data() {
    return {
      lists:[],
      listIndex:0
    };
  },
  computed: {
    ...mapGetters(['appList']),
    visible: {
      get() {
        return this.$store.state.settings.showSettings
      }
    },
  },
  methods: {
    // 退出登录
    logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '';
        })
      }).catch(() => {});
    },
    // 模块跳转
    application(url,index){
      this.listIndex = index
      window.open(url,"_self");

    }
  }
}
</script>
<style scoped>
.login-out{
  width: 300px;
  position: fixed;
  bottom: 0px;
  text-align: center;
  cursor:pointer;
  line-height: 40px;
  font-size: 14px;
  
}
.padd{
  background: #CED2D3;
  color:#00C770;
  width: 300px;
  font-weight: 600;
  border-radius: 10px;
}
/* .padd:hover {
  background: rgb(83, 168, 255);
} */
.application{
  padding: 10px 20px;
  background-color: #F3F6F6;
  margin-bottom: 30px;
}
.application-title{
  border-left: 4px solid #67C23A;
  padding-left: 10px;
  font-size: 18px;
  font-weight: 600;
  font-family:"微软雅黑";
}
.app-itemcli{
  line-height: 50px;
  text-align: center;
  background-image: linear-gradient(to right, #52B75C 0%, #99CA4B 100%);
  /* background-image: linear-gradient(to right, #00dbde 0%, #fc00ff 100%); */
  color: #fff;
  cursor:pointer;
}
.app-item:hover{
  line-height: 50px;
  text-align: center;
  background-image: linear-gradient(to right, #52B75C 0%, #99CA4B 100%);
  /* background-image: linear-gradient(to right, #00dbde 0%, #fc00ff 100%); */
  color: #fff;
   cursor:pointer;
}
.app-item{
  line-height: 50px;
  text-align: center;
  /* background-image: linear-gradient(to right, #52B75C 0%, #99CA4B 100%); */
  /* background-image: linear-gradient(to right, #00dbde 0%, #fc00ff 100%); */
  color: #565859;
  
  cursor:pointer;
}

.app{
  padding: 10px 0px 0px 0px;
}
</style>
