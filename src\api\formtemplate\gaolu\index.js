import request from '@/utils/request'

// 查询表单数据列表
export function listData(query) {
    return request({
      url: '/api/lingxiao/master/list',
      method: 'get',
      params: query
    })
  }

  // 查询表单类型详细
export function getType(id) {
    return request({
      url: '/api/lingxiao/master/'+ id,
      method: 'get'
    })
  }

  // 查询表单类型详细
export function addType(data) {
    return request({
      url: '/api/lingxiao/master/addType' ,
      method: 'post',
      data: data
    })
  }

  // 修改表单类型
export function updateType(data) {
    return request({
      url: '/api/lingxiao/master/updateType',
      method: 'put',
      data: data
    })
  }