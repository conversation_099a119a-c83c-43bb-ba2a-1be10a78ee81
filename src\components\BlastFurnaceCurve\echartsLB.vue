<template>
  <div class="echartsLB_box">
    <div class="title-bar">
      <div class="title-group">
        <div class="title-text" :class="{active: active === '铁水成分'}" @click="chagngePrecent('铁水成分')">铁水成分</div>
        <div class="title-text" :class="{active: active === '合格率'}" @click="chagngePrecent('合格率')">合格率</div>
      </div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一高炉'}" @click="chagngeGl('一高炉')">一高炉</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二高炉'}" @click="chagngeGl('二高炉')">二高炉</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsL" class="echartsL" v-if="active === '铁水成分'"></div>
      <div ref="echartsB" class="echartsB" v-if="active === '合格率'"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { ironScreenComponent } from "@/api/analyse/blastfurnace";

export default {
  name: 'EChartsLB',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      active: '铁水成分',
      activeGL: '一高炉',
      dataNow: 212,
      chart: null,
      chart1: null,
      param: {
        prodCenterCode: '1'
      }
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart) {
          this.chart.resize();
        }
        if (this.chart1) {
          this.chart1.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.initValue();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    if (this.chart1) {
      this.chart1.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    initValue() {
      ironScreenComponent(this.param).then(res => {
        if (res.code === 200) {
          var data = res.data;
          var xDataP = [];
          var xDataS = [];
          var xDataSi = [];
          var yData = [];
          if (data.length > 0) {
            data.forEach((item, index) => {
              xDataP[index] = (item.p * 100) || 0;
              xDataS[index] = (item.s * 100) || 0;
              xDataSi[index] = (item.si * 100) || 0;
              yData[index] = item.time || '0';
            });
          } else {
            xDataP = [];
            xDataS = [];
            xDataSi = [];
            yData = [];
          }
          this.$nextTick(() => {
            this.initChart(xDataP, xDataS, xDataSi, yData);
            window.addEventListener('resize', this.resizeChart);
          })
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(xDataP, xDataS, xDataSi, yData) {
      let that = this;
      /** 铁水成分 */
      const chartDom = this.$refs.echartsL;
      this.chart = echarts.init(chartDom);
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            // 条件显示
            invisible: yData.length > 0 && xDataS.length > 0 && xDataSi.length > 0 && xDataP.length > 0
          }]
        },
        title: {
          text: '铁水当日合格率：80%',
          left: '3%',
          top: '0',
          textStyle: {
            color: '#4A5566',
            fontSize: 14,
            fontWeight: 'normal',
          }
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(246, 248, 252, 0.8)',
          borderColor: '#fff',
          borderWidth: 2,
          borderRadius: 5,
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          axisPointer: {
            type: 'line',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(255, 255, 255, 0.5);',
          padding: 10,
          formatter: function (params) {
            let date = new Date(params[0].name);
            let tooltipHtml = `<div style="margin-bottom:5px;font-size: 13px;color: #000;">${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}</div>`;
            params.forEach(item => {
              const color = item.color;
              const value = typeof item.value === 'number' ? item.value.toFixed(2) : item.value;
              tooltipHtml +=
                `<div style="display:flex;align-items:center;margin-bottom:3px;background: #fff;color: #22272E;padding:5px;
                  border-radius:5px;font-size: 12px">
                  <span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:${color};margin-right:5px;"></span>
                  <span style="margin-right:10px;">${that.activeGL} </span>
                  <span style="margin-right:10px;">${item.seriesName}:</span>
                  <span>${value} %</span>
                </div>`;
            });
            return tooltipHtml;
          }
        },
        legend: {
          type: 'plain',
          icon: 'circle',
          right: '5%',
          itemHeight: 10,
          itemWidth: 10,
          textStyle: {
            fontSize: 12
          },
          data: ['Si', 'S', 'P']
        },
        grid: {
          top: '22%',
          left: '3%',
          right: '4%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#F4F4F4'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A3A3A3',
              fontSize: 10
            },
            formatter: function (value) {
              return echarts.format.formatTime('hh:mm', value);
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          data: yData
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A3A3A3',
              fontSize: 10
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F4F4F4'
            }
          },
          interval: 25
        },
        series: [
          {
            name: 'Si',
            type: 'line',
            smooth: true,
            symbolSize: 0,
            itemStyle: {
              color: '#1875F0'
            },
            data: xDataSi
          },
          {
            name: 'S',
            type: 'line',
            smooth: true,
            symbolSize: 0,
            itemStyle: {
              color: '#50D166'
            },
            data: xDataS
          },
          {
            name: 'P',
            type: 'line',
            smooth: true,
            symbolSize: 0,
            itemStyle: {
              color: '#21CCFF'
            },
            data: xDataP
          }
        ]
      };
      this.chart.setOption(option);
    },
    initChart1() {
      /** 合格率 */
      const chartDom1 = this.$refs.echartsB;
      this.chart1 = echarts.init(chartDom1);
      const option1 = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(255, 255, 255, 1)',
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',
          padding: 10
        },
        grid: {
          top: '5%',
          left: '3%',
          right: '4%',
          bottom: '2%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#F4F4F4'
            }
          },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A3A3A3',
              fontSize: 10
            }
          },
          axisTick: { show: false },
          splitLine: { show: false },
          data: ['夜', '早', '中']
        },
        yAxis: {
          type: 'value',
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            show: true,
            textStyle: {
              color: '#A3A3A3',
              fontSize: 10
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#F4F4F4'
            }
          },
          interval: 25
        },
        series: [
          {
            name: '合格率',
            type: 'bar',
            barWidth: '20%',
            itemStyle: {
              color: '#1875F0'
            },
            data: [82, 32, 85],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          },
        ]
      };
      this.chart1.setOption(option1);
    },
    resizeChart() {
      if (this.chart) {
        this.chart.resize();
      }
      if (this.chart1) {
        this.chart1.resize();
      }
    },
    chagngePrecent(name) {
      if (name === '铁水成分') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart1) {
            this.chart1.dispose();
          }
          this.initValue();
        })
      }
      if (name === '合格率') {
        this.active = name
        this.$nextTick(() => {
          if (this.chart) {
            this.chart.dispose();
          }
          this.initChart1();
          window.addEventListener('resize', this.resizeChart);
        })
      }
    },
    chagngeGl(name) {
      if (name == '一高炉') {
        this.activeGL = '一高炉';
        this.param.prodCenterCode = '1';
        this.initValue();
      }
      if (name == '二高炉') {
        this.activeGL = '二高炉';
        this.param.prodCenterCode = '2';
        this.initValue();
      }
    }
  },
  beforeDestroy() {
  }
}
</script>
<style scoped>
.echartsLB_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 10px 15px;
  justify-content: space-between;
}
.title-group {
  width: 200px;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: center;
  align-items: center;
}
.title-text {
  width: 100px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
  cursor: pointer;
}
.title-text:hover,
.title-text.active {
  color: #3c83ff;
  text-decoration: underline solid #3c83ff;
  text-underline-offset: 5px;
}
.button-group {
}
.echarts-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 5px 0 0 0;
}
.echartsL,
.echartsB {
  width: 100%;
  height: 100%;
}
</style>