<template>
  <div class="app-container" style="padding: 0px;">
    <div class="block" >
      <span >发布时间:</span>
      <el-date-picker style="margin-left: 10px;width: 392px;margin-right: 10px;"
                      v-model="publishTimeArr" type="datetimerange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
      </el-date-picker>
      <span>烧结:</span>
<!--      <el-form-item label="烧结">-->
        <el-select v-model="selectParam.staveNo" placeholder="请选择" style="    margin-left: 10px;">
          <el-option v-for="item in staveNoArr" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
<!--      </el-form-item>-->
      <el-button type="primary" style="margin-left: 20px;" @click="handleQuery" icon="el-icon-search" size="mini">搜索</el-button>
    </div>
    <div>
      <vxe-table
        style="margin-left: 20px;margin-right: 20px;"
        border
        :data="tableData"
        :header-cell-style="headerCellStyle"
        @header-cell-click="headerCellClickEvent"
        :cell-config="{height: 35}"
        :height="400"
        @cell-click="cellClickEvent"
      >
        <vxe-column type="seq" width="70"></vxe-column>
        <vxe-column field="prodCenterName" title="加工中心名称"></vxe-column>
        <vxe-column field="mateName" title="物料名称"></vxe-column>
        <vxe-column field="publishTime" title="发布时间" width="auto"></vxe-column>
        <vxe-column field="粒度40mm" title=">40"></vxe-column>
        <vxe-column field="粒度25_40mm" title="40-25"></vxe-column>
        <vxe-column field="粒度16_25mm" title="25-16"></vxe-column>
        <vxe-column field="粒度10_16mm" title="16-10"></vxe-column>
        <vxe-column field="粒度_10mm" title="<10"></vxe-column>
        <vxe-column field="筛分" title="筛分"></vxe-column>
        <vxe-column field="转鼓" title="转鼓"></vxe-column>
      </vxe-table>
      <lineChart :LineChartData="LineChartData" class="linechartClass" />
    </div>

  </div>
</template>

<script>
  import lineChart from '../../../../components/chart/lineChart';
  import { lineEchartsComm } from '@/api/chart/lineChartTemplate.js';
  import {
    sinteredOreParticleList,
  } from "@/api/report/preview/blastFurnace/sinteredOreParticle";
  import dayjs from "dayjs";
  export default {
    components: {
      lineChart
    },
    name: "sinteredOreParticle",
    data(){
      return{
        LineChartData: {
          xData:  [], // X轴数据
          yData:  [] ,// Y轴数据（曲线形高度）
        },
        loading:true,
        tableData:[],
        publishTimeArr:[],
        selectParam:{
          startTime:'',
          endTime:'',
          prodCenterCode:'',
          staveNo:'',
        },
        staveNoArr:[{
          value: '1#烧结',
          label: '1#烧结'
        },{
          value: '2#烧结',
          label: '2#烧结'
        }],
        queryParams: {
          startTime:'',
          endTime:'',
          staveNo:'',
        },
        headerCellStyle:null,
        pageCharConfig:null,

      }
    },
    created() {
      this.publishTimeArr.push(dayjs(new Date()).add(-1, "day"));
      this.publishTimeArr.push(dayjs(new Date()).add(1, "day"));
      const obj = JSON.parse(JSON.stringify(this.selectParam))
      if (this.publishTimeArr.length == 2) {
        obj.dtstart = dayjs(this.publishTimeArr[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        obj.dtend = dayjs(this.publishTimeArr[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.selectParam.startTime =  obj.dtstart
        this.selectParam.endTime =  obj.dtend

      }
      this.queryList();

      this.LineChartData.description = this.getFileName();  // 曲线元素名称
      let config = null;
      let lineConfig = lineEchartsComm().filter(x=>x.businessCode==this.getLineFileUrl());
      if(lineConfig != null || lineConfig.length > 0){
        config = lineConfig;
      }
      console.log("config:",JSON.stringify(config))
      if(config==null || config.length==0){
        //  提示
      }
      this.pageCharConfig=config[0];

      // 显示曲线得列填充颜色
      if(this.pageCharConfig.Y_line_axis.length>0){
        let tableFieldArr=[];
        for(let i=0;i<this.pageCharConfig.Y_line_axis.length;i++){
          tableFieldArr.push( this.pageCharConfig.Y_line_axis[i].tableField)
          console.log("tableFieldArr:",JSON.stringify(tableFieldArr))
          this.headerCellStyle = ({ column }) => {
            console.log("column:",column.field)
            console.log("tableFieldArr.includes(column.field ):",tableFieldArr.includes(column.field ))
            if (tableFieldArr.includes(column.field ) ) {
              return {
                backgroundColor: '#ECF5FF',
                color: '#606266'
              }
            }
          }
        }
      }
    },
    methods:{
      /** 图形名称 */
      getFileName() {
        return this.$route.query.FileName;
      },
      /** 曲线图路径参数 */
      getLineFileUrl() {
        console.log("this.$route.query.lineFileUrl:",this.$route.query.lineFileUrl)
        return this.$route.query.lineFileUrl;
      },
      /* 获取路径高炉号*/
      getProdCenterCode(){
        return this.$route.query.prodCenterCode;
      },

      /*搜索按钮*/
      handleQuery(){
        if(this.publishTimeArr != null){
          if (this.publishTimeArr.length == 2) {
            this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.queryList();
      },

      /* 查询数据列表 */
      queryList(){
        if(this.selectParam.staveNo == '' || this.selectParam.staveNo == null){
          this.selectParam.staveNo = '1#烧结'
        }
        if(this.publishTimeArr != null){
          if (this.publishTimeArr.length == 2) {
            this.selectParam.startTime = dayjs(this.publishTimeArr[0]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
            this.selectParam.endTime = dayjs(this.publishTimeArr[1]).format(
              "YYYY-MM-DD HH:mm:ss"
            );
          }
        }
        this.selectParam.prodCenterCode = this.getProdCenterCode();
        sinteredOreParticleList(this.selectParam).then(response=>{
          this.tableData = response.data
          if( this.tableData.length>0){
            var xData=[];
            var yData=[];
            let yitem = null;
            for (let i = 0; i < this.tableData.length ; i++) {
              const  item = this.tableData[i];
              this.LineChartData.seriesName1 = '粒度40mm'
              this.LineChartData.legendName = '粒度40mm'
              yData.push(item['粒度40mm'])
              if(this.pageCharConfig.echarType == "曲线图"){
                xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
                this.LineChartData.yData = yData   // 曲线图 y1轴
                this.LineChartData.xData = xData   // 曲线图 x轴
              }
            }
          }
          this.loading = false
          console.log("response:",JSON.stringify(response))
        });
      },
      /*表头单元格点击事件*/
      headerCellClickEvent ({ column }) {
        console.log(`表头单元格点击${column.field}`)
        let tableFild = '';
        tableFild=column.field;
        this.chartPlay(tableFild);
      },
      /* // 点击单列*/
      cellClickEvent ({ row, column }) {
        console.log("column:",column)
        let tableFild = '';
        tableFild=column.field;
        this.chartPlay(tableFild);
      },
      //曲线图展示
      chartPlay(tableFild){
        let yitem = null;
        if(this.pageCharConfig.echarType == "曲线图"){
          yitem = this.pageCharConfig.Y_line_axis.find(x=>x.tableField==tableFild);
          this.LineChartData.description = this.pageCharConfig.description; // 曲线元素名称
          this.LineChartData.seriesName1 = yitem.name
          this.LineChartData.legendName = yitem.name
        }

        if(yitem==null || yitem.length==0){
          return;
        }
        var xData=[];
        var yData=[];
        for (let i = 0; i < this.tableData.length ; i++) {
          const  item = this.tableData[i];
          yData.push(item[yitem.tableField])
          if(this.pageCharConfig.echarType == "曲线图"){
            xData.push(item[this.pageCharConfig.X_line_axis[0].tableField])
            this.LineChartData.yData = yData   // 曲线图 y1轴
            this.LineChartData.xData = xData   // 曲线图 x轴
          }
        }
      },

    }
  }
</script>

<style scoped>
  .block{
    margin-top: 10px;
    margin-bottom: 10px;
    margin-left: 10px;
  }
</style>
