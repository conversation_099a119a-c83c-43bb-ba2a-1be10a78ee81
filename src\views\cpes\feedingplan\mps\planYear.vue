<template>
  <div class="app-container">
    <!-- 搜索条件 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="年份" prop="planYear">
        <el-date-picker
          v-model="queryParams.planYear"
          type="year"
          placeholder="选择年份"
          format="yyyy"
          value-format="yyyy"
          style="width: 150px;"
        />
      </el-form-item>
      <el-form-item label="加工中心" prop="prodCenterCode">
        <el-select v-model="queryParams.prodCenterCode" placeholder="请选择加工中心" clearable>
          <el-option
            v-for="center in processingCenterList"
            :key="center.id"
            :label="center.name"
            :value="center.code"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <!-- <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button> -->
      </el-form-item>
    </el-form>

    <!-- 数据表格 -->
    <vxe-table
      ref="tableRef"
      v-loading="loading"
      :data="planList"
      border
      stripe
      size = "mini"
    >
      <vxe-column field="planYear" title="年份" width="70" align="center" fixed="left"></vxe-column>
      <vxe-column field="prodCenterName" title="加工中心" width="90" align="center" fixed="left"></vxe-column>
      <vxe-column field="statusName" title="状态" width="80" align="center" fixed="left"></vxe-column>

      <!-- 年计划 -->
      <vxe-colgroup title="年计划" align="center">
        <vxe-column field="yearStriveTarget" title="力争" width="75" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.yearStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="yearEnsureTarget" title="必保" width="75" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.yearEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="yearActualComplete" title="实绩" width="75" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.yearActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 1月 -->
      <vxe-colgroup title="1月" align="center">
        <vxe-column field="janStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.janStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="janEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.janEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="janActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.janActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 2月 -->
      <vxe-colgroup title="2月" align="center">
        <vxe-column field="febStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.febStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="febEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.febEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="febActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.febActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 3月 -->
      <vxe-colgroup title="3月" align="center">
        <vxe-column field="marStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.marStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="marEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.marEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="marActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.marActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 4月 -->
      <vxe-colgroup title="4月" align="center">
        <vxe-column field="aprStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.aprStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="aprEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.aprEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="aprActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.aprActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 5月 -->
      <vxe-colgroup title="5月" align="center">
        <vxe-column field="mayStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.mayStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="mayEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.mayEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="mayActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.mayActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 6月 -->
      <vxe-colgroup title="6月" align="center">
        <vxe-column field="junStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.junStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="junEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.junEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="junActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.junActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 7月 -->
      <vxe-colgroup title="7月" align="center">
        <vxe-column field="julStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.julStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="julEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.julEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="julActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.julActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 8月 -->
      <vxe-colgroup title="8月" align="center">
        <vxe-column field="augStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.augStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="augEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.augEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="augActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.augActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 9月 -->
      <vxe-colgroup title="9月" align="center">
        <vxe-column field="sepStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.sepStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="sepEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.sepEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="sepActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.sepActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 10月 -->
      <vxe-colgroup title="10月" align="center">
        <vxe-column field="octStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.octStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="octEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.octEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="octActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.octActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 11月 -->
      <vxe-colgroup title="11月" align="center">
        <vxe-column field="novStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.novStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="novEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.novEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="novActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.novActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>

      <!-- 12月 -->
      <vxe-colgroup title="12月" align="center">
        <vxe-column field="decStriveTarget" title="力争" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.decStriveTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="decEnsureTarget" title="必保" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.decEnsureTarget) }}</span>
          </template>
        </vxe-column>
        <vxe-column field="decActualComplete" title="实绩" width="65" align="center">
          <template #default="{ row }">
            <span>{{ formatNumber(row.decActualComplete) }}</span>
          </template>
        </vxe-column>
      </vxe-colgroup>


    </vxe-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>


  </div>
</template>

<script>
import {
  listYearPlan,

  getProcessingCenterList
} from "@/api/feedingplan/mps";

export default {
  name: "PlanYearQuery",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 年度计划表格数据
      planList: [],
      // 表格高度
   
      // 加工中心列表
      processingCenterList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        planYear: undefined,
        prodCenterCode: undefined
      }
    };
  },
  created() {
    // 初始化年份为当前年份
    this.queryParams.planYear = new Date().getFullYear().toString();
    this.getList();
    this.getProcessingCenterList();
  },
  mounted() {
    
  },
  beforeDestroy() {
   
  },
  methods: {

    /** 查询年度计划列表 */
    getList() {
      this.loading = true; 
       listYearPlan(this.queryParams).then(response => {
         this.planList = response.rows || [];
         this.total = response.total || 0;
         this.loading = false;
       }).catch(() => {
         this.planList = [];
        this.total = this.planList.length;
        this.loading = false;
       });
    },

    /** 获取加工中心列表 */
    getProcessingCenterList() {
      this.processingCenterList = [
        { id: 1, name: '1#烧结', code: 'CPES01' },
        { id: 2, name: '2#烧结', code: 'CPES02' },
         { id: 3, name: '1#高炉', code: 'IPES01' },
         { id: 4, name: '2#高炉', code: 'IPES02' },
      ];
    },

    /** 格式化数字 */
    formatNumber(value) {
      if (value === null || value === undefined || value === '') {
        return '-';
      }
      return Number(value).toLocaleString();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 导出按钮操作 */
   /* handleExport() {
      this.download('/mps/yearplan/export', {
        ...this.queryParams
      }, `年度生产计划查询_${new Date().getTime()}.xlsx`);
    }*/
  }
};
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.mb8 {
  margin-bottom: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;

}
.pagination-container .el-pagination {
  position:static !important;
  right: auto !important;
}

.dialog-footer {
 
}

/* 表格样式优化 */
::v-deep .vxe-table {
  font-size: 13px;
}

::v-deep .vxe-table .vxe-header--column {
  background-color: #f5f7fa;
  font-weight: bold;
}

::v-deep .vxe-table .vxe-body--row:hover {
  background-color: #f5f7fa;
}

/* 表单样式优化 */
::v-deep .el-form-item {
  margin-bottom: 18px;
}

::v-deep .el-input-number {
  width: 100%;
}

/* 按钮样式 */
.el-button + .el-button {
  margin-left: 8px;
}
</style>