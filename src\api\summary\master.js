import request from '@/utils/request'

// 查询汇总配置明细列表
export function list(query, selectVO) {
  return request({
    url: '/api/summary/master/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

export function detailList(query) {
  return request({
    url: '/api/summary/master/detailList',
    method: 'get',
    params: query,
  })
}

export function addDetail(data) {
  return request({
    url: '/api/summary/master',
    method: 'post',
    data: data
  })
}

