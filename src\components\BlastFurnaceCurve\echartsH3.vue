<template>
  <div class="echartsG_box">
    <div class="title-bar">
      <div class="title-text">烧结矿成品合格率</div>
      <div class="button-group">
        <el-button class="buttonGLXL" round :class="{active: activeGL === '一烧结'}" @click="chagngeGl('一烧结')">一烧结</el-button>
        <el-button class="buttonGLXL" round :class="{active: activeGL === '二烧结'}" @click="chagngeGl('二烧结')">二烧结</el-button>
      </div>
    </div>
    <div class="echarts-container">
      <div ref="echartsH31" class="echartsGauge"></div>
      <div ref="echartsH32" class="echartsGauge"></div>
      <div ref="echartsH33" class="echartsGauge"></div>
    </div>
  </div>
</template>
<script>
import echarts from 'echarts';
import { cpesScreenPassRate } from "@/api/analyse/sintering";

export default {
  name: 'EChartsH3',
  props: {
  },
  components: {
  },
  directives: {},
  data() {
    return {
      activeGL: '一烧结',
      chart1: null,
      chart2: null,
      chart3: null,
      chartData1: [
        { value: 90, name: '合格', itemStyle: { color: '#409EFF' } },
        { value: 10, name: '不合格', itemStyle: { color: '#95D475' } }
      ],
      chartData2: [
        { value: 90, name: '合格', itemStyle: { color: '#409EFF' } },
        { value: 10, name: '不合格', itemStyle: { color: '#95D475' } }
      ],
      chartData3: [
        { value: 90, name: '合格', itemStyle: { color: '#409EFF' } },
        { value: 10, name: '不合格', itemStyle: { color: '#95D475' } }
      ],
      param: {
        prodCenterCode: 'CPES01'
      },
    }
  },
  computed: {
    isCollapse() {
      return this.$store.state.app.sidebar.opened;
    }
  },
  watch: {
    isCollapse() {
      setTimeout(() => {
        if (this.chart1) {
          this.chart1.resize();
        }
        if (this.chart2) {
          this.chart2.resize();
        }
        if (this.chart3) {
          this.chart3.resize();
        }
      }, 300);
    }
  },
  activated() {
    this.intDate();
  },
  beforeDestroy() {
    if (this.chart1) {
      this.chart1.dispose();
    }
    if (this.chart2) {
      this.chart2.dispose();
    }
    if (this.chart3) {
      this.chart3.dispose();
    }
    window.removeEventListener('resize', this.resizeChart);
  },
  methods: {
    chagngeGl(name) {
      if (name == '一烧结') {
        this.activeGL = name
        this.param.prodCenterCode = 'CPES01';
        this.intDate();
      }
      if (name == '二烧结') {
        this.activeGL = name
        this.param.prodCenterCode = 'CPES02';
        this.intDate();
      }
    },
    intDate() {
      cpesScreenPassRate(this.param).then(res => {
        if (res.code === 200) {
          var date = res.data;
          if (date.length > 0) {
            date.forEach(element => {
              if (element.workClsass == '早') {
                this.chartData1[0].value = (Number(element.passRate) * 100).toFixed(2);
                this.chartData1[1].value = 100 - this.chartData1[0].value;
                this.$nextTick(() => {
                  const chartDom1 = this.$refs.echartsH31;
                  this.initChart('早班', this.chartData1, chartDom1);
                  window.addEventListener('resize', this.resizeChart);
                })
              } else {
                this.chartData1[0].value = '-';
                this.chartData1[1].value = '-';
                this.$nextTick(() => {
                  const chartDom1 = this.$refs.echartsH31;
                  this.initChart('早班', this.chartData1, chartDom1);
                  window.addEventListener('resize', this.resizeChart);
                })
              }
              if (element.workClsass == '中') {
                this.chartData2[0].value = (Number(element.passRate) * 100).toFixed(2);
                this.chartData2[1].value = 100 - this.chartData2[0].value;
                this.$nextTick(() => {
                  const chartDom2 = this.$refs.echartsH32;
                  this.initChart('中班', this.chartData2, chartDom2);
                  window.addEventListener('resize', this.resizeChart);
                })
              } else {
                this.chartData2[0].value = '-';
                this.chartData2[1].value = '-';
                this.$nextTick(() => {
                  const chartDom2 = this.$refs.echartsH32;
                  this.initChart('中班', this.chartData2, chartDom2);
                  window.addEventListener('resize', this.resizeChart);
                })
              }
              if (element.workClsass == '夜') {
                this.chartData3[0].value = (Number(element.passRate) * 100).toFixed(2);
                this.chartData3[1].value = 100 - this.chartData3[0].value;
                this.$nextTick(() => {
                  const chartDom3 = this.$refs.echartsH33;
                  this.initChart('夜班', this.chartData3, chartDom3);
                  window.addEventListener('resize', this.resizeChart);
                })
              } else {
                this.chartData3[0].value = '-';
                this.chartData3[1].value = '-';
                this.$nextTick(() => {
                  const chartDom3 = this.$refs.echartsH33;
                  this.initChart('夜班', this.chartData3, chartDom3);
                  window.addEventListener('resize', this.resizeChart);
                })
              }
            });
          } else {
            this.chartData1[0].value = '-';
            this.chartData1[1].value = '-';
            this.chartData2[0].value = '-';
            this.chartData2[1].value = '-';
            this.chartData3[0].value = '-';
            this.chartData3[1].value = '-';
            this.$nextTick(() => {
              const chartDom1 = this.$refs.echartsH31;
              this.initChart('早班', this.chartData1, chartDom1);
              const chartDom2 = this.$refs.echartsH32;
              this.initChart('中班', this.chartData2, chartDom2);
              const chartDom3 = this.$refs.echartsH33;
              this.initChart('夜班', this.chartData3, chartDom3);
              window.addEventListener('resize', this.resizeChart);
            })
          }
        } else {
          this.$message.error(res.msg || "获取数据失败");
        }
      }).catch((error) => {
        console.error("获取数据出错:", error);
      });
    },
    initChart(title, date, container) {
      if (title == '早班') {
        this.chart1 = echarts.init(container);
      }
      if (title == '中班') {
        this.chart2 = echarts.init(container);
      }
      if (title == '夜班') {
        this.chart3 = echarts.init(container);
      }
      const option = {
        graphic: {
          elements: [{
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '暂无数据',
              fill: '#999',
              fontSize: 16,
            },
            invisible: date[0].value != '-' && date[1].value != '-'
          }]
        },
        title: {
          show: date[0].value != '-' && date[1].value != '-',
          text: title + '\n' + date[0].value + '%',
          left: 'center',
          top: 'center',
          textStyle: {
            fontSize: 19,
            color: '#000000',
            lineHeight: date[0].value == 100 ? 25 : 30,
            fontWeight: 'normal',
            fontFamily: 'sourcehanMedium'
          }
        },
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(255, 255, 255, 1)',
          textStyle: {
            color: '#36373B',
            fontSize: 12
          },
          extraCssText: 'box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);',
          padding: 10
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '50%'],
            radius: ['60%', '85%'],
            startAngle: -45,
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: false,
              }
            },
            labelLine: {
              show: false
            },
            data: date,
            itemStyle: {
              show: false,
              borderColor: '#fff',
              borderWidth: date[1].value == 0 ? 0 : 3,
            }
          }
        ],
      };
      if (title == '早班') {
        this.chart1.setOption(option);
      }
      if (title == '中班') {
        this.chart2.setOption(option);
      }
      if (title == '夜班') {
        this.chart3.setOption(option);
      }
    },
    resizeChart() {
      if (this.chart1) {
        this.chart1.resize();
      }
      if (this.chart2) {
        this.chart2.resize();
      }
      if (this.chart3) {
        this.chart3.resize();
      }
    },
  },
}
</script>
<style scoped>
.echartsG_box {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.title-bar {
  width: 100%;
  height: 20%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  padding: 8px 10px;
  justify-content: space-between;
}
.title-text {
  width: 200px;
  text-align: left;
  font-family: sourcehanRegular;
  color: black;
  font-size: 17px;
}
.echarts-container {
  width: 100%;
  height: 80%;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
}
.echartsGauge {
  width: 100%;
  height: 100%;
}
</style>