import request from '@/utils/request'

// 新增班制
export function addClassMode(mode) {
  return request({
    url: '/work/mode/saveOne',
    method: 'post',
    data: mode
  })
}

// 查询班制
export function listByName(query) {
  return request({
    url: '/work/mode/list',
    method: 'get',
    params: query
  })
}

// 查询班制详细
export function getWorkMode(id) {
  return request({
    url: '/work/mode/' + id,
    method: 'get'
  })
}

// 修改班制信息
export function updateWorkMode(data) {
  return request({
    url: '/work/mode/',
    method: 'put',
    data: data
  })
}

// 删除参数配置
export function delWorkMode(id) {
  return request({
    url: '/work/mode/' + id,
    method: 'delete'
  })
}

// 根据月份查询排班情况
export function getWorkModeByMonth(param) {
  return request({
    url: '/work/mode/monthWork',
    method: 'get',
    params: param
  })
}

// 新增班别
export function addWorkClass(data) {
  return request({
    url: '/work/class/add',
    method: 'post',
    data: data
  })
}

// 根据班制查询班别
export function listWorkClassByWorkModeId(id) {
  return request({
    url: '/work/class/list?workModeId=' + id,
    method: 'get'
  })
}

// 新增班次
export function addWorkShift(data) {
  return request({
    url: '/work/shift/add',
    method: 'post',
    data: data
  })
}

// 根据班制查询班次
export function listWorkShiftByWorkModeId(id) {
  return request({
    url: '/work/shift/list?workModeId=' + id,
    method: 'get'
  })
}

// 根据班制查询班次
export function queryByProdCenterCode(prodCenterCode) {
  return request({
    url: '/work/shift/queryByProdCenterCode/' + prodCenterCode,
    method: 'get'
  })
}

export function queryWorkClass(modelName) {
  return request({
    url: '/work/mode/queryWorkClass/' + modelName,
    method: 'get'
  })
}

export function queryWorkShift(modelName) {
  return request({
    url: '/work/mode/queryWorkShift/' + modelName,
    method: 'get'
  })
}

// 生成倒班规则
export function generateShiftRules(workModeId) {
  return request({
    url: '/work/shift/set/' + workModeId,
    method: 'post'
  })
}

// 根据班制查询所有倒班规则
export function queryWorkShiftSetByWorkModeId(workModeId) {
  return request({
    url: '/work/shift/set/' + workModeId,
    method: 'get'
  })
}

// 根据班制查询当前时间倒班规则
export function queryListByWorkMode(workModeId) {
  return request({
    url: '/work/classDatail/queryListByWorkMode?workModeId=' + workModeId,
    method: 'get'
  })
}

export function queryDetail(query) {
  return request({
    url: '/work/classDatail/queryDetail',
    method: 'get',
    params: query
  })
}

export function class_queryByWorkName(wn) {
  let query = { workName: wn }
  return request({
    url: '/work/shift/queryByWorkName',
    method: 'get',
    params: query
  })
}

export function shift_queryByWorkName(wn) {
  let query = { workName: wn }
  return request({
    url: '/work/class/queryByWorkName',
    method: 'get',
    params: query
  })
}

// 调班
export function shiftAdjustment(workModeId, workclass, workShift, date) {
  return request({
    url: '/work/mode/shiftAdjustment?workModeId=' + workModeId + '&workClass=' + workclass + '&workShift=' + workShift + '&date=' + date,
    method: 'post'
  })
}

// 查看
export function queryAllUsed() {
  return request({
    url: '/work/mode/queryAllUsed',
    method: 'get'
  })
}

// 根据班制查询班次
export function class_queryByProdCenterCode(prodCenterCode) {
  return request({
    url: '/work/class/queryByProdCenterCode/' + prodCenterCode,
    method: 'get'
  })
}

// 根据班制查询班次
export function workMode_queryByProdCenterCode(prodCenterCode) {
  return request({
    url: '/work/mode/queryByProdCenterCode/' + prodCenterCode,
    method: 'get'
  })
}

export function workShift_queryByNodeAndName(query) {
  return request({
    url: '/work/shift/queryByNodeAndName',
    params: query,
    method: 'get'
  })
}

