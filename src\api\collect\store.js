import request from '@/utils/request'

// 查询存储配置列表
export function listStore(query,selectVO) {
  return request({
    url: '/api/collect/store/list',
    method: 'get',
    params: query,
    selectVO: selectVO
  })
}

// 查询存储配置详细
export function getStore(storeId) {
  return request({
    url: '/api/collect/store/' + storeId,
    method: 'get'
  })
}

export function getAllList() {
  return request({
    url: '/api/collect/store/getAllList',
    method: 'get'
  })
}


// 新增存储配置
export function addStore(data) {
  return request({
    url: '/api/collect/store',
    method: 'post',
    data: data
  })
}

export function saveOrUpdate(data) {
  return request({
    url: '/api/collect/store/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 修改存储配置
export function updateStore(data) {
  return request({
    url: '/api/collect/store',
    method: 'put',
    data: data
  })
}

// 删除存储配置
export function delStore(storeId) {
  return request({
    url: '/api/collect/store/' + storeId,
    method: 'delete'
  })
}
