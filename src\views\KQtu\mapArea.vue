<template>
  <div class="material-warehouse-management">
    <!-- 标题栏 -->
    <div class="title-header">
      <h1 class="main-title">—原料仓储管理</h1>
      <div class="header-buttons">
        <el-button class="header-btn" @click="refreshData">数据刷新</el-button>
        <el-button class="header-btn" @click="exportData">进入编辑</el-button>

      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="container">
      <!-- 左侧库区图 -->
      <div class="warehouse-layout">
        <!-- 顶部刻度尺 -->
        <div class="ruler-top">
          <div class="ruler-marks">
            <span v-for="mark in topRulerMarks" :key="mark" class="ruler-mark">{{ mark }}</span>
          </div>
        </div>

        <!-- 库区行标识 -->
        <div class="warehouse-rows">
          <!-- E区 -->
          <div class="warehouse-row" data-row="E">
            <div class="row-label">E</div>
            <div class="row-content">
              <div
                class="plan-block"
                :class="{ dragging: activePlanBlock === 'E' }"
                :style="{ left: planBlocks.E.left + 'px', width: planBlocks.E.width + 'px' }"
                @mousedown="startDragPlan($event, 'E')"
              >
                <div class="plan-info">计划区域</div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResizePlan($event, 'E', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResizePlan($event, 'E', 'right')"></div>
                </div>
              </div>
              <div
                class="material-block mixed-material draggable"
                :style="{ left: materialBlocks.E1.left + 'px', width: materialBlocks.E1.width + 'px' }"
                @mousedown="startDrag($event, 'E1')"
                @touchstart="startDrag($event, 'E1')"
              >
                <div class="material-info">
                  <div class="material-name">混匀</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="material-details">
                  <div class="detail-row">
                    <span>内容</span>
                    <span>铁矿</span>
                    <span>50.1</span>
                  </div>
                  <div class="detail-row">
                    <span>品位</span>
                    <span>铁矿</span>
                    <span>62.1</span>
                  </div>
                  <div class="detail-row">
                    <span>一级料</span>
                    <span>50</span>
                    <span>T</span>
                  </div>
                  <div class="detail-row">
                    <span>二级料</span>
                    <span>50</span>
                    <span>T</span>
                  </div>
                  <div class="detail-row">
                    <span>三级料</span>
                    <span>50</span>
                    <span>T</span>
                  </div>
                </div>
                <!-- 拖拽手柄 -->
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'E1', 'left')" @touchstart.stop="startResize($event, 'E1', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'E1', 'right')" @touchstart.stop="startResize($event, 'E1', 'right')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- D区 -->
          <div class="warehouse-row" data-row="D">
            <div class="row-label">D</div>
            <div class="row-content">
              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.D1.left + 'px', width: materialBlocks.D1.width + 'px' }"
                @mousedown="startDrag($event, 'D1')"
                @touchstart="startDrag($event, 'D1')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="material-details blue-bg">
                  <div class="detail-item">品位</div>
                  <div class="detail-item">含水量</div>
                  <div class="detail-item">粒度</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'D1', 'left')" @touchstart.stop="startResize($event, 'D1', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'D1', 'right')" @touchstart.stop="startResize($event, 'D1', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block mixed-coal draggable"
                :style="{ left: materialBlocks.D2.left + 'px', width: materialBlocks.D2.width + 'px' }"
                @mousedown="startDrag($event, 'D2')"
                @touchstart="startDrag($event, 'D2')"
              >
                <div class="material-info">
                  <div class="material-name">混合煤</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'D2', 'left')" @touchstart.stop="startResize($event, 'D2', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'D2', 'right')" @touchstart.stop="startResize($event, 'D2', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.D3.left + 'px', width: materialBlocks.D3.width + 'px' }"
                @mousedown="startDrag($event, 'D3')"
                @touchstart="startDrag($event, 'D3')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'D3', 'left')" @touchstart.stop="startResize($event, 'D3', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'D3', 'right')" @touchstart.stop="startResize($event, 'D3', 'right')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- C区 -->
          <div class="warehouse-row" data-row="C">
            <div class="row-label">C</div>
            <div class="row-content">
              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.C1.left + 'px', width: materialBlocks.C1.width + 'px' }"
                @mousedown="startDrag($event, 'C1')"
                @touchstart="startDrag($event, 'C1')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'C1', 'left')" @touchstart.stop="startResize($event, 'C1', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'C1', 'right')" @touchstart.stop="startResize($event, 'C1', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block limestone draggable"
                :style="{ left: materialBlocks.C2.left + 'px', width: materialBlocks.C2.width + 'px' }"
                @mousedown="startDrag($event, 'C2')"
                @touchstart="startDrag($event, 'C2')"
              >
                <div class="material-info">
                  <div class="material-name">萤石</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'C2', 'left')" @touchstart.stop="startResize($event, 'C2', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'C2', 'right')" @touchstart.stop="startResize($event, 'C2', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.C3.left + 'px', width: materialBlocks.C3.width + 'px' }"
                @mousedown="startDrag($event, 'C3')"
                @touchstart="startDrag($event, 'C3')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'C3', 'left')" @touchstart.stop="startResize($event, 'C3', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'C3', 'right')" @touchstart.stop="startResize($event, 'C3', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.C4.left + 'px', width: materialBlocks.C4.width + 'px' }"
                @mousedown="startDrag($event, 'C4')"
                @touchstart="startDrag($event, 'C4')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'C4', 'left')" @touchstart.stop="startResize($event, 'C4', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'C4', 'right')" @touchstart.stop="startResize($event, 'C4', 'right')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- B区 -->
          <div class="warehouse-row" data-row="B">
            <div class="row-label">B</div>
            <div class="row-content">
              <div
                class="material-block pellet draggable"
                :style="{ left: materialBlocks.B1.left + 'px', width: materialBlocks.B1.width + 'px' }"
                @mousedown="startDrag($event, 'B1')"
                @touchstart="startDrag($event, 'B1')"
              >
                <div class="material-info">
                  <div class="material-name">印度球团62+</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'B1', 'left')" @touchstart.stop="startResize($event, 'B1', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'B1', 'right')" @touchstart.stop="startResize($event, 'B1', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block mixed-coal draggable"
                :style="{ left: materialBlocks.B2.left + 'px', width: materialBlocks.B2.width + 'px' }"
                @mousedown="startDrag($event, 'B2')"
                @touchstart="startDrag($event, 'B2')"
              >
                <div class="material-info">
                  <div class="material-name">混合煤</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'B2', 'left')" @touchstart.stop="startResize($event, 'B2', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'B2', 'right')" @touchstart.stop="startResize($event, 'B2', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block pellet draggable"
                :style="{ left: materialBlocks.B3.left + 'px', width: materialBlocks.B3.width + 'px' }"
                @mousedown="startDrag($event, 'B3')"
                @touchstart="startDrag($event, 'B3')"
              >
                <div class="material-info">
                  <div class="material-name">俄罗斯球团62+</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'B3', 'left')" @touchstart.stop="startResize($event, 'B3', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'B3', 'right')" @touchstart.stop="startResize($event, 'B3', 'right')"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- A区 -->
          <div class="warehouse-row" data-row="A">
            <div class="row-label">A</div>
            <div class="row-content">
              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.A1.left + 'px', width: materialBlocks.A1.width + 'px' }"
                @mousedown="startDrag($event, 'A1')"
                @touchstart="startDrag($event, 'A1')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'A1', 'left')" @touchstart.stop="startResize($event, 'A1', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'A1', 'right')" @touchstart.stop="startResize($event, 'A1', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block mixed-coal draggable"
                :style="{ left: materialBlocks.A2.left + 'px', width: materialBlocks.A2.width + 'px' }"
                @mousedown="startDrag($event, 'A2')"
                @touchstart="startDrag($event, 'A2')"
              >
                <div class="material-info">
                  <div class="material-name">混合煤</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'A2', 'left')" @touchstart.stop="startResize($event, 'A2', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'A2', 'right')" @touchstart.stop="startResize($event, 'A2', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block iron-ore draggable"
                :style="{ left: materialBlocks.A3.left + 'px', width: materialBlocks.A3.width + 'px' }"
                @mousedown="startDrag($event, 'A3')"
                @touchstart="startDrag($event, 'A3')"
              >
                <div class="material-info">
                  <div class="material-name">混匀矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'A3', 'left')" @touchstart.stop="startResize($event, 'A3', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'A3', 'right')" @touchstart.stop="startResize($event, 'A3', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block coke draggable"
                :style="{ left: materialBlocks.A4.left + 'px', width: materialBlocks.A4.width + 'px' }"
                @mousedown="startDrag($event, 'A4')"
                @touchstart="startDrag($event, 'A4')"
              >
                <div class="material-info">
                  <div class="material-name">氧化铁皮</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'A4', 'left')" @touchstart.stop="startResize($event, 'A4', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'A4', 'right')" @touchstart.stop="startResize($event, 'A4', 'right')"></div>
                </div>
              </div>

              <div
                class="material-block limestone draggable"
                :style="{ left: materialBlocks.A5.left + 'px', width: materialBlocks.A5.width + 'px' }"
                @mousedown="startDrag($event, 'A5')"
                @touchstart="startDrag($event, 'A5')"
              >
                <div class="material-info">
                  <div class="material-name">液地矿</div>
                  <div class="material-weight">495 T</div>
                </div>
                <div class="drag-handles">
                  <div class="drag-handle left" @mousedown.stop="startResize($event, 'A5', 'left')" @touchstart.stop="startResize($event, 'A5', 'left')"></div>
                  <div class="drag-handle right" @mousedown.stop="startResize($event, 'A5', 'right')" @touchstart.stop="startResize($event, 'A5', 'right')"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部刻度尺 -->
        <div class="ruler-bottom">
          <div class="ruler-marks">
            <span v-for="mark in bottomRulerMarks" :key="mark" class="ruler-mark">{{ mark }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧数据面板 -->
      <div class="data-panel">
        <div class="panel-row" v-for="(item, index) in panelData" :key="index">
          <div class="panel-label">{{ item.label }}</div>
          <div class="panel-inputs">
            <el-input v-model="item.value1" size="mini" />
            <el-input v-model="item.value2" size="mini" />
            <el-input v-model="item.value3" size="mini" />
            <span class="panel-unit">{{ item.unit }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 拖动时的刻度指示器 -->
    <div v-if="dragState.isDragging" class="scale-indicator" :style="{ left: calculateIndicatorPosition + 'px' }">
      <div class="indicator-line"></div>
      <div class="indicator-value">{{ calculateScaleValue }}</div>
    </div>

  </div>
</template>

<script>
export default {
  name: 'MaterialWarehouseManagement',
  data() {
    return {
      // 刻度尺数据
      topRulerMarks: ['700','650', '600', '550', '500', '450', '400', '350', '300', '250', '200', '150', '100', '50', '0'],
      bottomRulerMarks: ['600', '550', '500', '450', '400', '350', '300', '250', '200', '150', '100', '50', '0'],

      // 物料块位置和尺寸数据
      materialBlocks: {
        E1: { left: 50, width: 120, materialName: '混匀' },
        D1: { left: 50, width: 100, materialName: '混匀矿' },
        D2: { left: 200, width: 80 },
        D3: { left: 320, width: 100 },
        C1: { left: 50, width: 100 },
        C2: { left: 180, width: 80 },
        C3: { left: 290, width: 100 },
        C4: { left: 420, width: 100 },
        B1: { left: 80, width: 120 },
        B2: { left: 230, width: 100 },
        B3: { left: 360, width: 140 },
        A1: { left: 50, width: 100 },
        A2: { left: 180, width: 100 },
        A3: { left: 310, width: 100 },
        A4: { left: 440, width: 100 },
        A5: { left: 570, width: 100 }
      },

      // 拖拽状态
      dragState: {
        isDragging: false,
        isResizing: false,
        currentBlock: null,
        resizeDirection: null,
        startX: 0,
        startLeft: 0,
        startWidth: 0,
        containerRect: null,
        animationFrame: null,
        lastUpdateTime: 0,
        planLeft: 0,
        planWidth: 0
      },

      // 刻度指示器数据
      indicatorPosition: 0,
      scaleValue: '',

      // 右侧面板数据
      panelData: [
        { label: '焦粉', value1: '501', value2: '621', value3: '621', unit: 'T' },
        { label: '硅锰铁矿粉', value1: '501', value2: '621', value3: '621', unit: 'T' },
        { label: '南非球矿62+', value1: '501', value2: '621', value3: '621', unit: 'T' },
        { label: '萤石', value1: '501', value2: '621', value3: '621', unit: 'T' },
        { label: '澳大利亚SP10球矿', value1: '501', value2: '621', value3: '621', unit: 'T' }
      ],

      // 状态
      loading: false,
      lastUpdateTime: '',
      refreshTimer: null,

      planBlocks: {
        E: { left: 50, width: 200 },
        D: { left: 50, width: 400 },
        C: { left: 50, width: 500 },
        B: { left: 50, width: 400 },
        A: { left: 50, width: 600 }
      },
      activePlanBlock: null,
      isDraggingPlan: false,
      isResizingPlan: false,
      resizePlanSide: null
    }
  },
  computed: {
    // 计算指示器位置
    calculateIndicatorPosition() {
      if (!this.dragState.isDragging || !this.dragState.currentBlock) return 0;
      const block = this.materialBlocks[this.dragState.currentBlock];
      // 获取容器的边界信息
      const containerRect = this.dragState.containerRect;
      if (!containerRect) return 0;
      
      // 计算相对于视口的位置
      return containerRect.left + block.left + (block.width / 2);
    },
    // 计算刻度值
    calculateScaleValue() {
      if (!this.dragState.isDragging || !this.dragState.currentBlock) return '';
      const block = this.materialBlocks[this.dragState.currentBlock];
      
      // 获取容器的边界信息
      const containerRect = this.dragState.containerRect;
      if (!containerRect) return '';
      
      // 计算有效区域的宽度（减去左右边距）
      const effectiveWidth = containerRect.width - 60; // 减去左右边距
      const effectiveLeft = block.left - 40; // 减去左边距
      
      // 计算相对位置（0-800的范围）
      const maxScale = 800;
      const scale = Math.round(((effectiveWidth - effectiveLeft) / effectiveWidth) * maxScale);
      
      // 确保刻度值在有效范围内
      const finalScale = Math.max(0, Math.min(maxScale, scale));
      
      return `${finalScale}m`;
    }
  },
  created() {
    this.initData()
    this.startAutoRefresh()
  },
  beforeDestroy() {
    this.stopAutoRefresh()
    // 清理拖拽事件监听器
    this.cleanupDragListeners()
  },
  methods: {
    //  初始化和数据加载 

    /** 初始化数据 */
    async initData() {
      this.loading = true
      try {
        await this.loadMaterialData()
        this.updateLastUpdateTime()
      } catch (error) {
        console.error('初始化数据失败:', error)
        this.$message.error('数据加载失败')
      } finally {
        this.loading = false
      }
    },

    /** 加载物料数据 */
    async loadMaterialData() {
      try {
        // 这里可以调用实际的API
        // const response = await stockmapInfo()
      } catch (error) {
        console.error('加载数据失败:', error)
      }
    },


    updateLastUpdateTime() {
      this.lastUpdateTime = new Date().toLocaleString()
    },

    //  自动刷新 

    startAutoRefresh() {

      this.refreshTimer = setInterval(() => {
        this.refreshData(false) 
      }, 1000)
    },

    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },

    //  工具栏操作 

    /** 刷新数据 */
    async refreshData(showMessage = true) {
      try {
        await this.loadMaterialData()
        this.updateLastUpdateTime()
        if (showMessage) {
          this.$message.success('数据刷新成功')
        }
      } catch (error) {
        console.error('刷新数据失败:', error)
        if (showMessage) {
          this.$message.error('数据刷新失败')
        }
      }
    },

    /** 进入编辑 */
    exportData() {
      this.$message.success('进入编辑模式')
    },

    //  拖拽功能 

    /** 开始拖拽物料块 */
    startDrag(event, blockId) {
      event.preventDefault();
      
      // 获取行ID（如'E'、'D'等）和对应的计划块
      const rowId = blockId.charAt(0);
      const planBlock = this.planBlocks[rowId];
      
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const container = event.target.closest('.warehouse-layout');
      
      this.dragState = {
        isDragging: true,
        isResizing: false,
        currentBlock: blockId,
        resizeDirection: null,
        startX: clientX,
        startLeft: this.materialBlocks[blockId].left,
        startWidth: this.materialBlocks[blockId].width,
        containerRect: container.getBoundingClientRect(),
        // 添加计划块信息
        planLeft: planBlock.left,
        planWidth: planBlock.width
      };
      
      // 添加全局事件监听
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handleDrag, { passive: false });
      document.addEventListener('touchend', this.endDrag);
      
      // 添加拖拽样式
      event.target.classList.add('dragging');
      document.body.style.userSelect = 'none';
      document.body.style.cursor = 'grabbing';
    },
    
    /** 处理拖拽过程 */
    handleDrag(event) {
      event.preventDefault();
      
      if (!this.dragState.isDragging) return;
      
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const deltaX = clientX - this.dragState.startX;
      
      if (this.dragState.isResizing) {
        // 调整大小
        let newWidth = this.dragState.startWidth;
        let newLeft = this.dragState.startLeft;
        
        if (this.dragState.resizeDirection === 'left') {
          // 从左侧调整大小
          newWidth = this.dragState.startWidth - deltaX;
          newLeft = this.dragState.startLeft + deltaX;
          
          // 限制在计划块范围内
          if (newLeft < this.dragState.planLeft) {
            newLeft = this.dragState.planLeft;
            newWidth = this.dragState.startWidth + this.dragState.startLeft - this.dragState.planLeft;
          }
          
          // 限制最小宽度
          if (newWidth < 100) {
            newWidth = 100;
            newLeft = this.dragState.startLeft + this.dragState.startWidth - 100;
          }
          
          // 更新物料块位置和宽度
          this.materialBlocks[this.dragState.currentBlock].width = newWidth;
          this.materialBlocks[this.dragState.currentBlock].left = newLeft;
        } else {
          // 从右侧调整大小
          newWidth = this.dragState.startWidth + deltaX;
          
          // 限制在计划块范围内
          const maxWidth = this.dragState.planLeft + this.dragState.planWidth - this.dragState.startLeft;
          newWidth = Math.min(newWidth, maxWidth);
          
          // 限制最小宽度
          newWidth = Math.max(100, newWidth);
          
          // 只更新宽度
          this.materialBlocks[this.dragState.currentBlock].width = newWidth;
        }
      } else {
        // 拖动位置
        let newLeft = this.dragState.startLeft + deltaX;
        
        // 限制在计划块范围内
        const minLeft = this.dragState.planLeft;
        const maxLeft = this.dragState.planLeft + this.dragState.planWidth - this.materialBlocks[this.dragState.currentBlock].width;
        newLeft = Math.max(minLeft, Math.min(newLeft, maxLeft));
        
        // 更新位置
        this.materialBlocks[this.dragState.currentBlock].left = newLeft;
      }
    },
    
    /** 开始调整物料块大小 */
    startResize(event, blockId, direction) {
      event.preventDefault();
      event.stopPropagation();
      
      // 获取行ID和计划块信息
      const rowId = blockId.charAt(0);
      const planBlock = this.planBlocks[rowId];
      
      const clientX = event.touches ? event.touches[0].clientX : event.clientX;
      const container = event.target.closest('.warehouse-layout');
      
      this.dragState = {
        isDragging: true,
        isResizing: true,
        currentBlock: blockId,
        resizeDirection: direction,
        startX: clientX,
        startLeft: this.materialBlocks[blockId].left,
        startWidth: this.materialBlocks[blockId].width,
        containerRect: container.getBoundingClientRect(),
        // 添加计划块信息
        planLeft: planBlock.left,
        planWidth: planBlock.width
      };
      
      document.addEventListener('mousemove', this.handleDrag);
      document.addEventListener('mouseup', this.endDrag);
      document.addEventListener('touchmove', this.handleDrag, { passive: false });
      document.addEventListener('touchend', this.endDrag);
      
      event.target.classList.add('resizing');
      document.body.style.cursor = direction === 'left' ? 'w-resize' : 'e-resize';
    },

    /** 结束拖拽 */
    endDrag(event) {
      if (!this.dragState.isDragging) return

      // 清理动画帧
      if (this.dragState.animationFrame) {
        cancelAnimationFrame(this.dragState.animationFrame)
        this.dragState.animationFrame = null
      }

      // 移除事件监听
      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.endDrag)
      document.removeEventListener('touchmove', this.handleDrag)
      document.removeEventListener('touchend', this.endDrag)

      // 移除拖拽样式
      const draggingElement = document.querySelector('.dragging')
      if (draggingElement) {
        draggingElement.classList.remove('dragging')
      }

      document.body.classList.remove('dragging')
      document.body.style.userSelect = ''
      document.body.style.cursor = ''

      // 重置拖拽状态
      this.dragState.isDragging = false
      this.dragState.currentBlock = null

      // 保存位置变更
      this.saveMaterialPositions()
    },

    /** 保存物料位置和尺寸 */
    saveMaterialPositions() {
      // 这里可以调用API保存位置和尺寸数据
      this.updateLastUpdateTime()
    },

    /** 清理拖拽事件监听器 */
    cleanupDragListeners() {
      // 清理动画帧
      if (this.dragState.animationFrame) {
        cancelAnimationFrame(this.dragState.animationFrame)
        this.dragState.animationFrame = null
      }

      document.removeEventListener('mousemove', this.handleDrag)
      document.removeEventListener('mouseup', this.endDrag)
      document.removeEventListener('touchmove', this.handleDrag)
      document.removeEventListener('touchend', this.endDrag)
      document.removeEventListener('mousemove', this.handleResize)
      document.removeEventListener('mouseup', this.endResize)
      document.removeEventListener('touchmove', this.handleResize)
      document.removeEventListener('touchend', this.endResize)

      // 重置样式
      document.body.classList.remove('dragging', 'resizing')
      document.body.style.userSelect = ''
      document.body.style.cursor = ''

      // 重置拖拽状态
      this.dragState.isDragging = false
      this.dragState.isResizing = false
      this.dragState.currentBlock = null
    },

    // 开始拖拽计划块
    startDragPlan(event, blockId) {
      if (event.button !== 0) return;
      
      this.activePlanBlock = blockId;
      this.isDraggingPlan = true;
      
      const startX = event.clientX;
      const startLeft = this.planBlocks[blockId].left;
      
      const moveHandler = (e) => {
        if (!this.isDraggingPlan) return;
        
        const deltaX = e.clientX - startX;
        let newLeft = startLeft + deltaX;
        
        // 限制在容器范围内
        newLeft = Math.max(0, Math.min(newLeft, 600 - this.planBlocks[blockId].width));
        
        this.planBlocks[blockId].left = newLeft;
      };
      
      const upHandler = () => {
        this.isDraggingPlan = false;
        this.activePlanBlock = null;
        
        document.removeEventListener('mousemove', moveHandler);
        document.removeEventListener('mouseup', upHandler);
      };
      
      document.addEventListener('mousemove', moveHandler);
      document.addEventListener('mouseup', upHandler);
    },
    
    // 开始调整计划块大小
    startResizePlan(event, blockId, side) {
      event.stopPropagation();
      if (event.button !== 0) return;
      
      this.activePlanBlock = blockId;
      this.isResizingPlan = true;
      this.resizePlanSide = side;
      
      const startX = event.clientX;
      const startWidth = this.planBlocks[blockId].width;
      const startLeft = this.planBlocks[blockId].left;
      
      const moveHandler = (e) => {
        if (!this.isResizingPlan) return;
        
        const deltaX = e.clientX - startX;
        
        if (side === 'left') {
          let newLeft = startLeft + deltaX;
          let newWidth = startWidth - deltaX;
          
          // 限制最小宽度
          if (newWidth < 100) {
            newWidth = 100;
            newLeft = startLeft + startWidth - 100;
          }
          
          // 限制左边界
          newLeft = Math.max(0, newLeft);
          
          this.planBlocks[blockId].left = newLeft;
          this.planBlocks[blockId].width = newWidth;
        } else {
          let newWidth = startWidth + deltaX;
          
          // 限制最小宽度和右边界
          newWidth = Math.max(100, Math.min(newWidth, 600 - this.planBlocks[blockId].left));
          
          this.planBlocks[blockId].width = newWidth;
        }
      };
      
      const upHandler = () => {
        this.isResizingPlan = false;
        this.activePlanBlock = null;
        this.resizePlanSide = null;
        
        document.removeEventListener('mousemove', moveHandler);
        document.removeEventListener('mouseup', upHandler);
      };
      
      document.addEventListener('mousemove', moveHandler);
      document.addEventListener('mouseup', upHandler);
    }
  }
}
</script>

<style lang="scss" scoped>
.material-warehouse-management {
  min-height: 90vh;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

//  标题栏样式 
.title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: url('~@/assets/images/imageKQT/headerBg.png') center/cover, rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 2px solid #2196f3;
  box-shadow: 0 4px 20px rgba(33, 150, 243, 0.1);
  position: relative;

  // 添加遮罩层以确保文字可读性
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    z-index: 1;
  }

  // 确保内容在遮罩层之上
  .main-title,
  .header-buttons {
    position: relative;
    z-index: 2;
  }

  .main-title {
    font-size: 24px;
    font-weight: 600;
    color: #1976d2;
    margin: 0;
    text-shadow: 0 2px 4px rgba(25, 118, 210, 0.1);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  .header-buttons {
    display: flex;
    gap: 15px;
    margin-left: auto;

    .header-btn {
      padding: 8px 20px;
      background: linear-gradient(135deg, #42a5f5, #1e88e5);
      color: white;
      border: none;
      border-radius: 20px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 3px 10px rgba(30, 136, 229, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(30, 136, 229, 0.4);
        background: linear-gradient(135deg, #1e88e5, #1565c0);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

//  主容器样式 
.container {
  min-height: 60%;
  display: flex;
  padding: 20px;
  gap: 20px;
  flex: 1;
}

//  库区图样式 
.warehouse-layout {
  flex: 1;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 0 15px 15px 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

//  刻度尺样式 
.ruler-top,
.ruler-bottom {
  height: 30px;
  background: linear-gradient(90deg, #f5f5f5, #e8e8e8);
  border-bottom: 1px solid #ddd;
  position: relative;
  padding: 0;


  .ruler-marks {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0;
    margin: 0 20px;

    .ruler-mark {
      font-size: 11px;
      color: #666;
      font-weight: 500;
    }

  }

}

.ruler-bottom {
  border-bottom: none;
  border-top: 1px solid #ddd;
}

//  库区行样式 
.warehouse-rows {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 10px 0;
}

.warehouse-row {
  display: flex;
  align-items: center;
  height: 80px;
  margin: 5px 0;
  position: relative;

  .row-label {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #64B5F6;
    color: white;
    border-radius: 4px;
    margin: 0 10px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }

  .row-content {
    flex: 1;
    position: relative;
    height: 100%;
    display: flex;
    align-items: center;
    background: linear-gradient(90deg, #fafafa 0%, #f0f0f0 100%);
  }
}

//  物料块样式 
.material-block {
  position: absolute;
  height: 70px;
  top: 5px;
  z-index: 150;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: grab;
  transition: all 0.3s ease;
  border: 2px solid rgba(255, 255, 255, 0.8);
  overflow: visible;
  user-select: none;

  &.dragging {
    z-index: 160;
    transform: scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }

  &:hover {
    transform: scale(1.01);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.18);
  }

  .material-info {
    padding: 8px;
    text-align: center;
    pointer-events: none;

    .material-name {
      font-size: 12px;
      font-weight: bold;
      color: white;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      margin-bottom: 2px;
    }

    .material-weight {
      font-size: 14px;
      font-weight: bold;
      color: #ffeb3b;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }

  .material-details {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(33, 150, 243, 0.9);
    color: white;
    padding: 4px;
    font-size: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;

    &.blue-bg {
      background: rgba(33, 150, 243, 0.9);
    }

    .detail-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 1px;
      font-size: 9px;
    }

    .detail-item {
      margin-bottom: 2px;
      font-size: 9px;
    }
  }

  &:hover .material-details {
    opacity: 1;
  }

  // 不同物料类型的颜色
  &.iron-ore {
    background: linear-gradient(135deg, #81c784, #66bb6a);
  }

  &.mixed-coal {
    background: linear-gradient(135deg, #90a4ae, #78909c);
  }

  &.limestone {
    background: linear-gradient(135deg, #ffb74d, #ffa726);
  }

  &.coke {
    background: linear-gradient(135deg, #a1887f, #8d6e63);
  }

  &.pellet {
    background: linear-gradient(135deg, #f06292, #ec407a);
  }

  &.mixed-material {
    background: linear-gradient(135deg, #ba68c8, #ab47bc);
  }
}

.plan-block {
  position: absolute;
  height: 75px;
  display: flex;
  align-items: center;
  padding: 2px;
  background: rgba(240, 240, 240, 0.4);
  border: 2px dashed #999;
  border-radius: 8px;
  cursor: grab;
  transition: all 0.3s ease;
  user-select: none;
  z-index: 100;

  &.dragging {
    background: rgba(240, 240, 240, 0.6);
    border-color: #2196f3;
    z-index: 110;
  }

  .plan-info {
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 12px;
    color: #666;
    background: rgba(255, 255, 255, 0.9);
    padding: 2px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .drag-handles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .drag-handle {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 8px;
      background: rgba(153, 153, 153, 0.5);
      cursor: col-resize;
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: auto;

      &.left {
        left: -4px;
        border-radius: 4px 0 0 4px;
      }

      &.right {
        right: -4px;
        border-radius: 0 4px 4px 0;
      }

      &:hover {
        opacity: 1;
        background: rgba(153, 153, 153, 0.8);
        width: 10px;
      }
    }
  }

  &:hover .drag-handles .drag-handle {
    opacity: 0.6;
  }
}

//  右侧数据面板样式 
.data-panel {
  width: 300px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 20px;
  overflow-y: auto;

  .panel-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px;
    background: rgba(240, 248, 255, 0.8);
    border-radius: 8px;
    border-left: 4px solid #2196f3;

    .panel-label {
      width: 80px;
      font-size: 12px;
      color: #333;
      font-weight: 500;
    }

    .panel-inputs {
      flex: 1;
      display: flex;
      gap: 5px;
      align-items: center;

      .el-input {
        width: 50px;

        ::v-deep .el-input__inner {
          height: 24px;
          line-height: 24px;
          font-size: 11px;
          padding: 0 5px;
          border-radius: 4px;
          border: 1px solid #ddd;
          text-align: center;

          &:focus {
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
          }
        }
      }

      .panel-unit {
        font-size: 11px;
        color: #666;
        margin-left: 5px;
      }
    }
  }
}

//  响应式设计 
@media (max-width: 1200px) {
.container {
flex-direction: column;
   height: auto;
  }

  .data-panel {
    width: 100%;
    height: 200px;
  }
}

@media (max-width: 768px) {
  .title-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;

    .header-buttons {
      width: 100%;
      justify-content: center;
    }
  }

  .container {
    padding: 10px;
  }

  .warehouse-row {
    height: 60px;

    .material-block {
      height: 50px;
    }
  }
}

//  拖拽优化样式 
.warehouse-layout {
  // 防止拖拽时选中文本
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  // 优化触摸设备体验
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

// 拖拽时的全局样式
body.dragging {
  cursor: grabbing !important;
  user-select: none !important;

  * {
    cursor: grabbing !important;
  }
}

body.resizing {
  user-select: none !important;
}

//  滚动条样式 
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(240, 240, 240, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #64b5f6, #42a5f5);
  border-radius: 4px;

  &:hover {
    background: linear-gradient(135deg, #42a5f5, #1e88e5);
  }
}

//  性能优化 
.material-block {
  // 启用硬件加速
  transform: translateZ(0);
  will-change: transform, left, width;

  &.dragging {
    will-change: transform, left, width;
    // 使用transform而不是left来提高性能
    transform: translateZ(0);
  }
}

//  拖动时的刻度指示器样式 
.scale-indicator {
  position: fixed; // 改为fixed定位，确保始终可见
  top: 0;
  height: 100vh;
  width: 2px;
  pointer-events: none;
  z-index: 1000;

  .indicator-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(33, 150, 243, 0.5);
    box-shadow: 0 0 8px rgba(33, 150, 243, 0.3);
  }

  .indicator-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #2196f3;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    
    &::after {
      content: '';
      position: absolute;
      left: 50%;
      top: 100%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: #2196f3;
    }
  }
}
</style>
