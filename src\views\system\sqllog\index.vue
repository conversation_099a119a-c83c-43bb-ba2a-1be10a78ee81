<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="auto">
      <el-form-item label="开始时间">
        <el-date-picker v-model="dateRange" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期"
                        type="datetimerange"
        >
        </el-date-picker>
      </el-form-item>


      <el-form-item label="服务器IP" prop="serverIp">
        <el-select v-model="queryParams.serverIp" placeholder="请选择包含内容" style="width: 100%;" clearable>
          <el-option v-for="item in serverList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="服务器端口" prop="serverPort">
        <el-select v-model="queryParams.serverPort" placeholder="请选择包含内容" style="width: 100%;" clearable>
          <el-option v-for="item in portList" :key="item" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="执行用户" prop="runUser">
        <el-input
          v-model="queryParams.runUser"
          placeholder="请输入执行用户"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="连接ID" prop="connectionId">
        <el-input
          v-model="queryParams.connectionId"
          placeholder="请输入连接ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="线程ID" prop="threadId">
        <el-input
          v-model="queryParams.threadId"
          placeholder="请输入线程ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行SQL" prop="executeSql">
        <el-input
          v-model="queryParams.executeSql"
          placeholder="请输入执行SQL"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="执行SQL" prop="SQL参数">
        <el-input
          v-model="queryParams.executeSqlPar"
          placeholder="请输入执行SQL"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>


      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>


    <div class="tableInfo">
      <vxe-table
        ref="tableMainRef"
        :data="mainTable.tableData"
        :row-config="{isHover: true}"
        :column-config="{resizable: true}"
        border
        header-align="center"
        :height="tableHeight-55-400"
        stripe
        @cell-click="cellClickEvent"
      >
        <vxe-column field="logId" title="SQL日志ID" width="auto"></vxe-column>
        <vxe-column field="createTime" title="创建时间" width="auto"></vxe-column>
        <vxe-column field="serverIp" title="服务器IP" width="auto"></vxe-column>
        <vxe-column field="serverPort" title="服务器端口" width="auto"></vxe-column>
        <vxe-column field="runUser" title="执行用户" width="auto"></vxe-column>
        <vxe-column field="threadId" title="线程ID" width="auto"></vxe-column>
        <vxe-column field="connectionId" title="连接ID" width="auto"></vxe-column>

        <vxe-column field="executeSql" title="执行SQL" show-overflow="title" width="500"></vxe-column>
        <vxe-column field="executeSqlPar" title="SQL参数" show-overflow="title" width="500"></vxe-column>
        <vxe-column field="runStep" title="执行方法" show-overflow="title" width="500"></vxe-column>


      </vxe-table>
      <vxe-pager
        :current-page.sync="mainTable.pageConfig.pageNum"
        :page-size.sync="mainTable.pageConfig.pageSize"
        :total="mainTable.pageConfig.total"
        @page-change="mainTablePageChange"
      >
      </vxe-pager>
    </div>
    <el-tabs type="border-card">
      <el-tab-pane label="详细信息">
        <el-descriptions :column="7" size="medium" border>
          <el-descriptions-item>
            <template slot="label">
              SQL日志ID
            </template>

            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.logId }}
            </span>

          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              创建时间
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.createTime }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              服务器IP
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.serverIp }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              服务器端口
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.serverPort }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              执行用户
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.runUser }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              线程ID
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.threadId }}
            </span>
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              连接ID
            </template>
            <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.connectionId }}
            </span>
          </el-descriptions-item>

        </el-descriptions>

        <el-divider content-position="center">SQL</el-divider>
        <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.executeSql }}
          </span>
        <el-divider content-position="center">SQL参数</el-divider>
        <span v-if="currentSqlLog!=null">
             {{ currentSqlLog.executeSqlPar }}
          </span>
        <el-divider content-position="center">执行方法</el-divider>

        <span v-if="currentSqlLog!=null">
             <span v-for="item in JSON.parse( currentSqlLog.runStep)">
               {{ item }}<br>
            </span>
          </span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import dayjs from 'dayjs'

import { getServerIp, getServerPort, listLog } from '@/api/system/sqllog'

export default {
  name: 'SqlLog',
  data() {
    return {
      tableHeight: 300,
      currentSqlLog: {
        logId: null,
        executeSql: null,
        executeSqlPar: null,
        runStep: null,
        createTime: null,
        serverIp: null,
        serverPort: null,
        runUser: null,
        threadId: null,
        connectionId: null
      },
      dateRange: [],
      serverList: [],
      portList: [],
      mainTable: {
        loading: true,
        single: true,
        multiple: true,
        tableData: [],
        selectId: [],
        pageConfig: {
          pageNum: 1, // 页码
          pageSize: 20, // 每页显示条目个数
          total: 0, // 总数
          background: true, // 是否展示分页器背景色
          pageSizes: [10, 20, 50, 100]// 分页器分页待选项
        }
      },
      showSearch: true,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        threadId: null,
        connectionId: null,
        serverIp: null,
        serverPort: null,
        executeSql: null,
        executeSqlPar: null,
        runUser: null
      }
    }
  },
  created() {
    this.dateRange.push(dayjs(new Date()).startOf('date'))
    this.dateRange.push(dayjs(new Date()).endOf('date'))
    this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
    this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
    getServerIp().then(response => {
      this.serverList = response.data
    })
    getServerPort().then(response => {
      this.portList = response.data
    })
    this.queryList()
  },
  methods: {
    queryList() {
      if (this.dateRange.length == 2) {
        this.queryParams.dtStart = dayjs(this.dateRange[0]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
        this.queryParams.dtEnd = dayjs(this.dateRange[1]).format(
          'YYYY-MM-DD HH:mm:ss'
        )
      }
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      this.mainTable.tableData = []
      this.currentSqlLog = null
      listLog(this.queryParams).then(response => {
        this.mainTable.tableData = response.rows
        this.mainTable.pageConfig.total = response.total
        if (this.mainTable.tableData.length != 0) {
          this.currentSqlLog = this.mainTable.tableData[0]
        }
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.mainTable.pageConfig.pageNum = 1
      this.queryList()
    },
    mainTablePageChange({ pageSize, currentPage }) {
      this.mainTable.pageConfig.pageNum = currentPage
      this.mainTable.pageConfig.pageSize = pageSize
      this.queryParams.pageNum = this.mainTable.pageConfig.pageNum
      this.queryParams.pageSize = this.mainTable.pageConfig.pageSize
      this.queryList()
    },
    cellClickEvent({ row, column }) {
      this.currentSqlLog = row
    }

  },
  mounted() {
    this.$nextTick(() => {
      let topValue = document.getElementsByClassName('tableInfo')[0].getBoundingClientRect().top
      this.tableHeight = document.body.clientHeight - topValue - 10

    })
  }
}
</script>
